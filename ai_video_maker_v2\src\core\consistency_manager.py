# -*- coding: utf-8 -*-
"""
一致性管理器 - 角色和场景一致性控制

提供智能的一致性管理功能：
- 角色外观一致性
- 场景环境一致性
- 风格统一性
- 自动优化建议
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from collections import defaultdict
import json

from ..models.storyboard import Storyboard, Character, Scene, Shot
from ..models.project import Project

logger = logging.getLogger(__name__)

@dataclass
class ConsistencyRule:
    """一致性规则"""
    rule_id: str
    name: str
    description: str
    rule_type: str  # character, scene, style, technical
    severity: str   # error, warning, suggestion
    check_function: str
    auto_fix: bool = False

@dataclass
class ConsistencyIssue:
    """一致性问题"""
    issue_id: str
    rule_id: str
    severity: str
    title: str
    description: str
    affected_shots: List[str] = field(default_factory=list)
    affected_characters: List[str] = field(default_factory=list)
    affected_scenes: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    auto_fixable: bool = False

@dataclass
class ConsistencyReport:
    """一致性报告"""
    project_id: str
    storyboard_id: str
    total_issues: int
    error_count: int
    warning_count: int
    suggestion_count: int
    issues: List[ConsistencyIssue] = field(default_factory=list)
    character_consistency: Dict[str, float] = field(default_factory=dict)
    scene_consistency: Dict[str, float] = field(default_factory=dict)
    overall_score: float = 0.0

class ConsistencyManager:
    """一致性管理器"""
    
    def __init__(self):
        self.rules: Dict[str, ConsistencyRule] = {}
        self.character_keywords: Dict[str, List[str]] = {}
        self.scene_keywords: Dict[str, List[str]] = {}
        self.style_keywords: List[str] = []
        
        self._initialize_rules()
    
    def _initialize_rules(self):
        """初始化一致性规则"""
        # 角色一致性规则
        self._add_rule(ConsistencyRule(
            rule_id="char_appearance_consistency",
            name="角色外观一致性",
            description="检查角色在不同镜头中的外观描述是否一致",
            rule_type="character",
            severity="warning",
            check_function="check_character_appearance_consistency"
        ))
        
        self._add_rule(ConsistencyRule(
            rule_id="char_name_consistency",
            name="角色姓名一致性",
            description="检查角色姓名在对话和描述中是否一致",
            rule_type="character",
            severity="error",
            check_function="check_character_name_consistency"
        ))
        
        # 场景一致性规则
        self._add_rule(ConsistencyRule(
            rule_id="scene_environment_consistency",
            name="场景环境一致性",
            description="检查同一场景在不同镜头中的环境描述是否一致",
            rule_type="scene",
            severity="warning",
            check_function="check_scene_environment_consistency"
        ))
        
        self._add_rule(ConsistencyRule(
            rule_id="scene_lighting_consistency",
            name="场景光线一致性",
            description="检查同一场景的光线设置是否一致",
            rule_type="scene",
            severity="suggestion",
            check_function="check_scene_lighting_consistency"
        ))
        
        # 风格一致性规则
        self._add_rule(ConsistencyRule(
            rule_id="visual_style_consistency",
            name="视觉风格一致性",
            description="检查整体视觉风格是否统一",
            rule_type="style",
            severity="warning",
            check_function="check_visual_style_consistency"
        ))
        
        # 技术一致性规则
        self._add_rule(ConsistencyRule(
            rule_id="shot_duration_balance",
            name="镜头时长平衡",
            description="检查镜头时长分布是否合理",
            rule_type="technical",
            severity="suggestion",
            check_function="check_shot_duration_balance"
        ))
    
    def _add_rule(self, rule: ConsistencyRule):
        """添加一致性规则"""
        self.rules[rule.rule_id] = rule
    
    def analyze_consistency(self, storyboard: Storyboard, project: Project = None) -> ConsistencyReport:
        """分析分镜一致性"""
        try:
            logger.info(f"开始分析分镜一致性: {storyboard.metadata.storyboard_id}")
            
            # 创建报告
            report = ConsistencyReport(
                project_id=project.metadata.project_id if project else "",
                storyboard_id=storyboard.metadata.storyboard_id,
                total_issues=0,
                error_count=0,
                warning_count=0,
                suggestion_count=0
            )
            
            # 提取关键词
            self._extract_keywords(storyboard)
            
            # 执行所有规则检查
            for rule_id, rule in self.rules.items():
                try:
                    issues = self._execute_rule(rule, storyboard)
                    report.issues.extend(issues)
                    
                    # 统计问题数量
                    for issue in issues:
                        if issue.severity == "error":
                            report.error_count += 1
                        elif issue.severity == "warning":
                            report.warning_count += 1
                        elif issue.severity == "suggestion":
                            report.suggestion_count += 1
                    
                except Exception as e:
                    logger.error(f"规则 {rule_id} 执行失败: {e}")
            
            # 计算一致性分数
            report.character_consistency = self._calculate_character_consistency(storyboard)
            report.scene_consistency = self._calculate_scene_consistency(storyboard)
            report.overall_score = self._calculate_overall_score(report)
            report.total_issues = len(report.issues)
            
            logger.info(f"一致性分析完成，发现 {report.total_issues} 个问题")
            return report
            
        except Exception as e:
            logger.error(f"一致性分析失败: {e}")
            raise
    
    def _extract_keywords(self, storyboard: Storyboard):
        """提取关键词"""
        # 提取角色关键词
        for char_id, character in storyboard.characters.items():
            keywords = []
            
            # 从外观描述中提取关键词
            if character.appearance:
                keywords.extend(self._extract_appearance_keywords(character.appearance))
            
            # 从性格描述中提取关键词
            if character.personality:
                keywords.extend(self._extract_personality_keywords(character.personality))
            
            self.character_keywords[char_id] = keywords
        
        # 提取场景关键词
        for scene_id, scene in storyboard.scenes.items():
            keywords = []
            
            # 从环境描述中提取关键词
            if scene.environment:
                keywords.extend(self._extract_environment_keywords(scene.environment))
            
            # 从光线描述中提取关键词
            if scene.lighting:
                keywords.extend(self._extract_lighting_keywords(scene.lighting))
            
            self.scene_keywords[scene_id] = keywords
        
        # 提取风格关键词
        if storyboard.visual_style:
            self.style_keywords = self._extract_style_keywords(storyboard.visual_style)
    
    def _extract_appearance_keywords(self, text: str) -> List[str]:
        """提取外观关键词"""
        keywords = []
        
        # 年龄相关
        age_patterns = [r'年轻', r'中年', r'老年', r'少年', r'青年', r'\d+岁']
        for pattern in age_patterns:
            if re.search(pattern, text):
                keywords.append(re.search(pattern, text).group())
        
        # 性别相关
        gender_patterns = [r'男性', r'女性', r'男', r'女']
        for pattern in gender_patterns:
            if re.search(pattern, text):
                keywords.append(re.search(pattern, text).group())
        
        # 发色相关
        hair_patterns = [r'黑发', r'金发', r'棕发', r'白发', r'银发']
        for pattern in hair_patterns:
            if re.search(pattern, text):
                keywords.append(pattern)
        
        # 服装相关
        clothing_patterns = [r'西装', r'休闲装', r'运动装', r'连衣裙', r'T恤', r'衬衫']
        for pattern in clothing_patterns:
            if re.search(pattern, text):
                keywords.append(pattern)
        
        return keywords
    
    def _extract_personality_keywords(self, text: str) -> List[str]:
        """提取性格关键词"""
        personality_patterns = [
            r'开朗', r'内向', r'活泼', r'沉稳', r'幽默', r'严肃',
            r'温和', r'急躁', r'冷静', r'热情', r'理性', r'感性'
        ]
        
        keywords = []
        for pattern in personality_patterns:
            if re.search(pattern, text):
                keywords.append(pattern)
        
        return keywords
    
    def _extract_environment_keywords(self, text: str) -> List[str]:
        """提取环境关键词"""
        environment_patterns = [
            r'室内', r'室外', r'客厅', r'卧室', r'厨房', r'办公室',
            r'公园', r'街道', r'商店', r'学校', r'医院', r'餐厅'
        ]
        
        keywords = []
        for pattern in environment_patterns:
            if re.search(pattern, text):
                keywords.append(pattern)
        
        return keywords
    
    def _extract_lighting_keywords(self, text: str) -> List[str]:
        """提取光线关键词"""
        lighting_patterns = [
            r'自然光', r'人工光', r'柔和', r'强烈', r'昏暗', r'明亮',
            r'暖色调', r'冷色调', r'阳光', r'月光', r'灯光'
        ]
        
        keywords = []
        for pattern in lighting_patterns:
            if re.search(pattern, text):
                keywords.append(pattern)
        
        return keywords
    
    def _extract_style_keywords(self, text: str) -> List[str]:
        """提取风格关键词"""
        style_patterns = [
            r'现实主义', r'卡通', r'动漫', r'写实', r'抽象',
            r'复古', r'现代', r'未来', r'古典', r'简约'
        ]
        
        keywords = []
        for pattern in style_patterns:
            if re.search(pattern, text):
                keywords.append(pattern)
        
        return keywords
    
    def _execute_rule(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """执行一致性规则"""
        method_name = rule.check_function
        if hasattr(self, method_name):
            method = getattr(self, method_name)
            return method(rule, storyboard)
        else:
            logger.warning(f"规则检查方法不存在: {method_name}")
            return []
    
    def check_character_appearance_consistency(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """检查角色外观一致性"""
        issues = []
        
        for char_id, character in storyboard.characters.items():
            # 获取角色出现的所有镜头
            character_shots = [shot for shot in storyboard.shots if char_id in shot.characters]
            
            if len(character_shots) < 2:
                continue
            
            # 检查外观描述的一致性
            appearance_descriptions = []
            for shot in character_shots:
                if character.name in shot.visual_description:
                    appearance_descriptions.append(shot.visual_description)
            
            # 分析一致性
            if len(appearance_descriptions) > 1:
                consistency_score = self._calculate_text_similarity(appearance_descriptions)
                
                if consistency_score < 0.7:  # 一致性阈值
                    issue = ConsistencyIssue(
                        issue_id=f"char_appearance_{char_id}",
                        rule_id=rule.rule_id,
                        severity=rule.severity,
                        title=f"角色 {character.name} 外观描述不一致",
                        description=f"角色在不同镜头中的外观描述差异较大，一致性分数: {consistency_score:.2f}",
                        affected_characters=[char_id],
                        affected_shots=[shot.shot_id for shot in character_shots],
                        suggestions=[
                            f"统一角色 {character.name} 的外观描述关键词",
                            "在角色设定中明确外观特征",
                            "使用一致的描述用词"
                        ]
                    )
                    issues.append(issue)
        
        return issues
    
    def check_character_name_consistency(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """检查角色姓名一致性"""
        issues = []
        
        # 收集所有对话中出现的姓名
        dialogue_names = set()
        for shot in storyboard.shots:
            if shot.dialogue:
                # 简单的姓名提取（可以改进）
                names = re.findall(r'[A-Za-z\u4e00-\u9fff]{2,4}(?=说|道|问|答)', shot.dialogue)
                dialogue_names.update(names)
        
        # 检查是否有未定义的角色姓名
        defined_names = {char.name for char in storyboard.characters.values()}
        undefined_names = dialogue_names - defined_names
        
        if undefined_names:
            issue = ConsistencyIssue(
                issue_id="undefined_character_names",
                rule_id=rule.rule_id,
                severity=rule.severity,
                title="发现未定义的角色姓名",
                description=f"对话中出现了未在角色列表中定义的姓名: {', '.join(undefined_names)}",
                suggestions=[
                    "在角色列表中添加缺失的角色",
                    "检查姓名拼写是否正确",
                    "统一角色称呼方式"
                ]
            )
            issues.append(issue)
        
        return issues
    
    def check_scene_environment_consistency(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """检查场景环境一致性"""
        issues = []
        
        # 按场景分组镜头
        scene_shots = defaultdict(list)
        for shot in storyboard.shots:
            if shot.scene_id:
                scene_shots[shot.scene_id].append(shot)
        
        for scene_id, shots in scene_shots.items():
            if len(shots) < 2:
                continue
            
            scene = storyboard.scenes.get(scene_id)
            if not scene:
                continue
            
            # 检查环境描述的一致性
            environment_descriptions = [shot.visual_description for shot in shots if shot.visual_description]
            
            if len(environment_descriptions) > 1:
                consistency_score = self._calculate_text_similarity(environment_descriptions)
                
                if consistency_score < 0.6:
                    issue = ConsistencyIssue(
                        issue_id=f"scene_env_{scene_id}",
                        rule_id=rule.rule_id,
                        severity=rule.severity,
                        title=f"场景 {scene.title} 环境描述不一致",
                        description=f"同一场景在不同镜头中的环境描述差异较大，一致性分数: {consistency_score:.2f}",
                        affected_scenes=[scene_id],
                        affected_shots=[shot.shot_id for shot in shots],
                        suggestions=[
                            f"统一场景 {scene.title} 的环境描述",
                            "在场景设定中明确环境特征",
                            "保持场景描述的连贯性"
                        ]
                    )
                    issues.append(issue)
        
        return issues
    
    def check_scene_lighting_consistency(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """检查场景光线一致性"""
        issues = []
        
        # 检查同一场景的光线设置
        for scene_id, scene in storyboard.scenes.items():
            scene_shots = [shot for shot in storyboard.shots if shot.scene_id == scene_id]
            
            if len(scene_shots) < 2:
                continue
            
            # 提取光线关键词
            lighting_keywords = []
            for shot in scene_shots:
                if shot.visual_description:
                    keywords = self._extract_lighting_keywords(shot.visual_description)
                    lighting_keywords.extend(keywords)
            
            # 检查光线一致性
            if len(set(lighting_keywords)) > 2:  # 如果光线关键词过于多样
                issue = ConsistencyIssue(
                    issue_id=f"scene_lighting_{scene_id}",
                    rule_id=rule.rule_id,
                    severity=rule.severity,
                    title=f"场景 {scene.title} 光线设置不一致",
                    description=f"同一场景中出现了多种不同的光线设置: {', '.join(set(lighting_keywords))}",
                    affected_scenes=[scene_id],
                    affected_shots=[shot.shot_id for shot in scene_shots],
                    suggestions=[
                        "统一场景的光线设置",
                        "考虑时间推移对光线的影响",
                        "明确场景的主要光源"
                    ]
                )
                issues.append(issue)
        
        return issues
    
    def check_visual_style_consistency(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """检查视觉风格一致性"""
        issues = []
        
        # 收集所有图像提示词
        image_prompts = [shot.image_prompt for shot in storyboard.shots if shot.image_prompt]
        
        if len(image_prompts) < 2:
            return issues
        
        # 分析风格一致性
        style_consistency = self._analyze_style_consistency(image_prompts)
        
        if style_consistency < 0.7:
            issue = ConsistencyIssue(
                issue_id="visual_style_inconsistency",
                rule_id=rule.rule_id,
                severity=rule.severity,
                title="视觉风格不够统一",
                description=f"图像提示词中的风格描述不够一致，一致性分数: {style_consistency:.2f}",
                suggestions=[
                    "在所有图像提示词中使用统一的风格描述",
                    "明确项目的整体视觉风格",
                    "避免混合不同的艺术风格"
                ]
            )
            issues.append(issue)
        
        return issues
    
    def check_shot_duration_balance(self, rule: ConsistencyRule, storyboard: Storyboard) -> List[ConsistencyIssue]:
        """检查镜头时长平衡"""
        issues = []
        
        if len(storyboard.shots) < 3:
            return issues
        
        durations = [shot.duration for shot in storyboard.shots]
        avg_duration = sum(durations) / len(durations)
        
        # 检查是否有过长或过短的镜头
        long_shots = [shot for shot in storyboard.shots if shot.duration > avg_duration * 2]
        short_shots = [shot for shot in storyboard.shots if shot.duration < avg_duration * 0.5]
        
        if long_shots:
            issue = ConsistencyIssue(
                issue_id="long_shots_detected",
                rule_id=rule.rule_id,
                severity=rule.severity,
                title="发现过长的镜头",
                description=f"有 {len(long_shots)} 个镜头时长超过平均值的2倍",
                affected_shots=[shot.shot_id for shot in long_shots],
                suggestions=[
                    "考虑分割过长的镜头",
                    "检查镜头内容是否过于复杂",
                    "平衡整体节奏"
                ]
            )
            issues.append(issue)
        
        if short_shots:
            issue = ConsistencyIssue(
                issue_id="short_shots_detected",
                rule_id=rule.rule_id,
                severity=rule.severity,
                title="发现过短的镜头",
                description=f"有 {len(short_shots)} 个镜头时长少于平均值的一半",
                affected_shots=[shot.shot_id for shot in short_shots],
                suggestions=[
                    "考虑延长过短的镜头",
                    "合并相似的短镜头",
                    "确保镜头有足够的表现时间"
                ]
            )
            issues.append(issue)
        
        return issues
    
    def _calculate_text_similarity(self, texts: List[str]) -> float:
        """计算文本相似度"""
        if len(texts) < 2:
            return 1.0
        
        # 简单的关键词重叠度计算
        all_keywords = []
        for text in texts:
            keywords = re.findall(r'[\u4e00-\u9fff]+', text)  # 提取中文词汇
            all_keywords.append(set(keywords))
        
        # 计算平均重叠度
        total_similarity = 0
        comparisons = 0
        
        for i in range(len(all_keywords)):
            for j in range(i + 1, len(all_keywords)):
                intersection = len(all_keywords[i] & all_keywords[j])
                union = len(all_keywords[i] | all_keywords[j])
                similarity = intersection / union if union > 0 else 0
                total_similarity += similarity
                comparisons += 1
        
        return total_similarity / comparisons if comparisons > 0 else 0
    
    def _analyze_style_consistency(self, prompts: List[str]) -> float:
        """分析风格一致性"""
        # 提取风格关键词
        style_keywords_per_prompt = []
        for prompt in prompts:
            keywords = self._extract_style_keywords(prompt)
            style_keywords_per_prompt.append(set(keywords))
        
        if not style_keywords_per_prompt:
            return 1.0
        
        # 计算风格关键词的一致性
        common_keywords = set.intersection(*style_keywords_per_prompt) if style_keywords_per_prompt else set()
        all_keywords = set.union(*style_keywords_per_prompt) if style_keywords_per_prompt else set()
        
        consistency = len(common_keywords) / len(all_keywords) if all_keywords else 1.0
        return consistency
    
    def _calculate_character_consistency(self, storyboard: Storyboard) -> Dict[str, float]:
        """计算角色一致性分数"""
        consistency_scores = {}
        
        for char_id, character in storyboard.characters.items():
            # 获取角色出现的镜头
            character_shots = [shot for shot in storyboard.shots if char_id in shot.characters]
            
            if len(character_shots) < 2:
                consistency_scores[char_id] = 1.0
                continue
            
            # 计算外观描述一致性
            descriptions = []
            for shot in character_shots:
                if character.name in shot.visual_description:
                    descriptions.append(shot.visual_description)
            
            if descriptions:
                consistency_scores[char_id] = self._calculate_text_similarity(descriptions)
            else:
                consistency_scores[char_id] = 1.0
        
        return consistency_scores
    
    def _calculate_scene_consistency(self, storyboard: Storyboard) -> Dict[str, float]:
        """计算场景一致性分数"""
        consistency_scores = {}
        
        # 按场景分组镜头
        scene_shots = defaultdict(list)
        for shot in storyboard.shots:
            if shot.scene_id:
                scene_shots[shot.scene_id].append(shot)
        
        for scene_id, shots in scene_shots.items():
            if len(shots) < 2:
                consistency_scores[scene_id] = 1.0
                continue
            
            # 计算环境描述一致性
            descriptions = [shot.visual_description for shot in shots if shot.visual_description]
            
            if descriptions:
                consistency_scores[scene_id] = self._calculate_text_similarity(descriptions)
            else:
                consistency_scores[scene_id] = 1.0
        
        return consistency_scores
    
    def _calculate_overall_score(self, report: ConsistencyReport) -> float:
        """计算总体一致性分数"""
        if report.total_issues == 0:
            return 1.0
        
        # 根据问题严重程度计算扣分
        error_penalty = report.error_count * 0.3
        warning_penalty = report.warning_count * 0.2
        suggestion_penalty = report.suggestion_count * 0.1
        
        total_penalty = error_penalty + warning_penalty + suggestion_penalty
        max_penalty = len(report.issues) * 0.3  # 假设所有问题都是错误
        
        score = 1.0 - (total_penalty / max_penalty) if max_penalty > 0 else 1.0
        return max(0.0, min(1.0, score))
    
    def generate_optimization_suggestions(self, report: ConsistencyReport) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 根据问题类型生成建议
        character_issues = [issue for issue in report.issues if issue.rule_id.startswith("char_")]
        scene_issues = [issue for issue in report.issues if issue.rule_id.startswith("scene_")]
        style_issues = [issue for issue in report.issues if issue.rule_id.startswith("visual_")]
        
        if character_issues:
            suggestions.append("建议创建详细的角色设定文档，包括外观、性格、服装等关键特征")
            suggestions.append("在生成图像时使用统一的角色描述模板")
        
        if scene_issues:
            suggestions.append("为每个场景建立标准的环境描述，确保一致性")
            suggestions.append("考虑场景的时间变化对环境和光线的影响")
        
        if style_issues:
            suggestions.append("明确项目的整体视觉风格，并在所有提示词中保持一致")
            suggestions.append("创建风格指南，包括色彩、构图、艺术风格等要素")
        
        # 根据一致性分数生成建议
        low_consistency_characters = [
            char_id for char_id, score in report.character_consistency.items() 
            if score < 0.7
        ]
        
        if low_consistency_characters:
            suggestions.append(f"重点关注以下角色的一致性: {', '.join(low_consistency_characters)}")
        
        return suggestions
