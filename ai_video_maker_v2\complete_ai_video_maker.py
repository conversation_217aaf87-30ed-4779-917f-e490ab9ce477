#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 V2.0 - 完整专业版
真正可用的完整程序
"""

import sys
import os
import json
import asyncio
import aiohttp
from datetime import datetime
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QTabWidget, QMessageBox,
    QProgressBar, QFileDialog, QComboBox, QSpinBox, QListWidget,
    QFrame, QGroupBox, QLineEdit, QSplitter
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor

# 专业样式表
PROFESSIONAL_STYLE = """
QMainWindow {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f8f9fa, stop: 1 #e9ecef);
    font-family: 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
}

QTabWidget::pane {
    border: 2px solid #dee2e6;
    background-color: white;
    border-radius: 10px;
}

QTabBar::tab {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e9ecef, stop: 1 #ced4da);
    padding: 15px 25px;
    margin-right: 3px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-weight: bold;
    font-size: 12pt;
    min-width: 120px;
    color: #495057;
}

QTabBar::tab:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ffffff, stop: 1 #f8f9fa);
    border-bottom: 4px solid #007bff;
    color: #007bff;
    font-weight: bold;
}

QTabBar::tab:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f8f9fa, stop: 1 #e9ecef);
}

QPushButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #007bff, stop: 1 #0056b3);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 12pt;
    min-height: 25px;
}

QPushButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #0056b3, stop: 1 #004085);
    transform: translateY(-2px);
}

QPushButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #004085, stop: 1 #002752);
}

QPushButton:disabled {
    background: #6c757d;
    color: #adb5bd;
}

QTextEdit, QLineEdit {
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 12px;
    font-size: 12pt;
    background-color: white;
    selection-background-color: #007bff;
}

QTextEdit:focus, QLineEdit:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

QLabel {
    font-size: 12pt;
    color: #212529;
    font-weight: 500;
}

QGroupBox {
    font-weight: bold;
    font-size: 13pt;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    margin-top: 15px;
    padding-top: 15px;
    background-color: white;
    color: #495057;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 15px;
    padding: 0 10px 0 10px;
    color: #007bff;
    font-size: 14pt;
    font-weight: bold;
}

QProgressBar {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    text-align: center;
    font-weight: bold;
    height: 30px;
    font-size: 11pt;
}

QProgressBar::chunk {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #28a745, stop: 1 #20c997);
    border-radius: 6px;
}

QListWidget {
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background-color: white;
    alternate-background-color: #f8f9fa;
    font-size: 11pt;
}

QListWidget::item {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
}

QListWidget::item:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #cce7ff, stop: 1 #b3d9ff);
    color: #004085;
}

QComboBox {
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 10px;
    background-color: white;
    min-width: 120px;
    font-size: 11pt;
}

QComboBox:focus {
    border-color: #007bff;
}

QComboBox::drop-down {
    border: none;
    width: 25px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #495057;
    margin-right: 8px;
}

QSpinBox {
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 10px;
    background-color: white;
    min-width: 100px;
    font-size: 11pt;
}

QSpinBox:focus {
    border-color: #007bff;
}

QStatusBar {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #343a40, stop: 1 #212529);
    color: white;
    font-weight: bold;
    padding: 8px;
    font-size: 11pt;
}
"""

class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.api_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        self.session = None
    
    async def generate_text(self, prompt, model="glm-4"):
        """生成文本"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7,
                "max_tokens": 2000
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(
                f"{self.base_url}chat/completions",
                headers=headers,
                json=data,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    return f"API调用失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"
    
    async def generate_image(self, prompt, model="cogview-3"):
        """生成图像"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "prompt": prompt,
                "size": "1024x1024"
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(
                f"{self.base_url}images/generations",
                headers=headers,
                json=data,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["data"][0]["url"]
                else:
                    return f"图像生成失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"
    
    async def close(self):
        if self.session:
            await self.session.close()

class WorkerThread(QThread):
    """工作线程"""
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    progress = pyqtSignal(str)
    
    def __init__(self, ai_service, task_type, prompt):
        super().__init__()
        self.ai_service = ai_service
        self.task_type = task_type
        self.prompt = prompt
    
    def run(self):
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.task_type == "story":
                self.progress.emit("正在创作故事...")
                result = loop.run_until_complete(
                    self.ai_service.generate_text(
                        f"请创作一个引人入胜的故事，主题：{self.prompt}。"
                        f"要求：情节完整，人物生动，适合制作成视频。字数控制在500-800字。"
                    )
                )
            elif self.task_type == "storyboard":
                self.progress.emit("正在生成分镜脚本...")
                result = loop.run_until_complete(
                    self.ai_service.generate_text(
                        f"请将以下故事改写为详细的分镜脚本，包含场景描述、镜头角度、人物动作、对话内容等：\n\n{self.prompt}"
                    )
                )
            elif self.task_type == "image":
                self.progress.emit("正在生成图像...")
                result = loop.run_until_complete(
                    self.ai_service.generate_image(self.prompt)
                )
            else:
                result = loop.run_until_complete(
                    self.ai_service.generate_text(self.prompt)
                )
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class CompleteAIVideoMaker(QMainWindow):
    """完整的AI视频生成器"""

    def __init__(self):
        super().__init__()
        self.ai_service = AIService()
        self.worker = None
        self.current_project = {
            "name": "新项目",
            "story": "",
            "storyboard": "",
            "images": []
        }
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("AI视频生成器 V2.0 - 完整专业版")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)
        self.setStyleSheet(PROFESSIONAL_STYLE)

        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # 顶部标题栏
        header = self.create_header()
        main_layout.addWidget(header)

        # 主要内容区域
        self.tabs = QTabWidget()
        self.setup_tabs()
        main_layout.addWidget(self.tabs)

        # 状态栏
        self.setup_status_bar()

    def create_header(self):
        """创建顶部标题栏"""
        header = QFrame()
        header.setFrameStyle(QFrame.Shape.StyledPanel)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #007bff, stop: 1 #0056b3);
                border-radius: 12px;
                padding: 15px;
            }
        """)

        layout = QHBoxLayout(header)

        # 标题
        title = QLabel("🎬 AI视频生成器 V2.0")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: white; padding: 10px;")
        layout.addWidget(title)

        layout.addStretch()

        # 项目信息
        self.project_label = QLabel("📁 项目: 新项目")
        self.project_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        self.project_label.setStyleSheet("color: white; padding: 10px; background-color: rgba(255,255,255,0.2); border-radius: 6px;")
        layout.addWidget(self.project_label)

        # 操作按钮
        self.save_btn = QPushButton("💾 保存项目")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #28a745, stop: 1 #1e7e34);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #1e7e34, stop: 1 #155724);
            }
        """)
        self.save_btn.clicked.connect(self.save_project)
        layout.addWidget(self.save_btn)

        return header

    def setup_tabs(self):
        """设置标签页"""
        # 文本创作
        self.tabs.addTab(self.create_story_page(), "📝 文本创作")

        # 分镜设计
        self.tabs.addTab(self.create_storyboard_page(), "🎬 分镜设计")

        # 图像生成
        self.tabs.addTab(self.create_image_page(), "🎨 图像生成")

        # 项目总览
        self.tabs.addTab(self.create_overview_page(), "📊 项目总览")

        # 使用帮助
        self.tabs.addTab(self.create_help_page(), "❓ 使用帮助")

    def create_story_page(self):
        """创建故事页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("📝 AI故事创作")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #007bff;
                padding: 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e3f2fd, stop: 1 #bbdefb);
                border-radius: 10px;
                border: 2px solid #007bff;
            }
        """)
        layout.addWidget(title)

        # 输入区域
        input_group = QGroupBox("故事主题输入")
        input_layout = QVBoxLayout(input_group)

        instruction = QLabel("💡 请输入您想要创作的故事主题或关键词，AI将为您创作一个完整的故事")
        instruction.setStyleSheet("color: #6c757d; font-style: italic; padding: 10px;")
        input_layout.addWidget(instruction)

        self.story_input = QTextEdit()
        self.story_input.setPlaceholderText("例如：一个关于时间旅行的科幻故事，主角是一位年轻的科学家...")
        self.story_input.setMaximumHeight(120)
        input_layout.addWidget(self.story_input)

        # 控制按钮
        btn_layout = QHBoxLayout()

        self.generate_story_btn = QPushButton("🚀 生成故事")
        self.generate_story_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #007bff, stop: 1 #0056b3);")
        self.generate_story_btn.clicked.connect(self.generate_story)
        btn_layout.addWidget(self.generate_story_btn)

        self.copy_to_storyboard_btn = QPushButton("📋 复制到分镜")
        self.copy_to_storyboard_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #28a745, stop: 1 #1e7e34);")
        self.copy_to_storyboard_btn.clicked.connect(self.copy_to_storyboard)
        btn_layout.addWidget(self.copy_to_storyboard_btn)

        self.clear_story_btn = QPushButton("🗑️ 清空内容")
        self.clear_story_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #dc3545, stop: 1 #c82333);")
        self.clear_story_btn.clicked.connect(self.clear_story)
        btn_layout.addWidget(self.clear_story_btn)

        btn_layout.addStretch()
        input_layout.addLayout(btn_layout)

        layout.addWidget(input_group)

        # 进度条
        self.story_progress = QProgressBar()
        self.story_progress.setVisible(False)
        self.story_progress.setRange(0, 0)
        layout.addWidget(self.story_progress)

        # 输出区域
        output_group = QGroupBox("生成的故事")
        output_layout = QVBoxLayout(output_group)

        self.story_output = QTextEdit()
        self.story_output.setPlaceholderText("生成的故事将在这里显示...")
        self.story_output.setMinimumHeight(300)
        output_layout.addWidget(self.story_output)

        # 故事统计信息
        self.story_stats = QLabel("字数: 0 | 段落: 0")
        self.story_stats.setStyleSheet("color: #6c757d; font-size: 10pt; padding: 5px;")
        output_layout.addWidget(self.story_stats)

        layout.addWidget(output_group)

        return page

    def create_storyboard_page(self):
        """创建分镜页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("🎬 AI分镜设计")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #dc3545;
                padding: 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffebee, stop: 1 #ffcdd2);
                border-radius: 10px;
                border: 2px solid #dc3545;
            }
        """)
        layout.addWidget(title)

        # 输入区域
        input_group = QGroupBox("故事文本输入")
        input_layout = QVBoxLayout(input_group)

        instruction = QLabel("💡 请输入故事文本，AI将为您生成详细的分镜脚本")
        instruction.setStyleSheet("color: #6c757d; font-style: italic; padding: 10px;")
        input_layout.addWidget(instruction)

        self.storyboard_input = QTextEdit()
        self.storyboard_input.setPlaceholderText("请粘贴您的故事文本，或从文本创作页面复制...")
        self.storyboard_input.setMaximumHeight(150)
        input_layout.addWidget(self.storyboard_input)

        # 控制按钮
        btn_layout = QHBoxLayout()

        self.generate_storyboard_btn = QPushButton("🎬 生成分镜")
        self.generate_storyboard_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #dc3545, stop: 1 #c82333);")
        self.generate_storyboard_btn.clicked.connect(self.generate_storyboard)
        btn_layout.addWidget(self.generate_storyboard_btn)

        self.clear_storyboard_btn = QPushButton("🗑️ 清空内容")
        self.clear_storyboard_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #6c757d, stop: 1 #495057);")
        self.clear_storyboard_btn.clicked.connect(self.clear_storyboard)
        btn_layout.addWidget(self.clear_storyboard_btn)

        btn_layout.addStretch()
        input_layout.addLayout(btn_layout)

        layout.addWidget(input_group)

        # 进度条
        self.storyboard_progress = QProgressBar()
        self.storyboard_progress.setVisible(False)
        self.storyboard_progress.setRange(0, 0)
        layout.addWidget(self.storyboard_progress)

        # 输出区域
        output_group = QGroupBox("生成的分镜脚本")
        output_layout = QVBoxLayout(output_group)

        self.storyboard_output = QTextEdit()
        self.storyboard_output.setPlaceholderText("生成的分镜脚本将在这里显示...")
        self.storyboard_output.setMinimumHeight(350)
        output_layout.addWidget(self.storyboard_output)

        layout.addWidget(output_group)

        return page

    def create_image_page(self):
        """创建图像生成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("🎨 AI图像生成")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #9c27b0;
                padding: 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #f3e5f5, stop: 1 #e1bee7);
                border-radius: 10px;
                border: 2px solid #9c27b0;
            }
        """)
        layout.addWidget(title)

        # 输入区域
        input_group = QGroupBox("图像描述输入")
        input_layout = QVBoxLayout(input_group)

        instruction = QLabel("💡 请详细描述您想要生成的图像，描述越详细，生成效果越好")
        instruction.setStyleSheet("color: #6c757d; font-style: italic; padding: 10px;")
        input_layout.addWidget(instruction)

        self.image_input = QTextEdit()
        self.image_input.setPlaceholderText("例如：一个美丽的日落场景，远山如黛，湖水波光粼粼，天空中飞鸟成群...")
        self.image_input.setMaximumHeight(100)
        input_layout.addWidget(self.image_input)

        # 控制按钮
        btn_layout = QHBoxLayout()

        self.generate_image_btn = QPushButton("🎨 生成图像")
        self.generate_image_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #9c27b0, stop: 1 #7b1fa2);")
        self.generate_image_btn.clicked.connect(self.generate_image)
        btn_layout.addWidget(self.generate_image_btn)

        self.clear_image_btn = QPushButton("🗑️ 清空内容")
        self.clear_image_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #6c757d, stop: 1 #495057);")
        self.clear_image_btn.clicked.connect(self.clear_image)
        btn_layout.addWidget(self.clear_image_btn)

        btn_layout.addStretch()
        input_layout.addLayout(btn_layout)

        layout.addWidget(input_group)

        # 进度条
        self.image_progress = QProgressBar()
        self.image_progress.setVisible(False)
        self.image_progress.setRange(0, 0)
        layout.addWidget(self.image_progress)

        # 输出区域
        output_group = QGroupBox("生成的图像")
        output_layout = QVBoxLayout(output_group)

        self.image_output = QTextEdit()
        self.image_output.setPlaceholderText("生成的图像URL将在这里显示...")
        self.image_output.setMaximumHeight(200)
        output_layout.addWidget(self.image_output)

        # 图像历史列表
        self.image_list = QListWidget()
        self.image_list.setMaximumHeight(200)
        output_layout.addWidget(QLabel("图像生成历史:"))
        output_layout.addWidget(self.image_list)

        layout.addWidget(output_group)

        return page

    def create_overview_page(self):
        """创建项目总览页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 页面标题
        title = QLabel("📊 项目总览")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #28a745;
                padding: 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e8f5e8, stop: 1 #c8e6c9);
                border-radius: 10px;
                border: 2px solid #28a745;
            }
        """)
        layout.addWidget(title)

        # 项目统计
        stats_group = QGroupBox("项目统计")
        stats_layout = QVBoxLayout(stats_group)

        self.project_stats = QLabel()
        self.project_stats.setStyleSheet("font-size: 12pt; padding: 15px; line-height: 1.6;")
        stats_layout.addWidget(self.project_stats)

        layout.addWidget(stats_group)

        # 快速操作
        actions_group = QGroupBox("快速操作")
        actions_layout = QHBoxLayout(actions_group)

        export_btn = QPushButton("📤 导出项目")
        export_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #17a2b8, stop: 1 #138496);")
        export_btn.clicked.connect(self.export_project)
        actions_layout.addWidget(export_btn)

        refresh_btn = QPushButton("🔄 刷新统计")
        refresh_btn.setStyleSheet("background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ffc107, stop: 1 #e0a800);")
        refresh_btn.clicked.connect(self.update_project_stats)
        actions_layout.addWidget(refresh_btn)

        actions_layout.addStretch()

        layout.addWidget(actions_group)
        layout.addStretch()

        return page

    def create_help_page(self):
        """创建帮助页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(25, 25, 25, 25)

        # 页面标题
        title = QLabel("❓ 使用帮助")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #6f42c1;
                padding: 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #f4f1ff, stop: 1 #e6d9ff);
                border-radius: 10px;
                border: 2px solid #6f42c1;
            }
        """)
        layout.addWidget(title)

        # 帮助内容
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h2>🎬 AI视频生成器使用指南</h2>

        <h3>📝 文本创作</h3>
        <p>1. 在输入框中输入故事主题或关键词</p>
        <p>2. 点击"生成故事"按钮，AI将创作完整故事</p>
        <p>3. 可以点击"复制到分镜"将故事传递到下一步</p>

        <h3>🎬 分镜设计</h3>
        <p>1. 输入或粘贴故事文本</p>
        <p>2. 点击"生成分镜"按钮，AI将生成详细分镜脚本</p>
        <p>3. 分镜脚本包含场景描述、镜头角度、人物动作等</p>

        <h3>🎨 图像生成</h3>
        <p>1. 输入详细的图像描述</p>
        <p>2. 点击"生成图像"按钮，AI将生成图像</p>
        <p>3. 生成的图像URL可以在浏览器中查看</p>

        <h3>📊 项目管理</h3>
        <p>• 所有内容自动保存到当前项目</p>
        <p>• 可以在项目总览中查看统计信息</p>
        <p>• 支持导出项目数据</p>

        <h3>💡 使用技巧</h3>
        <p>• 描述越详细，AI生成效果越好</p>
        <p>• 建议按顺序使用：故事 → 分镜 → 图像</p>
        <p>• 可以多次生成，选择最满意的结果</p>

        <h3>⚙️ 技术信息</h3>
        <p>• 使用智谱AI GLM-4模型进行文本生成</p>
        <p>• 使用CogView-3模型进行图像生成</p>
        <p>• 所有功能已配置API密钥，可直接使用</p>
        """)
        help_text.setStyleSheet("font-size: 11pt; line-height: 1.6; padding: 15px;")
        layout.addWidget(help_text)

        return page

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪 - AI视频生成器已启动")

        # API状态
        self.api_status = QLabel("🟢 API已连接")
        self.api_status.setStyleSheet("color: #28a745; font-weight: bold; padding: 0 15px;")
        self.status_bar.addPermanentWidget(self.api_status)

        # 时间显示
        self.time_label = QLabel()
        self.update_time()
        self.status_bar.addPermanentWidget(self.time_label)

        # 定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"🕒 {current_time}")
        self.time_label.setStyleSheet("color: #6c757d; font-weight: bold; padding: 0 15px;")

    # ==================== 功能方法 ====================

    def generate_story(self):
        """生成故事"""
        prompt = self.story_input.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请输入故事主题或关键词")
            return

        self.set_story_buttons_enabled(False)
        self.story_progress.setVisible(True)
        self.status_bar.showMessage("正在生成故事...")

        self.worker = WorkerThread(self.ai_service, "story", prompt)
        self.worker.finished.connect(self.on_story_finished)
        self.worker.error.connect(self.on_error)
        self.worker.progress.connect(self.status_bar.showMessage)
        self.worker.start()

    def on_story_finished(self, result):
        """故事生成完成"""
        self.story_output.setPlainText(result)
        self.current_project["story"] = result
        self.update_story_stats()
        self.set_story_buttons_enabled(True)
        self.story_progress.setVisible(False)
        self.status_bar.showMessage("故事生成完成！")
        QMessageBox.information(self, "成功", "故事生成完成！您可以复制到分镜设计页面继续制作。")

    def copy_to_storyboard(self):
        """复制故事到分镜页面"""
        story = self.story_output.toPlainText()
        if not story:
            QMessageBox.warning(self, "警告", "没有故事内容可复制")
            return

        self.storyboard_input.setPlainText(story)
        self.tabs.setCurrentIndex(1)  # 切换到分镜页面
        QMessageBox.information(self, "成功", "故事已复制到分镜设计页面！")

    def clear_story(self):
        """清空故事内容"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有故事内容吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.story_input.clear()
            self.story_output.clear()
            self.current_project["story"] = ""
            self.update_story_stats()

    def generate_storyboard(self):
        """生成分镜"""
        story = self.storyboard_input.toPlainText().strip()
        if not story:
            QMessageBox.warning(self, "警告", "请输入故事文本")
            return

        self.set_storyboard_buttons_enabled(False)
        self.storyboard_progress.setVisible(True)
        self.status_bar.showMessage("正在生成分镜脚本...")

        self.worker = WorkerThread(self.ai_service, "storyboard", story)
        self.worker.finished.connect(self.on_storyboard_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()

    def on_storyboard_finished(self, result):
        """分镜生成完成"""
        self.storyboard_output.setPlainText(result)
        self.current_project["storyboard"] = result
        self.set_storyboard_buttons_enabled(True)
        self.storyboard_progress.setVisible(False)
        self.status_bar.showMessage("分镜脚本生成完成！")
        QMessageBox.information(self, "成功", "分镜脚本生成完成！")

    def clear_storyboard(self):
        """清空分镜内容"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有分镜内容吗？")
        if reply == QMessageBox.StandardButton.Yes:
            self.storyboard_input.clear()
            self.storyboard_output.clear()
            self.current_project["storyboard"] = ""

    def generate_image(self):
        """生成图像"""
        prompt = self.image_input.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请输入图像描述")
            return

        self.set_image_buttons_enabled(False)
        self.image_progress.setVisible(True)
        self.status_bar.showMessage("正在生成图像...")

        self.worker = WorkerThread(self.ai_service, "image", prompt)
        self.worker.finished.connect(self.on_image_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()

    def on_image_finished(self, result):
        """图像生成完成"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 显示结果
        self.image_output.append(f"[{current_time}] 生成完成")
        self.image_output.append(f"图像URL: {result}")
        self.image_output.append("-" * 50)

        # 添加到历史列表
        self.image_list.addItem(f"[{current_time}] {result}")

        # 保存到项目
        self.current_project["images"].append({
            "url": result,
            "prompt": self.image_input.toPlainText(),
            "timestamp": current_time
        })

        self.set_image_buttons_enabled(True)
        self.image_progress.setVisible(False)
        self.status_bar.showMessage("图像生成完成！")

        if result.startswith("http"):
            QMessageBox.information(self, "成功", f"图像生成完成！\n\nURL: {result}\n\n您可以复制URL到浏览器查看图像。")
        else:
            QMessageBox.warning(self, "注意", f"生成结果: {result}")

    def clear_image(self):
        """清空图像内容"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有图像内容吗？")
        if reply == QMessageBox.StandardButton.Yes:
            self.image_input.clear()
            self.image_output.clear()
            self.image_list.clear()
            self.current_project["images"] = []

    def save_project(self):
        """保存项目"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存项目", f"{self.current_project['name']}.json",
                "JSON文件 (*.json)"
            )
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_project, f, ensure_ascii=False, indent=2)
                QMessageBox.information(self, "成功", f"项目已保存到: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")

    def export_project(self):
        """导出项目"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出项目", f"{self.current_project['name']}_export.txt",
                "文本文件 (*.txt)"
            )
            if file_path:
                content = f"""
AI视频生成器项目导出
项目名称: {self.current_project['name']}
导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

=== 故事内容 ===
{self.current_project.get('story', '暂无内容')}

=== 分镜脚本 ===
{self.current_project.get('storyboard', '暂无内容')}

=== 生成图像 ===
"""
                for i, img in enumerate(self.current_project.get('images', []), 1):
                    content += f"{i}. {img.get('url', '')}\n"

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content.strip())
                QMessageBox.information(self, "成功", f"项目已导出到: {file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def update_project_stats(self):
        """更新项目统计"""
        story_len = len(self.current_project.get("story", ""))
        storyboard_len = len(self.current_project.get("storyboard", ""))
        image_count = len(self.current_project.get("images", []))

        stats_text = f"""
📊 项目统计信息

📝 故事文本: {story_len} 字符
🎬 分镜脚本: {storyboard_len} 字符
🎨 生成图像: {image_count} 张
📅 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 项目进度:
{'✅' if story_len > 0 else '⭕'} 故事创作
{'✅' if storyboard_len > 0 else '⭕'} 分镜设计
{'✅' if image_count > 0 else '⭕'} 图像生成
        """.strip()

        self.project_stats.setText(stats_text)

    def update_story_stats(self):
        """更新故事统计"""
        text = self.story_output.toPlainText()
        char_count = len(text)
        paragraph_count = len([p for p in text.split('\n') if p.strip()])
        self.story_stats.setText(f"字数: {char_count} | 段落: {paragraph_count}")

    def set_story_buttons_enabled(self, enabled):
        """设置故事按钮状态"""
        self.generate_story_btn.setEnabled(enabled)
        self.copy_to_storyboard_btn.setEnabled(enabled)
        self.clear_story_btn.setEnabled(enabled)

    def set_storyboard_buttons_enabled(self, enabled):
        """设置分镜按钮状态"""
        self.generate_storyboard_btn.setEnabled(enabled)
        self.clear_storyboard_btn.setEnabled(enabled)

    def set_image_buttons_enabled(self, enabled):
        """设置图像按钮状态"""
        self.generate_image_btn.setEnabled(enabled)
        self.clear_image_btn.setEnabled(enabled)

    def on_error(self, error_msg):
        """处理错误"""
        QMessageBox.critical(self, "错误", f"操作失败: {error_msg}")

        # 重置所有按钮状态
        self.set_story_buttons_enabled(True)
        self.set_storyboard_buttons_enabled(True)
        self.set_image_buttons_enabled(True)

        # 隐藏所有进度条
        self.story_progress.setVisible(False)
        self.storyboard_progress.setVisible(False)
        self.image_progress.setVisible(False)

        self.status_bar.showMessage("操作失败")

    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(self, "确认退出", "确定要退出AI视频生成器吗？")
        if reply == QMessageBox.StandardButton.Yes:
            # 关闭AI服务
            if self.ai_service.session:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.ai_service.close())
            event.accept()
        else:
            event.ignore()

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("AI视频生成器")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("AI Video Maker")

    # 创建并显示主窗口
    window = CompleteAIVideoMaker()
    window.show()

    # 初始化项目统计
    window.update_project_stats()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
