{"environment": "development", "debug": true, "log_level": "INFO", "data_dir": "data", "cache_dir": "cache", "ui": {"theme": "light", "language": "zh_CN", "window_width": 1400, "window_height": 900, "auto_save_interval": 300}, "performance": {"max_concurrent_tasks": 3, "cache_size_mb": 512, "enable_gpu": true, "memory_limit_mb": 2048}, "llm_apis": {"zhipu": {"provider": "<PERSON><PERSON><PERSON>", "api_key": "your_zhipu_api_key_here", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "timeout": 30, "max_retries": 3}, "openai": {"provider": "openai", "api_key": "your_openai_api_key_here", "base_url": "https://api.openai.com/v1/", "timeout": 30, "max_retries": 3}, "deepseek": {"provider": "deepseek", "api_key": "your_deepseek_api_key_here", "base_url": "https://api.deepseek.com/v1/", "timeout": 30, "max_retries": 3}}, "image_apis": {"cogview": {"provider": "cogview", "api_key": "your_cogview_api_key_here", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "timeout": 60, "max_retries": 3}, "dalle": {"provider": "dalle", "api_key": "your_openai_api_key_here", "base_url": "https://api.openai.com/v1/", "timeout": 60, "max_retries": 3}, "pollinations": {"provider": "pollinations", "api_key": "", "base_url": "https://image.pollinations.ai/", "timeout": 60, "max_retries": 3}}, "voice_apis": {"azure": {"provider": "azure", "api_key": "your_azure_speech_key_here", "base_url": "https://your-region.tts.speech.microsoft.com/", "timeout": 30, "max_retries": 3}, "elevenlabs": {"provider": "elevenlabs", "api_key": "your_elevenlabs_api_key_here", "base_url": "https://api.elevenlabs.io/v1/", "timeout": 30, "max_retries": 3}, "openai_tts": {"provider": "openai_tts", "api_key": "your_openai_api_key_here", "base_url": "https://api.openai.com/v1/", "timeout": 30, "max_retries": 3}}, "video_apis": {"cogvideo": {"provider": "cogvideo", "api_key": "your_cogvideo_api_key_here", "base_url": "https://open.bigmodel.cn/api/paas/v4/", "timeout": 120, "max_retries": 3}, "runway": {"provider": "runway", "api_key": "your_runway_api_key_here", "base_url": "https://api.runwayml.com/v1/", "timeout": 120, "max_retries": 3}, "pika": {"provider": "pika", "api_key": "your_pika_api_key_here", "base_url": "https://api.pika.art/v1/", "timeout": 120, "max_retries": 3}}, "default_settings": {"storyboard": {"default_style": "电影风格", "max_shots_per_scene": 10, "default_shot_duration": 3.0, "enable_optimization": true}, "image_generation": {"default_provider": "cogview", "default_size": "1024x1024", "default_quality": "high", "concurrent_tasks": 3, "enable_upscaling": false}, "voice_generation": {"default_provider": "azure", "default_voice": "zh-CN-XiaoxiaoNeural", "default_speed": 1.0, "default_pitch": 1.0, "enable_ssml": true}, "video_generation": {"default_provider": "cogvideo", "default_resolution": "1920x1080", "default_fps": 30, "default_duration": 5.0, "enable_transitions": true}}, "advanced": {"enable_caching": true, "cache_expiry_hours": 24, "enable_auto_backup": true, "backup_interval_minutes": 30, "max_backup_files": 10, "enable_telemetry": false, "enable_crash_reporting": true}}