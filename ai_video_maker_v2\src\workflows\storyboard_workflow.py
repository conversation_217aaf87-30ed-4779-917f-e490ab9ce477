# -*- coding: utf-8 -*-
"""
分镜生成工作流 - 五阶段智能分镜生成

实现现代化的五阶段分镜生成工作流：
1. 世界观构建
2. 角色场景分析
3. 情节结构分析
4. 分镜脚本生成
5. 优化和完善
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
import json
import re

from ..core.workflow_manager import Workflow, WorkflowStep
from ..core.service_manager import ServiceType
from ..models.storyboard import Storyboard, StoryboardMetadata, Character, Scene, Shot, ShotType, CameraMovement
from ..models.workflow import WorkflowData, StageData, TaskData

logger = logging.getLogger(__name__)

class StoryboardProcessor:
    """分镜处理器"""
    
    def __init__(self, service_manager):
        self.service_manager = service_manager
        
    async def process_stage_1_world_building(self, text: str, style: str = "电影风格") -> Dict[str, Any]:
        """阶段1：世界观构建"""
        try:
            prompt = f"""
            你是一位专业的故事分析师和世界观设计师。请分析以下文本，构建详细的世界观设定。

            原始文本：
            {text}

            风格要求：{style}

            请按照以下格式输出世界观分析：

            ## 世界观设定
            ### 时代背景
            [详细描述故事发生的时代背景]

            ### 地理环境
            [描述故事发生的地理位置和环境特征]

            ### 社会文化
            [描述社会制度、文化背景、价值观念等]

            ### 技术水平
            [描述科技发展水平、工具器械等]

            ## 视觉风格
            ### 整体色调
            [描述主要色彩搭配和视觉氛围]

            ### 美术风格
            [描述绘画风格、构图特点等]

            ### 服装道具
            [描述典型的服装、道具、建筑风格]

            ## 叙事基调
            ### 情感氛围
            [描述整体的情感基调和氛围营造]

            ### 节奏特点
            [描述叙事节奏的特点]

            请确保分析详细、准确，为后续的角色和场景设计提供坚实基础。
            """
            
            result = await self.service_manager.call_service(
                ServiceType.LLM,
                "generate_text",
                prompt=prompt,
                max_tokens=2000,
                temperature=0.7
            )
            
            if not result.success:
                raise Exception(f"世界观构建失败: {result.error}")
            
            world_setting = result.data.get('text', '')
            
            return {
                'world_setting': world_setting,
                'visual_style': style,
                'analysis_complete': True
            }
            
        except Exception as e:
            logger.error(f"阶段1处理失败: {e}")
            raise
    
    async def process_stage_2_character_scene_analysis(self, text: str, world_setting: str) -> Dict[str, Any]:
        """阶段2：角色场景分析"""
        try:
            # 角色分析
            character_prompt = f"""
            基于以下世界观设定和原始文本，提取和分析所有重要角色。

            世界观设定：
            {world_setting}

            原始文本：
            {text}

            请按照以下JSON格式输出角色分析：
            {{
                "characters": [
                    {{
                        "name": "角色姓名",
                        "description": "角色简介",
                        "appearance": "外貌描述",
                        "personality": "性格特点",
                        "role": "在故事中的作用",
                        "relationships": ["与其他角色的关系"],
                        "voice_style": "说话风格和语调"
                    }}
                ]
            }}

            请确保：
            1. 提取所有在故事中有台词或重要作用的角色
            2. 外貌描述要详细具体，便于后续图像生成
            3. 性格特点要鲜明，体现角色个性
            4. 关系描述要清晰，便于理解角色互动
            """
            
            char_result = await self.service_manager.call_service(
                ServiceType.LLM,
                "generate_text",
                prompt=character_prompt,
                max_tokens=1500,
                temperature=0.6
            )
            
            if not char_result.success:
                raise Exception(f"角色分析失败: {char_result.error}")
            
            # 场景分析
            scene_prompt = f"""
            基于以下世界观设定和原始文本，分析所有重要场景。

            世界观设定：
            {world_setting}

            原始文本：
            {text}

            请按照以下JSON格式输出场景分析：
            {{
                "scenes": [
                    {{
                        "title": "场景名称",
                        "location": "具体地点",
                        "time_of_day": "时间（如：清晨、黄昏等）",
                        "weather": "天气状况",
                        "environment": "环境描述",
                        "mood": "场景氛围",
                        "lighting": "光线特点",
                        "key_elements": ["场景中的关键元素"]
                    }}
                ]
            }}

            请确保：
            1. 识别所有不同的场景位置
            2. 环境描述要详细，便于后续图像生成
            3. 氛围描述要准确，体现情节需要
            4. 光线描述要专业，便于视觉呈现
            """
            
            scene_result = await self.service_manager.call_service(
                ServiceType.LLM,
                "generate_text",
                prompt=scene_prompt,
                max_tokens=1500,
                temperature=0.6
            )
            
            if not scene_result.success:
                raise Exception(f"场景分析失败: {scene_result.error}")
            
            # 解析结果
            characters_data = self._parse_json_response(char_result.data.get('text', ''))
            scenes_data = self._parse_json_response(scene_result.data.get('text', ''))
            
            return {
                'characters': characters_data.get('characters', []),
                'scenes': scenes_data.get('scenes', []),
                'analysis_complete': True
            }
            
        except Exception as e:
            logger.error(f"阶段2处理失败: {e}")
            raise
    
    async def process_stage_3_plot_structure(self, text: str, characters: List[Dict], scenes: List[Dict]) -> Dict[str, Any]:
        """阶段3：情节结构分析"""
        try:
            prompt = f"""
            基于以下信息，分析故事的情节结构，将文本分解为逻辑清晰的情节段落。

            原始文本：
            {text}

            角色信息：
            {json.dumps(characters, ensure_ascii=False, indent=2)}

            场景信息：
            {json.dumps(scenes, ensure_ascii=False, indent=2)}

            请按照以下JSON格式输出情节结构分析：
            {{
                "plot_segments": [
                    {{
                        "segment_id": "段落编号",
                        "title": "段落标题",
                        "content": "段落内容（原文）",
                        "summary": "段落摘要",
                        "characters_involved": ["参与的角色名称"],
                        "scene_location": "发生场景",
                        "emotional_tone": "情感基调",
                        "narrative_function": "叙事功能（如：开端、发展、高潮、结局）",
                        "key_actions": ["关键动作或事件"],
                        "dialogue_highlights": ["重要对话"]
                    }}
                ]
            }}

            请确保：
            1. 按照故事发展的时间顺序排列段落
            2. 每个段落内容完整，不遗漏原文信息
            3. 准确识别参与角色和场景位置
            4. 情感基调描述要准确，便于后续镜头设计
            5. 叙事功能分析要专业，体现故事结构
            """
            
            result = await self.service_manager.call_service(
                ServiceType.LLM,
                "generate_text",
                prompt=prompt,
                max_tokens=2500,
                temperature=0.6
            )
            
            if not result.success:
                raise Exception(f"情节结构分析失败: {result.error}")
            
            plot_data = self._parse_json_response(result.data.get('text', ''))
            
            return {
                'plot_segments': plot_data.get('plot_segments', []),
                'structure_complete': True
            }
            
        except Exception as e:
            logger.error(f"阶段3处理失败: {e}")
            raise
    
    async def process_stage_4_shot_generation(self, plot_segments: List[Dict], characters: List[Dict], scenes: List[Dict], style: str) -> Dict[str, Any]:
        """阶段4：分镜脚本生成"""
        try:
            shots = []
            shot_id = 1
            
            for segment in plot_segments:
                segment_prompt = f"""
                基于以下情节段落，生成详细的分镜脚本。

                情节段落：
                {json.dumps(segment, ensure_ascii=False, indent=2)}

                角色信息：
                {json.dumps(characters, ensure_ascii=False, indent=2)}

                场景信息：
                {json.dumps(scenes, ensure_ascii=False, indent=2)}

                风格要求：{style}

                请按照以下JSON格式输出分镜脚本：
                {{
                    "shots": [
                        {{
                            "shot_number": "镜头编号",
                            "shot_type": "镜头类型（close_up/medium_shot/long_shot/wide_shot）",
                            "camera_movement": "摄像机运动（static/pan/tilt/zoom_in/zoom_out）",
                            "duration": "持续时间（秒）",
                            "description": "镜头描述",
                            "visual_description": "视觉描述（详细的画面内容）",
                            "dialogue": "对话内容",
                            "action": "动作描述",
                            "characters": ["出现的角色"],
                            "scene_location": "场景位置",
                            "lighting": "光线描述",
                            "mood": "情绪氛围",
                            "image_prompt": "图像生成提示词（英文，详细描述画面内容、风格、光线等）"
                        }}
                    ]
                }}

                请确保：
                1. 镜头设计要符合电影语言规范
                2. 视觉描述要详细具体，便于图像生成
                3. 图像提示词要专业，包含风格、构图、光线等要素
                4. 镜头时长要合理，符合叙事节奏
                5. 摄像机运动要有目的性，服务于叙事
                """
                
                result = await self.service_manager.call_service(
                    ServiceType.LLM,
                    "generate_text",
                    prompt=segment_prompt,
                    max_tokens=2000,
                    temperature=0.7
                )
                
                if not result.success:
                    logger.warning(f"段落 {segment.get('segment_id')} 分镜生成失败: {result.error}")
                    continue
                
                segment_shots = self._parse_json_response(result.data.get('text', ''))
                
                for shot_data in segment_shots.get('shots', []):
                    shot_data['shot_number'] = shot_id
                    shots.append(shot_data)
                    shot_id += 1
            
            return {
                'shots': shots,
                'total_shots': len(shots),
                'generation_complete': True
            }
            
        except Exception as e:
            logger.error(f"阶段4处理失败: {e}")
            raise
    
    async def process_stage_5_optimization(self, shots: List[Dict], style: str) -> Dict[str, Any]:
        """阶段5：优化和完善"""
        try:
            prompt = f"""
            作为专业的分镜师和导演，请对以下分镜脚本进行优化和完善。

            当前分镜脚本：
            {json.dumps(shots, ensure_ascii=False, indent=2)}

            风格要求：{style}

            请从以下方面进行优化分析：

            ## 技术优化建议
            ### 镜头语言
            [分析镜头类型和运动的合理性，提出改进建议]

            ### 节奏控制
            [分析镜头时长和剪辑节奏，提出优化建议]

            ### 视觉连贯性
            [分析镜头间的视觉连接，提出改进建议]

            ## 叙事优化建议
            ### 情感表达
            [分析情感传达效果，提出增强建议]

            ### 信息传递
            [分析信息传递的清晰度，提出改进建议]

            ### 观众体验
            [从观众角度分析观看体验，提出优化建议]

            ## 制作建议
            ### 拍摄难度
            [评估拍摄难度，提出简化或替代方案]

            ### 成本控制
            [分析制作成本，提出优化建议]

            ### 技术要求
            [分析技术实现要求，提出建议]

            请提供具体、可操作的优化建议，帮助提升分镜脚本的质量。
            """
            
            result = await self.service_manager.call_service(
                ServiceType.LLM,
                "generate_text",
                prompt=prompt,
                max_tokens=2000,
                temperature=0.6
            )
            
            if not result.success:
                raise Exception(f"优化分析失败: {result.error}")
            
            optimization_suggestions = result.data.get('text', '')
            
            return {
                'optimization_suggestions': optimization_suggestions,
                'optimized_shots': shots,  # 这里可以根据建议进一步优化
                'optimization_complete': True
            }
            
        except Exception as e:
            logger.error(f"阶段5处理失败: {e}")
            raise
    
    def _parse_json_response(self, text: str) -> Dict[str, Any]:
        """解析JSON响应"""
        try:
            # 尝试直接解析
            return json.loads(text)
        except json.JSONDecodeError:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', text, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    pass
            
            # 如果解析失败，返回空字典
            logger.warning(f"JSON解析失败，原始文本: {text[:200]}...")
            return {}
    
    async def create_storyboard_from_results(self, 
                                           text: str,
                                           stage_results: Dict[str, Any],
                                           style: str) -> Storyboard:
        """从处理结果创建分镜板对象"""
        try:
            # 创建分镜板元数据
            metadata = StoryboardMetadata(
                title="AI生成分镜",
                description="基于五阶段分析生成的分镜脚本",
                style=style
            )
            
            # 创建分镜板
            storyboard = Storyboard(metadata=metadata)
            storyboard.original_text = text
            storyboard.world_setting = stage_results.get('stage_1', {}).get('world_setting', '')
            storyboard.visual_style = stage_results.get('stage_1', {}).get('visual_style', style)
            
            # 添加角色
            characters_data = stage_results.get('stage_2', {}).get('characters', [])
            for char_data in characters_data:
                character = Character(
                    name=char_data.get('name', ''),
                    description=char_data.get('description', ''),
                    appearance=char_data.get('appearance', ''),
                    personality=char_data.get('personality', ''),
                    voice_style=char_data.get('voice_style', '')
                )
                storyboard.add_character(character)
            
            # 添加场景
            scenes_data = stage_results.get('stage_2', {}).get('scenes', [])
            for scene_data in scenes_data:
                scene = Scene(
                    title=scene_data.get('title', ''),
                    location=scene_data.get('location', ''),
                    time_of_day=scene_data.get('time_of_day', ''),
                    weather=scene_data.get('weather', ''),
                    environment=scene_data.get('environment', ''),
                    mood=scene_data.get('mood', ''),
                    lighting=scene_data.get('lighting', '')
                )
                storyboard.add_scene(scene)
            
            # 添加镜头
            shots_data = stage_results.get('stage_4', {}).get('shots', [])
            for shot_data in shots_data:
                # 解析镜头类型
                shot_type_str = shot_data.get('shot_type', 'medium_shot')
                try:
                    shot_type = ShotType(shot_type_str)
                except ValueError:
                    shot_type = ShotType.MEDIUM_SHOT
                
                # 解析摄像机运动
                camera_movement_str = shot_data.get('camera_movement', 'static')
                try:
                    camera_movement = CameraMovement(camera_movement_str)
                except ValueError:
                    camera_movement = CameraMovement.STATIC
                
                shot = Shot(
                    sequence_number=shot_data.get('shot_number', 0),
                    description=shot_data.get('description', ''),
                    dialogue=shot_data.get('dialogue', ''),
                    action=shot_data.get('action', ''),
                    shot_type=shot_type,
                    camera_movement=camera_movement,
                    duration=float(shot_data.get('duration', 3.0)),
                    visual_description=shot_data.get('visual_description', ''),
                    image_prompt=shot_data.get('image_prompt', ''),
                    characters=shot_data.get('characters', [])
                )
                storyboard.add_shot(shot)
            
            # 设置阶段数据
            for stage, data in stage_results.items():
                storyboard.set_stage_data(stage, data)
            
            return storyboard
            
        except Exception as e:
            logger.error(f"创建分镜板失败: {e}")
            raise

def create_storyboard_workflow() -> Workflow:
    """创建分镜生成工作流"""
    workflow = Workflow(
        workflow_id="storyboard_generation",
        name="五阶段分镜生成",
        description="基于AI的智能分镜生成工作流"
    )
    
    # 阶段1：世界观构建
    stage1_step = WorkflowStep(
        step_id="world_building",
        name="世界观构建",
        service_type=ServiceType.LLM,
        method="process_stage_1_world_building",
        params={"text": "${input.text}", "style": "${input.style}"},
        timeout=120.0
    )
    
    # 阶段2：角色场景分析
    stage2_step = WorkflowStep(
        step_id="character_scene_analysis",
        name="角色场景分析",
        service_type=ServiceType.LLM,
        method="process_stage_2_character_scene_analysis",
        params={
            "text": "${input.text}",
            "world_setting": "${world_building.world_setting}"
        },
        depends_on=["world_building"],
        timeout=120.0
    )
    
    # 阶段3：情节结构分析
    stage3_step = WorkflowStep(
        step_id="plot_structure",
        name="情节结构分析",
        service_type=ServiceType.LLM,
        method="process_stage_3_plot_structure",
        params={
            "text": "${input.text}",
            "characters": "${character_scene_analysis.characters}",
            "scenes": "${character_scene_analysis.scenes}"
        },
        depends_on=["character_scene_analysis"],
        timeout=120.0
    )
    
    # 阶段4：分镜脚本生成
    stage4_step = WorkflowStep(
        step_id="shot_generation",
        name="分镜脚本生成",
        service_type=ServiceType.LLM,
        method="process_stage_4_shot_generation",
        params={
            "plot_segments": "${plot_structure.plot_segments}",
            "characters": "${character_scene_analysis.characters}",
            "scenes": "${character_scene_analysis.scenes}",
            "style": "${input.style}"
        },
        depends_on=["plot_structure"],
        timeout=180.0
    )
    
    # 阶段5：优化和完善
    stage5_step = WorkflowStep(
        step_id="optimization",
        name="优化和完善",
        service_type=ServiceType.LLM,
        method="process_stage_5_optimization",
        params={
            "shots": "${shot_generation.shots}",
            "style": "${input.style}"
        },
        depends_on=["shot_generation"],
        timeout=120.0
    )
    
    workflow.steps = [stage1_step, stage2_step, stage3_step, stage4_step, stage5_step]
    
    return workflow
