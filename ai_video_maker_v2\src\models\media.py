# -*- coding: utf-8 -*-
"""
媒体资源模型 - 媒体文件数据结构

定义媒体资源的数据结构：
- 基础媒体资源
- 图像资源
- 音频资源
- 视频资源
"""

import uuid
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

class MediaType(Enum):
    """媒体类型"""
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"
    DOCUMENT = "document"

class MediaStatus(Enum):
    """媒体状态"""
    PENDING = "pending"        # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"    # 已完成
    FAILED = "failed"         # 失败
    ARCHIVED = "archived"     # 已归档

@dataclass
class MediaMetadata:
    """媒体元数据"""
    asset_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    tags: List[str] = field(default_factory=list)
    
    # 文件信息
    filename: str = ""
    file_path: str = ""
    file_size: int = 0  # 字节
    mime_type: str = ""
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 来源信息
    source: str = ""  # 生成来源（如：cogview, azure_tts等）
    source_params: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MediaMetadata':
        """从字典创建"""
        data = data.copy()
        if 'created_at' in data:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)

@dataclass
class MediaAsset:
    """基础媒体资源"""
    metadata: MediaMetadata = field(default_factory=MediaMetadata)
    media_type: MediaType = MediaType.IMAGE
    status: MediaStatus = MediaStatus.PENDING
    
    # 关联信息
    project_id: str = ""
    shot_id: Optional[str] = None
    scene_id: Optional[str] = None
    
    # 处理信息
    processing_log: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    
    # 自定义属性
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    def add_log(self, message: str):
        """添加处理日志"""
        timestamp = datetime.now().isoformat()
        self.processing_log.append(f"[{timestamp}] {message}")
        self.metadata.updated_at = datetime.now()
    
    def set_status(self, status: MediaStatus, message: Optional[str] = None):
        """设置状态"""
        self.status = status
        if message:
            self.add_log(f"状态变更为 {status.value}: {message}")
    
    def set_error(self, error_message: str):
        """设置错误"""
        self.status = MediaStatus.FAILED
        self.error_message = error_message
        self.add_log(f"错误: {error_message}")
    
    def get_file_path(self) -> Path:
        """获取文件路径"""
        return Path(self.metadata.file_path)
    
    def exists(self) -> bool:
        """检查文件是否存在"""
        return self.get_file_path().exists()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'metadata': self.metadata.to_dict(),
            'media_type': self.media_type.value,
            'status': self.status.value,
            'project_id': self.project_id,
            'shot_id': self.shot_id,
            'scene_id': self.scene_id,
            'processing_log': self.processing_log,
            'error_message': self.error_message,
            'custom_properties': self.custom_properties
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MediaAsset':
        """从字典创建"""
        metadata = MediaMetadata.from_dict(data.get('metadata', {}))
        
        return cls(
            metadata=metadata,
            media_type=MediaType(data.get('media_type', 'image')),
            status=MediaStatus(data.get('status', 'pending')),
            project_id=data.get('project_id', ''),
            shot_id=data.get('shot_id'),
            scene_id=data.get('scene_id'),
            processing_log=data.get('processing_log', []),
            error_message=data.get('error_message'),
            custom_properties=data.get('custom_properties', {})
        )

@dataclass
class ImageAsset(MediaAsset):
    """图像资源"""
    
    def __post_init__(self):
        self.media_type = MediaType.IMAGE
    
    # 图像属性
    width: int = 0
    height: int = 0
    channels: int = 3  # RGB=3, RGBA=4
    color_mode: str = "RGB"
    
    # 生成参数
    prompt: str = ""
    negative_prompt: str = ""
    style: str = ""
    seed: Optional[int] = None
    
    # 图像质量
    quality: str = "high"  # low, medium, high
    compression: float = 0.9
    
    # 缩略图
    thumbnail_path: Optional[str] = None
    
    def get_dimensions(self) -> Tuple[int, int]:
        """获取图像尺寸"""
        return (self.width, self.height)
    
    def get_aspect_ratio(self) -> float:
        """获取宽高比"""
        if self.height == 0:
            return 0.0
        return self.width / self.height
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = super().to_dict()
        data.update({
            'width': self.width,
            'height': self.height,
            'channels': self.channels,
            'color_mode': self.color_mode,
            'prompt': self.prompt,
            'negative_prompt': self.negative_prompt,
            'style': self.style,
            'seed': self.seed,
            'quality': self.quality,
            'compression': self.compression,
            'thumbnail_path': self.thumbnail_path
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ImageAsset':
        """从字典创建"""
        base_asset = MediaAsset.from_dict(data)
        
        return cls(
            metadata=base_asset.metadata,
            status=base_asset.status,
            project_id=base_asset.project_id,
            shot_id=base_asset.shot_id,
            scene_id=base_asset.scene_id,
            processing_log=base_asset.processing_log,
            error_message=base_asset.error_message,
            custom_properties=base_asset.custom_properties,
            width=data.get('width', 0),
            height=data.get('height', 0),
            channels=data.get('channels', 3),
            color_mode=data.get('color_mode', 'RGB'),
            prompt=data.get('prompt', ''),
            negative_prompt=data.get('negative_prompt', ''),
            style=data.get('style', ''),
            seed=data.get('seed'),
            quality=data.get('quality', 'high'),
            compression=data.get('compression', 0.9),
            thumbnail_path=data.get('thumbnail_path')
        )

@dataclass
class AudioAsset(MediaAsset):
    """音频资源"""
    
    def __post_init__(self):
        self.media_type = MediaType.AUDIO
    
    # 音频属性
    duration: float = 0.0  # 秒
    sample_rate: int = 44100  # Hz
    channels: int = 2  # 1=单声道, 2=立体声
    bit_depth: int = 16  # 位深度
    bitrate: int = 192  # kbps
    
    # 生成参数
    text: str = ""  # 原始文本
    voice_id: str = ""
    voice_style: str = ""
    speed: float = 1.0
    pitch: float = 1.0
    volume: float = 1.0
    
    # 音频处理
    effects: List[str] = field(default_factory=list)
    normalized: bool = False
    
    def get_duration_formatted(self) -> str:
        """获取格式化的时长"""
        minutes = int(self.duration // 60)
        seconds = int(self.duration % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = super().to_dict()
        data.update({
            'duration': self.duration,
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'bit_depth': self.bit_depth,
            'bitrate': self.bitrate,
            'text': self.text,
            'voice_id': self.voice_id,
            'voice_style': self.voice_style,
            'speed': self.speed,
            'pitch': self.pitch,
            'volume': self.volume,
            'effects': self.effects,
            'normalized': self.normalized
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AudioAsset':
        """从字典创建"""
        base_asset = MediaAsset.from_dict(data)
        
        return cls(
            metadata=base_asset.metadata,
            status=base_asset.status,
            project_id=base_asset.project_id,
            shot_id=base_asset.shot_id,
            scene_id=base_asset.scene_id,
            processing_log=base_asset.processing_log,
            error_message=base_asset.error_message,
            custom_properties=base_asset.custom_properties,
            duration=data.get('duration', 0.0),
            sample_rate=data.get('sample_rate', 44100),
            channels=data.get('channels', 2),
            bit_depth=data.get('bit_depth', 16),
            bitrate=data.get('bitrate', 192),
            text=data.get('text', ''),
            voice_id=data.get('voice_id', ''),
            voice_style=data.get('voice_style', ''),
            speed=data.get('speed', 1.0),
            pitch=data.get('pitch', 1.0),
            volume=data.get('volume', 1.0),
            effects=data.get('effects', []),
            normalized=data.get('normalized', False)
        )

@dataclass
class VideoAsset(MediaAsset):
    """视频资源"""
    
    def __post_init__(self):
        self.media_type = MediaType.VIDEO
    
    # 视频属性
    duration: float = 0.0  # 秒
    width: int = 1920
    height: int = 1080
    fps: float = 30.0
    bitrate: int = 5000  # kbps
    codec: str = "h264"
    
    # 生成参数
    prompt: str = ""
    style: str = ""
    motion_strength: float = 0.5
    seed: Optional[int] = None
    
    # 输入资源
    input_image: Optional[str] = None  # 输入图像路径
    input_audio: Optional[str] = None  # 输入音频路径
    
    # 视频处理
    effects: List[str] = field(default_factory=list)
    transitions: List[str] = field(default_factory=list)
    
    # 缩略图和预览
    thumbnail_path: Optional[str] = None
    preview_path: Optional[str] = None
    
    def get_dimensions(self) -> Tuple[int, int]:
        """获取视频尺寸"""
        return (self.width, self.height)
    
    def get_aspect_ratio(self) -> float:
        """获取宽高比"""
        if self.height == 0:
            return 0.0
        return self.width / self.height
    
    def get_duration_formatted(self) -> str:
        """获取格式化的时长"""
        minutes = int(self.duration // 60)
        seconds = int(self.duration % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = super().to_dict()
        data.update({
            'duration': self.duration,
            'width': self.width,
            'height': self.height,
            'fps': self.fps,
            'bitrate': self.bitrate,
            'codec': self.codec,
            'prompt': self.prompt,
            'style': self.style,
            'motion_strength': self.motion_strength,
            'seed': self.seed,
            'input_image': self.input_image,
            'input_audio': self.input_audio,
            'effects': self.effects,
            'transitions': self.transitions,
            'thumbnail_path': self.thumbnail_path,
            'preview_path': self.preview_path
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VideoAsset':
        """从字典创建"""
        base_asset = MediaAsset.from_dict(data)
        
        return cls(
            metadata=base_asset.metadata,
            status=base_asset.status,
            project_id=base_asset.project_id,
            shot_id=base_asset.shot_id,
            scene_id=base_asset.scene_id,
            processing_log=base_asset.processing_log,
            error_message=base_asset.error_message,
            custom_properties=base_asset.custom_properties,
            duration=data.get('duration', 0.0),
            width=data.get('width', 1920),
            height=data.get('height', 1080),
            fps=data.get('fps', 30.0),
            bitrate=data.get('bitrate', 5000),
            codec=data.get('codec', 'h264'),
            prompt=data.get('prompt', ''),
            style=data.get('style', ''),
            motion_strength=data.get('motion_strength', 0.5),
            seed=data.get('seed'),
            input_image=data.get('input_image'),
            input_audio=data.get('input_audio'),
            effects=data.get('effects', []),
            transitions=data.get('transitions', []),
            thumbnail_path=data.get('thumbnail_path'),
            preview_path=data.get('preview_path')
        )
