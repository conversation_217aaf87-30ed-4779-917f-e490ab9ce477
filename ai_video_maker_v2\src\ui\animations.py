# -*- coding: utf-8 -*-
"""
动画管理器 - 现代化的动画效果

提供各种动画效果：
- 淡入淡出
- 滑动效果
- 缩放效果
- 旋转效果
- 弹性效果
"""

from PyQt6.QtCore import (QPropertyAnimation, QEasingCurve, QParallelAnimationGroup,
                         QSequentialAnimationGroup, QTimer, pyqtProperty, QObject)
from PyQt6.QtGui import QTransform
from PyQt6.QtWidgets import QWidget, QGraphicsOpacityEffect
from typing import Callable, Optional
import math

class AnimationManager(QObject):
    """动画管理器"""
    
    def __init__(self):
        super().__init__()
        self._animations = {}
        self._animation_id = 0
    
    def fade_in(self, widget: QWidget, duration: int = 300, 
                easing: QEasingCurve.Type = QEasingCurve.Type.OutCubic,
                callback: Optional[Callable] = None) -> int:
        """淡入动画"""
        # 设置透明度效果
        opacity_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(opacity_effect)
        
        # 创建动画
        animation = QPropertyAnimation(opacity_effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(easing)
        
        # 设置回调
        if callback:
            animation.finished.connect(callback)
        
        # 启动动画
        animation.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation
        
        # 动画完成后清理
        animation.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def fade_out(self, widget: QWidget, duration: int = 300,
                 easing: QEasingCurve.Type = QEasingCurve.Type.OutCubic,
                 callback: Optional[Callable] = None) -> int:
        """淡出动画"""
        # 设置透明度效果
        opacity_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(opacity_effect)
        
        # 创建动画
        animation = QPropertyAnimation(opacity_effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(1.0)
        animation.setEndValue(0.0)
        animation.setEasingCurve(easing)
        
        # 设置回调
        if callback:
            animation.finished.connect(callback)
        
        # 启动动画
        animation.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation
        
        # 动画完成后清理
        animation.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def slide_in_from_left(self, widget: QWidget, duration: int = 400,
                          easing: QEasingCurve.Type = QEasingCurve.Type.OutCubic,
                          callback: Optional[Callable] = None) -> int:
        """从左侧滑入"""
        # 获取原始位置
        original_pos = widget.pos()
        start_pos = original_pos
        start_pos.setX(original_pos.x() - widget.width())
        
        # 设置起始位置
        widget.move(start_pos)
        widget.show()
        
        # 创建动画
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(original_pos)
        animation.setEasingCurve(easing)
        
        # 设置回调
        if callback:
            animation.finished.connect(callback)
        
        # 启动动画
        animation.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation
        
        # 动画完成后清理
        animation.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def slide_in_from_right(self, widget: QWidget, duration: int = 400,
                           easing: QEasingCurve.Type = QEasingCurve.Type.OutCubic,
                           callback: Optional[Callable] = None) -> int:
        """从右侧滑入"""
        # 获取原始位置
        original_pos = widget.pos()
        start_pos = original_pos
        start_pos.setX(original_pos.x() + widget.width())
        
        # 设置起始位置
        widget.move(start_pos)
        widget.show()
        
        # 创建动画
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(original_pos)
        animation.setEasingCurve(easing)
        
        # 设置回调
        if callback:
            animation.finished.connect(callback)
        
        # 启动动画
        animation.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation
        
        # 动画完成后清理
        animation.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def slide_in_from_top(self, widget: QWidget, duration: int = 400,
                         easing: QEasingCurve.Type = QEasingCurve.Type.OutCubic,
                         callback: Optional[Callable] = None) -> int:
        """从顶部滑入"""
        # 获取原始位置
        original_pos = widget.pos()
        start_pos = original_pos
        start_pos.setY(original_pos.y() - widget.height())
        
        # 设置起始位置
        widget.move(start_pos)
        widget.show()
        
        # 创建动画
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(start_pos)
        animation.setEndValue(original_pos)
        animation.setEasingCurve(easing)
        
        # 设置回调
        if callback:
            animation.finished.connect(callback)
        
        # 启动动画
        animation.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation
        
        # 动画完成后清理
        animation.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def scale_in(self, widget: QWidget, duration: int = 300,
                 easing: QEasingCurve.Type = QEasingCurve.Type.OutBack,
                 callback: Optional[Callable] = None) -> int:
        """缩放进入动画"""
        # 创建缩放动画
        scale_animation = QPropertyAnimation(widget, b"geometry")
        scale_animation.setDuration(duration)
        scale_animation.setEasingCurve(easing)
        
        # 获取原始几何信息
        original_geometry = widget.geometry()
        
        # 计算缩放起始几何信息（从中心缩放）
        center = original_geometry.center()
        start_geometry = original_geometry
        start_geometry.setWidth(0)
        start_geometry.setHeight(0)
        start_geometry.moveCenter(center)
        
        scale_animation.setStartValue(start_geometry)
        scale_animation.setEndValue(original_geometry)
        
        # 结合透明度动画
        opacity_effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(opacity_effect)
        
        opacity_animation = QPropertyAnimation(opacity_effect, b"opacity")
        opacity_animation.setDuration(duration)
        opacity_animation.setStartValue(0.0)
        opacity_animation.setEndValue(1.0)
        opacity_animation.setEasingCurve(easing)
        
        # 并行动画组
        animation_group = QParallelAnimationGroup()
        animation_group.addAnimation(scale_animation)
        animation_group.addAnimation(opacity_animation)
        
        # 设置回调
        if callback:
            animation_group.finished.connect(callback)
        
        # 启动动画
        animation_group.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation_group
        
        # 动画完成后清理
        animation_group.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def bounce_in(self, widget: QWidget, duration: int = 600,
                  callback: Optional[Callable] = None) -> int:
        """弹跳进入动画"""
        # 创建序列动画组
        animation_group = QSequentialAnimationGroup()
        
        # 第一阶段：快速放大
        scale1 = QPropertyAnimation(widget, b"geometry")
        scale1.setDuration(duration // 3)
        scale1.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        original_geometry = widget.geometry()
        center = original_geometry.center()
        
        # 起始状态（缩小）
        start_geometry = original_geometry
        start_geometry.setWidth(int(original_geometry.width() * 0.3))
        start_geometry.setHeight(int(original_geometry.height() * 0.3))
        start_geometry.moveCenter(center)
        
        # 第一阶段结束状态（略微放大）
        mid_geometry = original_geometry
        mid_geometry.setWidth(int(original_geometry.width() * 1.1))
        mid_geometry.setHeight(int(original_geometry.height() * 1.1))
        mid_geometry.moveCenter(center)
        
        scale1.setStartValue(start_geometry)
        scale1.setEndValue(mid_geometry)
        
        # 第二阶段：回弹到正常大小
        scale2 = QPropertyAnimation(widget, b"geometry")
        scale2.setDuration(duration * 2 // 3)
        scale2.setEasingCurve(QEasingCurve.Type.OutBounce)
        scale2.setStartValue(mid_geometry)
        scale2.setEndValue(original_geometry)
        
        animation_group.addAnimation(scale1)
        animation_group.addAnimation(scale2)
        
        # 设置回调
        if callback:
            animation_group.finished.connect(callback)
        
        # 启动动画
        animation_group.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation_group
        
        # 动画完成后清理
        animation_group.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def shake(self, widget: QWidget, duration: int = 500,
              intensity: int = 10, callback: Optional[Callable] = None) -> int:
        """摇摆动画"""
        original_pos = widget.pos()
        
        # 创建序列动画组
        animation_group = QSequentialAnimationGroup()
        
        # 创建多个小的移动动画
        shake_count = 8
        single_duration = duration // shake_count
        
        for i in range(shake_count):
            shake_animation = QPropertyAnimation(widget, b"pos")
            shake_animation.setDuration(single_duration)
            shake_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
            
            # 计算偏移量
            offset_x = intensity * math.sin(i * math.pi / 2) * (1 - i / shake_count)
            shake_pos = original_pos
            shake_pos.setX(original_pos.x() + int(offset_x))
            
            if i == 0:
                shake_animation.setStartValue(original_pos)
            shake_animation.setEndValue(shake_pos)
            
            animation_group.addAnimation(shake_animation)
        
        # 最后回到原位置
        final_animation = QPropertyAnimation(widget, b"pos")
        final_animation.setDuration(single_duration)
        final_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        final_animation.setEndValue(original_pos)
        animation_group.addAnimation(final_animation)
        
        # 设置回调
        if callback:
            animation_group.finished.connect(callback)
        
        # 启动动画
        animation_group.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation_group
        
        # 动画完成后清理
        animation_group.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def pulse(self, widget: QWidget, duration: int = 1000, 
              scale_factor: float = 1.1, repeat: int = 3,
              callback: Optional[Callable] = None) -> int:
        """脉冲动画"""
        # 创建序列动画组
        animation_group = QSequentialAnimationGroup()
        
        original_geometry = widget.geometry()
        center = original_geometry.center()
        
        # 放大的几何信息
        scaled_geometry = original_geometry
        scaled_geometry.setWidth(int(original_geometry.width() * scale_factor))
        scaled_geometry.setHeight(int(original_geometry.height() * scale_factor))
        scaled_geometry.moveCenter(center)
        
        single_duration = duration // (repeat * 2)
        
        for i in range(repeat):
            # 放大动画
            scale_up = QPropertyAnimation(widget, b"geometry")
            scale_up.setDuration(single_duration)
            scale_up.setEasingCurve(QEasingCurve.Type.InOutSine)
            scale_up.setStartValue(original_geometry)
            scale_up.setEndValue(scaled_geometry)
            
            # 缩小动画
            scale_down = QPropertyAnimation(widget, b"geometry")
            scale_down.setDuration(single_duration)
            scale_down.setEasingCurve(QEasingCurve.Type.InOutSine)
            scale_down.setStartValue(scaled_geometry)
            scale_down.setEndValue(original_geometry)
            
            animation_group.addAnimation(scale_up)
            animation_group.addAnimation(scale_down)
        
        # 设置回调
        if callback:
            animation_group.finished.connect(callback)
        
        # 启动动画
        animation_group.start()
        
        # 保存动画引用
        animation_id = self._get_next_id()
        self._animations[animation_id] = animation_group
        
        # 动画完成后清理
        animation_group.finished.connect(lambda: self._cleanup_animation(animation_id))
        
        return animation_id
    
    def stop_animation(self, animation_id: int):
        """停止动画"""
        if animation_id in self._animations:
            animation = self._animations[animation_id]
            animation.stop()
            self._cleanup_animation(animation_id)
    
    def stop_all_animations(self):
        """停止所有动画"""
        for animation_id in list(self._animations.keys()):
            self.stop_animation(animation_id)
    
    def _get_next_id(self) -> int:
        """获取下一个动画ID"""
        self._animation_id += 1
        return self._animation_id
    
    def _cleanup_animation(self, animation_id: int):
        """清理动画"""
        if animation_id in self._animations:
            del self._animations[animation_id]

# 全局动画管理器实例
animation_manager = AnimationManager()
