# -*- coding: utf-8 -*-
"""
应用程序控制器 - 应用程序的主控制器

协调各个组件，提供统一的应用程序接口：
- 应用程序生命周期管理
- 组件协调
- 高级业务逻辑
- 错误处理
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional, Callable
import sys

from .config_manager import ConfigManager
from .event_system import EventSystem, Event, EventType
from .service_manager import ServiceManager
from .state_manager import StateManager
from .workflow_manager import WorkflowManager

logger = logging.getLogger(__name__)

class AppController:
    """应用程序控制器"""
    
    def __init__(self, config_dir: str = "config", data_dir: str = "data"):
        self.config_dir = Path(config_dir)
        self.data_dir = Path(data_dir)
        
        # 确保目录存在
        self.config_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        # 核心组件
        self.config_manager: Optional[ConfigManager] = None
        self.event_system: Optional[EventSystem] = None
        self.state_manager: Optional[StateManager] = None
        self.service_manager: Optional[ServiceManager] = None
        self.workflow_manager: Optional[WorkflowManager] = None
        
        # 应用程序状态
        self._initialized = False
        self._running = False
        
        # 事件循环
        self._event_loop: Optional[asyncio.AbstractEventLoop] = None
    
    async def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            logger.info("开始初始化应用程序...")
            
            # 获取事件循环
            self._event_loop = asyncio.get_event_loop()
            
            # 1. 初始化配置管理器
            self.config_manager = ConfigManager(str(self.config_dir))
            logger.info("✅ 配置管理器初始化完成")
            
            # 2. 初始化事件系统
            self.event_system = EventSystem()
            await self.event_system.start()
            logger.info("✅ 事件系统初始化完成")
            
            # 3. 初始化状态管理器
            state_file = self.data_dir / "app_state.json"
            self.state_manager = StateManager(
                event_system=self.event_system,
                state_file=str(state_file)
            )
            logger.info("✅ 状态管理器初始化完成")
            
            # 4. 初始化服务管理器
            self.service_manager = ServiceManager(
                config_manager=self.config_manager,
                event_system=self.event_system
            )
            await self.service_manager.start()
            logger.info("✅ 服务管理器初始化完成")
            
            # 5. 初始化工作流管理器
            self.workflow_manager = WorkflowManager(
                service_manager=self.service_manager,
                event_system=self.event_system
            )
            logger.info("✅ 工作流管理器初始化完成")
            
            # 6. 注册预定义工作流
            self._register_workflows()
            
            # 7. 设置事件监听器
            self._setup_event_listeners()
            
            # 发布应用启动事件
            event = Event(
                event_type=EventType.APP_STARTED,
                data={
                    'version': '2.0.0',
                    'config_dir': str(self.config_dir),
                    'data_dir': str(self.data_dir)
                },
                source='app_controller'
            )
            await self.event_system.emit(event)
            
            self._initialized = True
            self._running = True
            
            logger.info("🎉 应用程序初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"应用程序初始化失败: {e}", exc_info=True)
            await self.shutdown()
            return False
    
    async def shutdown(self):
        """关闭应用程序"""
        try:
            logger.info("开始关闭应用程序...")
            
            self._running = False
            
            # 发布应用关闭事件
            if self.event_system:
                event = Event(
                    event_type=EventType.APP_SHUTDOWN,
                    data={'reason': 'normal_shutdown'},
                    source='app_controller'
                )
                await self.event_system.emit(event)
            
            # 保存状态
            if self.state_manager:
                self.state_manager.save_state()
            
            # 关闭组件（按依赖顺序逆序关闭）
            if self.workflow_manager:
                await self.workflow_manager.shutdown()
                logger.info("✅ 工作流管理器已关闭")
            
            if self.service_manager:
                await self.service_manager.shutdown()
                logger.info("✅ 服务管理器已关闭")
            
            if self.event_system:
                await self.event_system.shutdown()
                logger.info("✅ 事件系统已关闭")
            
            logger.info("🎉 应用程序已关闭")
            
        except Exception as e:
            logger.error(f"关闭应用程序时发生错误: {e}", exc_info=True)
    
    def _register_workflows(self):
        """注册预定义工作流"""
        from ..workflows.storyboard_workflow import create_storyboard_workflow
        # from ..workflows.image_generation_workflow import create_image_generation_workflow
        # from ..workflows.voice_generation_workflow import create_voice_generation_workflow
        # from ..workflows.video_generation_workflow import create_video_generation_workflow
        
        try:
            # 注册分镜生成工作流
            storyboard_workflow = create_storyboard_workflow()
            self.workflow_manager.register_template(storyboard_workflow)
            
            # 注册图像生成工作流
            # image_workflow = create_image_generation_workflow()
            # self.workflow_manager.register_template(image_workflow)

            # 注册语音生成工作流
            # voice_workflow = create_voice_generation_workflow()
            # self.workflow_manager.register_template(voice_workflow)

            # 注册视频生成工作流
            # video_workflow = create_video_generation_workflow()
            # self.workflow_manager.register_template(video_workflow)
            
            logger.info("✅ 预定义工作流已注册")
            
        except ImportError as e:
            logger.warning(f"部分工作流注册失败: {e}")
    
    def _setup_event_listeners(self):
        """设置事件监听器"""
        # 监听项目相关事件
        self.event_system.subscribe(
            EventType.PROJECT_CREATED,
            self._on_project_created
        )
        
        self.event_system.subscribe(
            EventType.PROJECT_LOADED,
            self._on_project_loaded
        )
        
        # 监听服务状态变化
        self.event_system.subscribe(
            EventType.SERVICE_CONNECTED,
            self._on_service_connected
        )
        
        self.event_system.subscribe(
            EventType.SERVICE_DISCONNECTED,
            self._on_service_disconnected
        )
    
    async def _on_project_created(self, event: Event):
        """处理项目创建事件"""
        project_data = event.data
        logger.info(f"项目已创建: {project_data.get('project_name')}")
        
        # 更新状态
        self.state_manager.set_state("current_project", project_data)
        self.state_manager.set_state("project_path", project_data.get("project_path"))
    
    async def _on_project_loaded(self, event: Event):
        """处理项目加载事件"""
        project_data = event.data
        logger.info(f"项目已加载: {project_data.get('project_name')}")
        
        # 更新状态
        self.state_manager.set_state("current_project", project_data)
        self.state_manager.set_state("project_path", project_data.get("project_path"))
    
    async def _on_service_connected(self, event: Event):
        """处理服务连接事件"""
        service_info = event.data
        logger.info(f"服务已连接: {service_info.get('service_type')} - {service_info.get('provider')}")
    
    async def _on_service_disconnected(self, event: Event):
        """处理服务断开事件"""
        service_info = event.data
        logger.warning(f"服务已断开: {service_info.get('service_type')} - {service_info.get('provider')}")
    
    # 高级业务方法
    
    async def create_project(self, project_name: str, description: str = "") -> Dict[str, Any]:
        """创建新项目"""
        try:
            from ..models.project import Project
            
            # 创建项目
            project = Project.create_new(
                name=project_name,
                description=description,
                base_dir=str(self.data_dir / "projects")
            )
            
            # 发布事件
            event = Event(
                event_type=EventType.PROJECT_CREATED,
                data=project.to_dict(),
                source='app_controller'
            )
            await self.event_system.emit(event)
            
            return project.to_dict()
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    async def load_project(self, project_path: str) -> Dict[str, Any]:
        """加载项目"""
        try:
            from ..models.project import Project
            
            # 加载项目
            project = Project.load_from_file(project_path)
            
            # 发布事件
            event = Event(
                event_type=EventType.PROJECT_LOADED,
                data=project.to_dict(),
                source='app_controller'
            )
            await self.event_system.emit(event)
            
            return project.to_dict()
            
        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            raise
    
    async def generate_storyboard(self, 
                                 text: str,
                                 style: str = "电影风格",
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成分镜"""
        try:
            # 创建工作流
            workflow = self.workflow_manager.create_workflow_from_template(
                "storyboard_generation",
                text=text,
                style=style
            )
            
            if not workflow:
                raise ValueError("无法创建分镜生成工作流")
            
            # 执行工作流
            success = await self.workflow_manager.execute_workflow(
                workflow,
                progress_callback
            )
            
            if success:
                return workflow.results
            else:
                raise RuntimeError(f"分镜生成失败: {workflow.errors}")
                
        except Exception as e:
            logger.error(f"生成分镜失败: {e}")
            raise
    
    async def generate_images(self,
                             storyboard_data: Dict[str, Any],
                             provider: str = "cogview",
                             progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成图像"""
        try:
            # 创建工作流
            workflow = self.workflow_manager.create_workflow_from_template(
                "image_generation",
                storyboard_data=storyboard_data,
                provider=provider
            )
            
            if not workflow:
                raise ValueError("无法创建图像生成工作流")
            
            # 执行工作流
            success = await self.workflow_manager.execute_workflow(
                workflow,
                progress_callback
            )
            
            if success:
                return workflow.results
            else:
                raise RuntimeError(f"图像生成失败: {workflow.errors}")
                
        except Exception as e:
            logger.error(f"生成图像失败: {e}")
            raise
    
    async def generate_voice(self,
                            storyboard_data: Dict[str, Any],
                            voice_config: Dict[str, Any],
                            progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成语音"""
        try:
            # 创建工作流
            workflow = self.workflow_manager.create_workflow_from_template(
                "voice_generation",
                storyboard_data=storyboard_data,
                voice_config=voice_config
            )
            
            if not workflow:
                raise ValueError("无法创建语音生成工作流")
            
            # 执行工作流
            success = await self.workflow_manager.execute_workflow(
                workflow,
                progress_callback
            )
            
            if success:
                return workflow.results
            else:
                raise RuntimeError(f"语音生成失败: {workflow.errors}")
                
        except Exception as e:
            logger.error(f"生成语音失败: {e}")
            raise
    
    async def generate_video(self,
                            project_data: Dict[str, Any],
                            video_config: Dict[str, Any],
                            progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成视频"""
        try:
            # 创建工作流
            workflow = self.workflow_manager.create_workflow_from_template(
                "video_generation",
                project_data=project_data,
                video_config=video_config
            )
            
            if not workflow:
                raise ValueError("无法创建视频生成工作流")
            
            # 执行工作流
            success = await self.workflow_manager.execute_workflow(
                workflow,
                progress_callback
            )
            
            if success:
                return workflow.results
            else:
                raise RuntimeError(f"视频生成失败: {workflow.errors}")
                
        except Exception as e:
            logger.error(f"生成视频失败: {e}")
            raise
    
    # 状态查询方法
    
    def get_app_status(self) -> Dict[str, Any]:
        """获取应用程序状态"""
        return {
            'initialized': self._initialized,
            'running': self._running,
            'services': self.service_manager.get_service_stats() if self.service_manager else {},
            'workflows': len(self.workflow_manager.get_running_workflows()) if self.workflow_manager else 0,
            'current_project': self.state_manager.get_state("current_project") if self.state_manager else None
        }
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        if not self.service_manager:
            return {}
        return self.service_manager.get_service_stats()
    
    def get_current_project(self) -> Optional[Dict[str, Any]]:
        """获取当前项目"""
        if not self.state_manager:
            return None
        return self.state_manager.get_state("current_project")
    
    @property
    def is_initialized(self) -> bool:
        """是否已初始化"""
        return self._initialized
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self._running
