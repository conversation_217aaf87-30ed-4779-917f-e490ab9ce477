# -*- coding: utf-8 -*-
"""
按钮组件 - 现代化的按钮设计

提供各种类型的按钮组件：
- 主要按钮
- 次要按钮
- 图标按钮
- 浮动操作按钮
"""

from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty, QSize
from PyQt6.QtGui import QPainter, QPainterPath, QColor, QIcon, QPixmap
from PyQt6.QtWidgets import QPushButton, QGraphicsDropShadowEffect, QSizePolicy

class BaseButton(QPushButton):
    """按钮基类"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self._elevation = 2
        self._hover_elevation = 4
        self._current_elevation = 2
        
        # 设置基本属性
        self.setMinimumHeight(36)
        self.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        
        # 设置阴影效果
        self._setup_shadow()
        
        # 设置动画
        self._setup_animations()
    
    def _setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(self._elevation * 2)
        self.shadow_effect.setOffset(0, self._elevation)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(self.shadow_effect)
    
    def _setup_animations(self):
        """设置动画"""
        self.elevation_animation = QPropertyAnimation(self, b"current_elevation")
        self.elevation_animation.setDuration(150)
        self.elevation_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.elevation_animation.valueChanged.connect(self._update_shadow)
    
    @pyqtProperty(float)
    def current_elevation(self):
        return self._current_elevation
    
    @current_elevation.setter
    def current_elevation(self, value):
        self._current_elevation = value
    
    def _update_shadow(self):
        """更新阴影效果"""
        self.shadow_effect.setBlurRadius(self._current_elevation * 2)
        self.shadow_effect.setOffset(0, self._current_elevation)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        if self.isEnabled():
            self.elevation_animation.setStartValue(self._current_elevation)
            self.elevation_animation.setEndValue(self._hover_elevation)
            self.elevation_animation.start()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.elevation_animation.setStartValue(self._current_elevation)
        self.elevation_animation.setEndValue(self._elevation)
        self.elevation_animation.start()
        super().leaveEvent(event)

class PrimaryButton(BaseButton):
    """主要按钮"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("primary-button")
        
        self.setStyleSheet("""
            QPushButton#primary-button {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton#primary-button:hover {
                background-color: #1976D2;
            }
            QPushButton#primary-button:pressed {
                background-color: #1565C0;
            }
            QPushButton#primary-button:disabled {
                background-color: #BDBDBD;
                color: #FFFFFF;
            }
        """)

class SecondaryButton(BaseButton):
    """次要按钮"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("secondary-button")
        self._elevation = 0
        self._hover_elevation = 1
        self._current_elevation = 0
        self._update_shadow()
        
        self.setStyleSheet("""
            QPushButton#secondary-button {
                background-color: transparent;
                color: #2196F3;
                border: 1px solid #2196F3;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton#secondary-button:hover {
                background-color: #E3F2FD;
                border-color: #1976D2;
                color: #1976D2;
            }
            QPushButton#secondary-button:pressed {
                background-color: #BBDEFB;
                border-color: #1565C0;
                color: #1565C0;
            }
            QPushButton#secondary-button:disabled {
                background-color: transparent;
                border-color: #BDBDBD;
                color: #BDBDBD;
            }
        """)

class TextButton(QPushButton):
    """文本按钮"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("text-button")
        self.setMinimumHeight(36)
        
        self.setStyleSheet("""
            QPushButton#text-button {
                background-color: transparent;
                color: #2196F3;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton#text-button:hover {
                background-color: #E3F2FD;
                color: #1976D2;
            }
            QPushButton#text-button:pressed {
                background-color: #BBDEFB;
                color: #1565C0;
            }
            QPushButton#text-button:disabled {
                background-color: transparent;
                color: #BDBDBD;
            }
        """)

class IconButton(QPushButton):
    """图标按钮"""
    
    def __init__(self, icon=None, parent=None, size=40):
        super().__init__(parent)
        self.setObjectName("icon-button")
        self.button_size = size
        
        # 设置固定大小
        self.setFixedSize(size, size)
        
        # 设置图标
        if icon:
            self.setIcon(icon)
            self.setIconSize(QSize(size // 2, size // 2))
        
        self.setStyleSheet(f"""
            QPushButton#icon-button {{
                background-color: transparent;
                color: #757575;
                border: none;
                border-radius: {size // 2}px;
            }}
            QPushButton#icon-button:hover {{
                background-color: #F5F5F5;
                color: #2196F3;
            }}
            QPushButton#icon-button:pressed {{
                background-color: #EEEEEE;
                color: #1976D2;
            }}
            QPushButton#icon-button:disabled {{
                background-color: transparent;
                color: #BDBDBD;
            }}
        """)

class FloatingActionButton(BaseButton):
    """浮动操作按钮"""
    
    def __init__(self, icon=None, parent=None, size=56):
        super().__init__(parent=parent)
        self.setObjectName("fab")
        self.button_size = size
        self._elevation = 6
        self._hover_elevation = 8
        self._current_elevation = 6
        
        # 设置固定大小
        self.setFixedSize(size, size)
        
        # 设置图标
        if icon:
            self.setIcon(icon)
            self.setIconSize(QSize(size // 2, size // 2))
        
        # 更新阴影
        self._update_shadow()
        
        self.setStyleSheet(f"""
            QPushButton#fab {{
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: {size // 2}px;
            }}
            QPushButton#fab:hover {{
                background-color: #1976D2;
            }}
            QPushButton#fab:pressed {{
                background-color: #1565C0;
            }}
            QPushButton#fab:disabled {{
                background-color: #BDBDBD;
                color: #FFFFFF;
            }}
        """)

class ToggleButton(QPushButton):
    """切换按钮"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("toggle-button")
        self.setCheckable(True)
        self.setMinimumHeight(36)
        
        self.setStyleSheet("""
            QPushButton#toggle-button {
                background-color: #F5F5F5;
                color: #757575;
                border: 1px solid #E0E0E0;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton#toggle-button:hover {
                background-color: #EEEEEE;
                border-color: #BDBDBD;
            }
            QPushButton#toggle-button:checked {
                background-color: #2196F3;
                color: white;
                border-color: #2196F3;
            }
            QPushButton#toggle-button:checked:hover {
                background-color: #1976D2;
                border-color: #1976D2;
            }
            QPushButton#toggle-button:disabled {
                background-color: #FAFAFA;
                color: #BDBDBD;
                border-color: #E0E0E0;
            }
        """)

class ButtonGroup(object):
    """按钮组"""
    
    def __init__(self):
        self.buttons = []
        self.exclusive = False
    
    def add_button(self, button):
        """添加按钮"""
        self.buttons.append(button)
        if isinstance(button, ToggleButton):
            button.toggled.connect(self._on_button_toggled)
    
    def set_exclusive(self, exclusive):
        """设置是否互斥"""
        self.exclusive = exclusive
    
    def _on_button_toggled(self, checked):
        """按钮切换事件"""
        if not self.exclusive or not checked:
            return
        
        sender = self.sender()
        for button in self.buttons:
            if button != sender and isinstance(button, ToggleButton):
                button.setChecked(False)
    
    def get_checked_button(self):
        """获取选中的按钮"""
        for button in self.buttons:
            if isinstance(button, ToggleButton) and button.isChecked():
                return button
        return None

class LoadingButton(PrimaryButton):
    """加载按钮"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self._original_text = text
        self._loading = False
        
        # 加载动画（简化版）
        self._loading_dots = 0
    
    def set_loading(self, loading):
        """设置加载状态"""
        self._loading = loading
        if loading:
            self.setEnabled(False)
            self._start_loading_animation()
        else:
            self.setEnabled(True)
            self.setText(self._original_text)
    
    def _start_loading_animation(self):
        """开始加载动画"""
        if self._loading:
            dots = "." * (self._loading_dots % 4)
            self.setText(f"加载中{dots}")
            self._loading_dots += 1
            
            # 简单的定时器模拟（实际应用中应该使用QTimer）
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(500, self._start_loading_animation)
    
    def is_loading(self):
        """是否正在加载"""
        return self._loading
