# -*- coding: utf-8 -*-
"""
主窗口 - 应用程序主界面

现代化的主窗口设计，包括：
- 响应式布局
- 侧边导航
- 主内容区
- 状态栏
- 工具栏
"""

import asyncio
import logging
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt6.QtGui import QIcon, QPixmap, QAction
from PyQt6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                            QSplitter, QStackedWidget, QStatusBar, QMenuBar,
                            QToolBar, QLabel, QProgressBar, QApplication)

from .theme import ThemeManager
from .components.card import Card, CardHeader, CardContent
from .components.button import PrimaryButton, SecondaryButton, IconButton
from .animations import animation_manager
from ..core.app_controller import AppController

logger = logging.getLogger(__name__)

class NavigationRail(QWidget):
    """导航栏"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("navigation-rail")
        self.setFixedWidth(240)
        
        # 当前选中的页面
        self.current_page = None
        self.nav_buttons = {}
        
        self._setup_ui()
        self._setup_style()
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 24, 16, 24)
        layout.setSpacing(8)
        
        # 应用标题
        title_card = Card(elevation=0)
        title_header = CardHeader("AI视频生成器", "V2.0")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        layout.addSpacing(16)
        
        # 导航按钮
        nav_items = [
            ("dashboard", "🏠", "仪表板"),
            ("text", "📝", "文本创作"),
            ("storyboard", "🎬", "分镜设计"),
            ("image", "🎨", "图像生成"),
            ("voice", "🎤", "语音制作"),
            ("video", "🎥", "视频合成"),
            ("settings", "⚙️", "设置")
        ]
        
        for page_id, icon, title in nav_items:
            btn = self._create_nav_button(page_id, icon, title)
            self.nav_buttons[page_id] = btn
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # 底部信息
        info_card = Card(elevation=0)
        info_content = CardContent()
        
        status_label = QLabel("就绪")
        status_label.setStyleSheet("color: #4CAF50; font-weight: 500;")
        info_content.add_widget(status_label)
        
        info_card.add_widget(info_content)
        layout.addWidget(info_card)
    
    def _create_nav_button(self, page_id, icon, title):
        """创建导航按钮"""
        button = SecondaryButton(f"{icon} {title}")
        button.setObjectName(f"nav-{page_id}")
        button.clicked.connect(lambda: self._on_nav_clicked(page_id))
        
        # 设置按钮样式
        button.setStyleSheet(f"""
            QPushButton#nav-{page_id} {{
                text-align: left;
                padding: 12px 16px;
                margin: 2px 0;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
            }}
            QPushButton#nav-{page_id}:hover {{
                background-color: #E3F2FD;
            }}
            QPushButton#nav-{page_id}.active {{
                background-color: #2196F3;
                color: white;
                border-color: #2196F3;
            }}
        """)
        
        return button
    
    def _on_nav_clicked(self, page_id):
        """导航按钮点击事件"""
        self.set_current_page(page_id)
        self.page_changed.emit(page_id)
    
    def set_current_page(self, page_id):
        """设置当前页面"""
        # 重置所有按钮状态
        for btn_id, btn in self.nav_buttons.items():
            if btn_id == page_id:
                btn.setProperty("class", "active")
            else:
                btn.setProperty("class", "")
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        
        self.current_page = page_id
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget#navigation-rail {
                background-color: #F8F9FA;
                border-right: 1px solid #E0E0E0;
            }
        """)

class ContentArea(QStackedWidget):
    """内容区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("content-area")
        
        # 页面字典
        self.pages = {}
        
        self._setup_pages()
    
    def _setup_pages(self):
        """设置页面"""
        # 仪表板页面
        dashboard_page = self._create_dashboard_page()
        self.add_page("dashboard", dashboard_page)
        
        # 文本创作页面
        text_page = self._create_text_page()
        self.add_page("text", text_page)
        
        # 分镜设计页面
        storyboard_page = self._create_storyboard_page()
        self.add_page("storyboard", storyboard_page)
        
        # 图像生成页面
        image_page = self._create_image_page()
        self.add_page("image", image_page)
        
        # 语音制作页面
        voice_page = self._create_voice_page()
        self.add_page("voice", voice_page)
        
        # 视频合成页面
        video_page = self._create_video_page()
        self.add_page("video", video_page)
        
        # 设置页面
        settings_page = self._create_settings_page()
        self.add_page("settings", settings_page)
    
    def add_page(self, page_id, widget):
        """添加页面"""
        self.pages[page_id] = widget
        self.addWidget(widget)
    
    def show_page(self, page_id):
        """显示页面"""
        if page_id in self.pages:
            widget = self.pages[page_id]
            self.setCurrentWidget(widget)
            
            # 添加页面切换动画
            animation_manager.fade_in(widget, duration=200)
    
    def _create_dashboard_page(self):
        """创建仪表板页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)
        
        # 页面标题
        title_card = Card()
        title_header = CardHeader("仪表板", "项目概览和快速操作")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        # 统计卡片
        stats_layout = QHBoxLayout()
        
        # 项目统计
        project_card = Card()
        project_header = CardHeader("项目数量")
        project_content = CardContent()
        project_count = QLabel("0")
        project_count.setStyleSheet("font-size: 32px; font-weight: bold; color: #2196F3;")
        project_content.add_widget(project_count)
        project_card.add_widget(project_header)
        project_card.add_widget(project_content)
        stats_layout.addWidget(project_card)
        
        # 生成统计
        generation_card = Card()
        generation_header = CardHeader("今日生成")
        generation_content = CardContent()
        generation_count = QLabel("0")
        generation_count.setStyleSheet("font-size: 32px; font-weight: bold; color: #4CAF50;")
        generation_content.add_widget(generation_count)
        generation_card.add_widget(generation_header)
        generation_card.add_widget(generation_content)
        stats_layout.addWidget(generation_card)
        
        layout.addLayout(stats_layout)
        
        # 快速操作
        actions_card = Card()
        actions_header = CardHeader("快速操作")
        actions_content = CardContent()
        
        actions_layout = QHBoxLayout()
        new_project_btn = PrimaryButton("新建项目")
        open_project_btn = SecondaryButton("打开项目")
        actions_layout.addWidget(new_project_btn)
        actions_layout.addWidget(open_project_btn)
        actions_layout.addStretch()
        
        actions_content.add_layout(actions_layout)
        actions_card.add_widget(actions_header)
        actions_card.add_widget(actions_content)
        layout.addWidget(actions_card)
        
        layout.addStretch()
        
        return page
    
    def _create_text_page(self):
        """创建文本创作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        # 页面标题
        title_card = Card()
        title_header = CardHeader("文本创作", "输入或生成故事文本")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        # 占位内容
        content_card = Card()
        content_header = CardHeader("文本编辑器")
        content_card.add_widget(content_header)
        layout.addWidget(content_card)
        
        return page
    
    def _create_storyboard_page(self):
        """创建分镜设计页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title_card = Card()
        title_header = CardHeader("分镜设计", "五阶段智能分镜生成")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        return page
    
    def _create_image_page(self):
        """创建图像生成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title_card = Card()
        title_header = CardHeader("图像生成", "AI图像生成和管理")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        return page
    
    def _create_voice_page(self):
        """创建语音制作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title_card = Card()
        title_header = CardHeader("语音制作", "AI语音合成和配音")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        return page
    
    def _create_video_page(self):
        """创建视频合成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title_card = Card()
        title_header = CardHeader("视频合成", "自动化视频制作")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        return page
    
    def _create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(24, 24, 24, 24)
        
        title_card = Card()
        title_header = CardHeader("设置", "应用程序配置和偏好")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        return page

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self, app_controller: AppController = None):
        super().__init__()
        self.app_controller = app_controller
        self.theme_manager = ThemeManager()
        
        # 设置窗口属性
        self.setWindowTitle("AI视频生成器 V2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口图标
        # self.setWindowIcon(QIcon("assets/icons/app_icon.png"))
        
        self._setup_ui()
        self._setup_connections()
        self._setup_status_bar()
        
        # 应用主题
        self.theme_manager.theme_changed.connect(self._on_theme_changed)
        
        # 显示仪表板
        self.navigation_rail.set_current_page("dashboard")
        self.content_area.show_page("dashboard")
    
    def _setup_ui(self):
        """设置界面"""
        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 导航栏
        self.navigation_rail = NavigationRail()
        splitter.addWidget(self.navigation_rail)
        
        # 内容区域
        self.content_area = ContentArea()
        splitter.addWidget(self.content_area)
        
        # 设置分割器比例
        splitter.setSizes([240, 1160])
        splitter.setCollapsible(0, False)  # 导航栏不可折叠
        splitter.setCollapsible(1, False)  # 内容区不可折叠
    
    def _setup_connections(self):
        """设置连接"""
        # 导航切换
        self.navigation_rail.page_changed.connect(self.content_area.show_page)
    
    def _setup_status_bar(self):
        """设置状态栏"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)
        
        # 版本信息
        version_label = QLabel("V2.0.0")
        version_label.setStyleSheet("color: #757575;")
        status_bar.addPermanentWidget(version_label)
    
    def _on_theme_changed(self, theme):
        """主题变更事件"""
        logger.info(f"主题已切换到: {theme.name}")
    
    def show_progress(self, message="处理中...", progress=None):
        """显示进度"""
        self.status_label.setText(message)
        self.progress_bar.setVisible(True)
        
        if progress is not None:
            self.progress_bar.setValue(progress)
        else:
            # 不确定进度
            self.progress_bar.setRange(0, 0)
    
    def hide_progress(self):
        """隐藏进度"""
        self.progress_bar.setVisible(False)
        self.progress_bar.setRange(0, 100)
        self.status_label.setText("就绪")
    
    def set_status(self, message):
        """设置状态消息"""
        self.status_label.setText(message)
    
    def closeEvent(self, event):
        """关闭事件"""
        # 这里可以添加保存数据、清理资源等操作
        if self.app_controller:
            # 异步关闭应用控制器
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.app_controller.shutdown())
            loop.close()
        
        event.accept()
