#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 V2.0 - 真正可用版本
简单直接，专注功能
"""

import sys
import os
import json
import asyncio
import aiohttp
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QMessageBox, QProgressBar, 
    QFileDialog, QTabWidget, QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.api_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
    
    async def generate_text(self, prompt, model="glm-4"):
        """生成文本"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        return f"API调用失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"
    
    async def generate_image(self, prompt, model="cogview-3"):
        """生成图像"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "prompt": prompt
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}images/generations",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["data"][0]["url"]
                    else:
                        return f"图像生成失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"

class WorkerThread(QThread):
    """工作线程"""
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    
    def __init__(self, ai_service, prompt, task_type):
        super().__init__()
        self.ai_service = ai_service
        self.prompt = prompt
        self.task_type = task_type
    
    def run(self):
        """运行任务"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.task_type == "story":
                result = loop.run_until_complete(
                    self.ai_service.generate_text(f"请创作一个有趣的故事：{self.prompt}")
                )
            elif self.task_type == "storyboard":
                result = loop.run_until_complete(
                    self.ai_service.generate_text(f"请将以下故事分解为详细的分镜脚本：\n\n{self.prompt}")
                )
            elif self.task_type == "image":
                result = loop.run_until_complete(
                    self.ai_service.generate_image(self.prompt)
                )
            else:
                result = loop.run_until_complete(
                    self.ai_service.generate_text(self.prompt)
                )
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class SimpleVideoMaker(QMainWindow):
    """简单可用的AI视频生成器"""
    
    def __init__(self):
        super().__init__()
        self.ai_service = AIService()
        self.worker = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("AI视频生成器 V2.0 - 可用版本")
        self.setGeometry(100, 100, 1000, 700)
        
        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎬 AI视频生成器")
        title.setFont(QFont("Microsoft YaHei", 20, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("color: #2c3e50; padding: 20px; background-color: #ecf0f1; border-radius: 10px; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 创建标签页
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
        """)
        
        # 添加功能页面
        self.tabs.addTab(self.create_story_page(), "📝 文本创作")
        self.tabs.addTab(self.create_storyboard_page(), "🎬 分镜设计")
        self.tabs.addTab(self.create_image_page(), "🎨 图像生成")
        self.tabs.addTab(self.create_info_page(), "ℹ️ 使用说明")
        
        layout.addWidget(self.tabs)
        
        # 状态栏
        self.statusBar().showMessage("就绪 - 所有功能可用")
    
    def create_story_page(self):
        """创建故事页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 输入区域
        layout.addWidget(QLabel("📝 输入故事主题："))
        self.story_input = QTextEdit()
        self.story_input.setMaximumHeight(80)
        self.story_input.setPlaceholderText("例如：一个关于时间旅行的科幻故事...")
        self.story_input.setStyleSheet("font-size: 12pt; padding: 10px; border: 2px solid #bdc3c7; border-radius: 5px;")
        layout.addWidget(self.story_input)
        
        # 按钮
        btn_layout = QHBoxLayout()
        self.generate_story_btn = QPushButton("🚀 生成故事")
        self.generate_story_btn.clicked.connect(self.generate_story)
        self.generate_story_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14pt;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        btn_layout.addWidget(self.generate_story_btn)
        
        self.save_story_btn = QPushButton("💾 保存故事")
        self.save_story_btn.clicked.connect(self.save_story)
        self.save_story_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14pt;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_layout.addWidget(self.save_story_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # 进度条
        self.story_progress = QProgressBar()
        self.story_progress.setVisible(False)
        self.story_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.story_progress)
        
        # 输出区域
        layout.addWidget(QLabel("📖 生成的故事："))
        self.story_output = QTextEdit()
        self.story_output.setStyleSheet("font-size: 11pt; padding: 15px; border: 2px solid #bdc3c7; border-radius: 5px; line-height: 1.6;")
        layout.addWidget(self.story_output)
        
        return page
    
    def create_storyboard_page(self):
        """创建分镜页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 输入区域
        layout.addWidget(QLabel("🎬 输入故事文本："))
        self.storyboard_input = QTextEdit()
        self.storyboard_input.setMaximumHeight(120)
        self.storyboard_input.setPlaceholderText("粘贴您的故事文本，AI将生成详细分镜脚本...")
        self.storyboard_input.setStyleSheet("font-size: 12pt; padding: 10px; border: 2px solid #bdc3c7; border-radius: 5px;")
        layout.addWidget(self.storyboard_input)
        
        # 按钮
        btn_layout = QHBoxLayout()
        self.generate_storyboard_btn = QPushButton("🎬 生成分镜")
        self.generate_storyboard_btn.clicked.connect(self.generate_storyboard)
        self.generate_storyboard_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14pt;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        btn_layout.addWidget(self.generate_storyboard_btn)
        
        self.save_storyboard_btn = QPushButton("💾 保存分镜")
        self.save_storyboard_btn.clicked.connect(self.save_storyboard)
        self.save_storyboard_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14pt;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_layout.addWidget(self.save_storyboard_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # 进度条
        self.storyboard_progress = QProgressBar()
        self.storyboard_progress.setVisible(False)
        self.storyboard_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #e74c3c;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.storyboard_progress)
        
        # 输出区域
        layout.addWidget(QLabel("🎭 生成的分镜脚本："))
        self.storyboard_output = QTextEdit()
        self.storyboard_output.setStyleSheet("font-size: 11pt; padding: 15px; border: 2px solid #bdc3c7; border-radius: 5px; line-height: 1.6;")
        layout.addWidget(self.storyboard_output)
        
        return page

    def create_image_page(self):
        """创建图像生成页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 输入区域
        layout.addWidget(QLabel("🎨 输入图像描述："))
        self.image_input = QTextEdit()
        self.image_input.setMaximumHeight(80)
        self.image_input.setPlaceholderText("例如：一个美丽的日落场景，山峦起伏...")
        self.image_input.setStyleSheet("font-size: 12pt; padding: 10px; border: 2px solid #bdc3c7; border-radius: 5px;")
        layout.addWidget(self.image_input)

        # 按钮
        btn_layout = QHBoxLayout()
        self.generate_image_btn = QPushButton("🎨 生成图像")
        self.generate_image_btn.clicked.connect(self.generate_image)
        self.generate_image_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14pt;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        btn_layout.addWidget(self.generate_image_btn)

        btn_layout.addStretch()
        layout.addLayout(btn_layout)

        # 进度条
        self.image_progress = QProgressBar()
        self.image_progress.setVisible(False)
        self.image_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #9b59b6;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.image_progress)

        # 输出区域
        layout.addWidget(QLabel("🖼️ 生成的图像URL："))
        self.image_output = QTextEdit()
        self.image_output.setMaximumHeight(200)
        self.image_output.setStyleSheet("font-size: 11pt; padding: 15px; border: 2px solid #bdc3c7; border-radius: 5px;")
        layout.addWidget(self.image_output)

        return page

    def create_info_page(self):
        """创建使用说明页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setStyleSheet("font-size: 12pt; padding: 20px; border: 2px solid #bdc3c7; border-radius: 5px; line-height: 1.8;")
        info_text.setHtml("""
        <h2>🎬 AI视频生成器使用说明</h2>

        <h3>📝 文本创作</h3>
        <p>1. 在输入框中输入故事主题或关键词</p>
        <p>2. 点击"生成故事"按钮</p>
        <p>3. AI将为您创作完整的故事</p>
        <p>4. 可以保存故事到本地文件</p>

        <h3>🎬 分镜设计</h3>
        <p>1. 将故事文本粘贴到输入框</p>
        <p>2. 点击"生成分镜"按钮</p>
        <p>3. AI将生成详细的分镜脚本</p>
        <p>4. 可以保存分镜脚本到本地文件</p>

        <h3>🎨 图像生成</h3>
        <p>1. 输入图像描述</p>
        <p>2. 点击"生成图像"按钮</p>
        <p>3. AI将生成图像并返回URL</p>
        <p>4. 可以通过URL查看和下载图像</p>

        <h3>💡 使用技巧</h3>
        <p>• 描述越详细，生成效果越好</p>
        <p>• 可以多次生成，选择最满意的结果</p>
        <p>• 建议按顺序使用：故事 → 分镜 → 图像</p>

        <h3>⚙️ 技术信息</h3>
        <p>• 使用智谱AI GLM-4模型进行文本生成</p>
        <p>• 使用CogView-3模型进行图像生成</p>
        <p>• 所有功能都已配置好API密钥，可直接使用</p>
        """)
        layout.addWidget(info_text)

        return page

    # ==================== 功能方法 ====================

    def generate_story(self):
        """生成故事"""
        prompt = self.story_input.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请输入故事主题或关键词")
            return

        self.generate_story_btn.setEnabled(False)
        self.story_progress.setVisible(True)
        self.story_progress.setRange(0, 0)
        self.statusBar().showMessage("正在生成故事，请稍候...")

        self.worker = WorkerThread(self.ai_service, prompt, "story")
        self.worker.finished.connect(self.on_story_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()

    def on_story_finished(self, result):
        """故事生成完成"""
        self.story_output.setPlainText(result)
        self.generate_story_btn.setEnabled(True)
        self.story_progress.setVisible(False)
        self.statusBar().showMessage("故事生成完成！")

        # 自动复制到分镜页面
        self.storyboard_input.setPlainText(result)
        QMessageBox.information(self, "成功", "故事生成完成！已自动复制到分镜设计页面。")

    def save_story(self):
        """保存故事"""
        content = self.story_output.toPlainText()
        if not content:
            QMessageBox.warning(self, "警告", "没有内容可保存")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存故事", "story.txt", "文本文件 (*.txt)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                QMessageBox.information(self, "成功", f"故事已保存到：{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")

    def generate_storyboard(self):
        """生成分镜"""
        story_text = self.storyboard_input.toPlainText().strip()
        if not story_text:
            QMessageBox.warning(self, "警告", "请输入故事文本")
            return

        self.generate_storyboard_btn.setEnabled(False)
        self.storyboard_progress.setVisible(True)
        self.storyboard_progress.setRange(0, 0)
        self.statusBar().showMessage("正在生成分镜脚本，请稍候...")

        self.worker = WorkerThread(self.ai_service, story_text, "storyboard")
        self.worker.finished.connect(self.on_storyboard_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()

    def on_storyboard_finished(self, result):
        """分镜生成完成"""
        self.storyboard_output.setPlainText(result)
        self.generate_storyboard_btn.setEnabled(True)
        self.storyboard_progress.setVisible(False)
        self.statusBar().showMessage("分镜脚本生成完成！")
        QMessageBox.information(self, "成功", "分镜脚本生成完成！")

    def save_storyboard(self):
        """保存分镜"""
        content = self.storyboard_output.toPlainText()
        if not content:
            QMessageBox.warning(self, "警告", "没有内容可保存")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存分镜脚本", "storyboard.txt", "文本文件 (*.txt)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                QMessageBox.information(self, "成功", f"分镜脚本已保存到：{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")

    def generate_image(self):
        """生成图像"""
        prompt = self.image_input.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请输入图像描述")
            return

        self.generate_image_btn.setEnabled(False)
        self.image_progress.setVisible(True)
        self.image_progress.setRange(0, 0)
        self.statusBar().showMessage("正在生成图像，请稍候...")

        self.worker = WorkerThread(self.ai_service, prompt, "image")
        self.worker.finished.connect(self.on_image_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()

    def on_image_finished(self, result):
        """图像生成完成"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.image_output.append(f"生成时间：{current_time}")
        self.image_output.append(f"图像URL：{result}")
        self.image_output.append("=" * 50)

        self.generate_image_btn.setEnabled(True)
        self.image_progress.setVisible(False)
        self.statusBar().showMessage("图像生成完成！")

        if result.startswith("http"):
            QMessageBox.information(self, "成功", f"图像生成完成！\n\nURL：{result}\n\n您可以复制URL到浏览器查看图像。")
        else:
            QMessageBox.warning(self, "注意", f"生成结果：{result}")

    def on_error(self, error_msg):
        """处理错误"""
        QMessageBox.critical(self, "错误", f"操作失败：{error_msg}")

        # 重置所有按钮状态
        self.generate_story_btn.setEnabled(True)
        self.generate_storyboard_btn.setEnabled(True)
        self.generate_image_btn.setEnabled(True)

        # 隐藏所有进度条
        self.story_progress.setVisible(False)
        self.storyboard_progress.setVisible(False)
        self.image_progress.setVisible(False)

        self.statusBar().showMessage("操作失败")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用信息
    app.setApplicationName("AI视频生成器 V2.0")
    app.setApplicationVersion("2.0.0")

    # 创建主窗口
    window = SimpleVideoMaker()
    window.show()

    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
