# -*- coding: utf-8 -*-
"""
项目模型 - 项目数据结构和管理

定义项目的数据结构和操作方法：
- 项目元数据
- 项目状态
- 项目文件管理
- 数据序列化
"""

import json
import uuid
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class ProjectStatus(Enum):
    """项目状态"""
    CREATED = "created"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ARCHIVED = "archived"
    ERROR = "error"

@dataclass
class ProjectMetadata:
    """项目元数据"""
    project_id: str
    name: str
    description: str = ""
    version: str = "2.0.0"
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    author: str = ""
    tags: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectMetadata':
        """从字典创建"""
        data = data.copy()
        if 'created_at' in data:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)

@dataclass
class ProjectSettings:
    """项目设置"""
    # 视频设置
    video_width: int = 1920
    video_height: int = 1080
    video_fps: int = 30
    video_duration: float = 0.0
    
    # 音频设置
    audio_sample_rate: int = 44100
    audio_channels: int = 2
    audio_bitrate: int = 192
    
    # 生成设置
    default_llm_provider: str = "zhipu"
    default_image_provider: str = "cogview"
    default_voice_provider: str = "azure"
    default_video_provider: str = "cogvideo"
    
    # 质量设置
    image_quality: str = "high"
    voice_quality: str = "high"
    video_quality: str = "high"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectSettings':
        """从字典创建"""
        return cls(**data)

@dataclass
class ProjectData:
    """项目数据"""
    # 原始文本
    original_text: str = ""
    rewritten_text: str = ""
    
    # 分镜数据
    storyboard_data: Optional[Dict[str, Any]] = None
    
    # 媒体资源
    images: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    audio_files: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    video_files: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # 工作流数据
    workflow_history: List[Dict[str, Any]] = field(default_factory=list)
    current_stage: str = "text"
    
    # 缓存数据
    cache_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectData':
        """从字典创建"""
        return cls(**data)

class Project:
    """项目类"""
    
    def __init__(self, 
                 metadata: ProjectMetadata,
                 settings: ProjectSettings = None,
                 data: ProjectData = None,
                 project_dir: Optional[Path] = None):
        self.metadata = metadata
        self.settings = settings or ProjectSettings()
        self.data = data or ProjectData()
        self.project_dir = project_dir
        
        # 状态
        self.status = ProjectStatus.CREATED
        self._dirty = False  # 是否有未保存的更改
        
        # 文件路径
        self._project_file: Optional[Path] = None
        self._assets_dir: Optional[Path] = None
        self._cache_dir: Optional[Path] = None
    
    @classmethod
    def create_new(cls, 
                   name: str,
                   description: str = "",
                   base_dir: str = "projects",
                   author: str = "") -> 'Project':
        """创建新项目"""
        try:
            # 生成项目ID
            project_id = str(uuid.uuid4())
            
            # 创建元数据
            metadata = ProjectMetadata(
                project_id=project_id,
                name=name,
                description=description,
                author=author
            )
            
            # 创建项目目录
            base_path = Path(base_dir)
            project_dir = base_path / f"{name}_{project_id[:8]}"
            project_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建项目实例
            project = cls(metadata=metadata, project_dir=project_dir)
            project._setup_directories()
            
            # 保存项目
            project.save()
            
            logger.info(f"新项目已创建: {name} ({project_id})")
            return project
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            raise
    
    @classmethod
    def load_from_file(cls, project_file: str) -> 'Project':
        """从文件加载项目"""
        try:
            project_path = Path(project_file)
            
            if not project_path.exists():
                raise FileNotFoundError(f"项目文件不存在: {project_file}")
            
            # 读取项目文件
            with open(project_path, 'r', encoding='utf-8') as f:
                project_json = json.load(f)
            
            # 解析数据
            metadata = ProjectMetadata.from_dict(project_json['metadata'])
            settings = ProjectSettings.from_dict(project_json.get('settings', {}))
            data = ProjectData.from_dict(project_json.get('data', {}))
            
            # 创建项目实例
            project_dir = project_path.parent
            project = cls(
                metadata=metadata,
                settings=settings,
                data=data,
                project_dir=project_dir
            )
            
            project._project_file = project_path
            project.status = ProjectStatus(project_json.get('status', 'created'))
            project._setup_directories()
            
            logger.info(f"项目已加载: {metadata.name} ({metadata.project_id})")
            return project
            
        except Exception as e:
            logger.error(f"加载项目失败: {e}")
            raise
    
    def _setup_directories(self):
        """设置项目目录结构"""
        if not self.project_dir:
            return
        
        # 创建子目录
        self._assets_dir = self.project_dir / "assets"
        self._cache_dir = self.project_dir / "cache"
        
        # 创建资源子目录
        (self._assets_dir / "images").mkdir(parents=True, exist_ok=True)
        (self._assets_dir / "audio").mkdir(parents=True, exist_ok=True)
        (self._assets_dir / "video").mkdir(parents=True, exist_ok=True)
        (self._assets_dir / "exports").mkdir(parents=True, exist_ok=True)
        
        # 创建缓存目录
        self._cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置项目文件路径
        if not self._project_file:
            self._project_file = self.project_dir / "project.json"
    
    def save(self) -> bool:
        """保存项目"""
        try:
            if not self._project_file:
                raise ValueError("项目文件路径未设置")
            
            # 更新时间戳
            self.metadata.updated_at = datetime.now()
            
            # 构建保存数据
            save_data = {
                'metadata': self.metadata.to_dict(),
                'settings': self.settings.to_dict(),
                'data': self.data.to_dict(),
                'status': self.status.value,
                'version': '2.0.0'
            }
            
            # 保存到文件
            with open(self._project_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            self._dirty = False
            logger.info(f"项目已保存: {self.metadata.name}")
            return True
            
        except Exception as e:
            logger.error(f"保存项目失败: {e}")
            return False
    
    def export_data(self, export_path: str) -> bool:
        """导出项目数据"""
        try:
            export_file = Path(export_path)
            export_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 构建导出数据
            export_data = {
                'metadata': self.metadata.to_dict(),
                'settings': self.settings.to_dict(),
                'data': self.data.to_dict(),
                'status': self.status.value,
                'exported_at': datetime.now().isoformat(),
                'version': '2.0.0'
            }
            
            # 保存导出文件
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"项目数据已导出: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出项目数据失败: {e}")
            return False
    
    def get_asset_path(self, asset_type: str, filename: str) -> Path:
        """获取资源文件路径"""
        if not self._assets_dir:
            raise ValueError("资源目录未设置")
        
        asset_dir = self._assets_dir / asset_type
        return asset_dir / filename
    
    def get_cache_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        if not self._cache_dir:
            raise ValueError("缓存目录未设置")
        
        return self._cache_dir / f"{cache_key}.json"
    
    def add_image(self, image_id: str, image_data: Dict[str, Any]):
        """添加图像资源"""
        self.data.images[image_id] = image_data
        self._dirty = True
    
    def add_audio(self, audio_id: str, audio_data: Dict[str, Any]):
        """添加音频资源"""
        self.data.audio_files[audio_id] = audio_data
        self._dirty = True
    
    def add_video(self, video_id: str, video_data: Dict[str, Any]):
        """添加视频资源"""
        self.data.video_files[video_id] = video_data
        self._dirty = True
    
    def set_storyboard_data(self, storyboard_data: Dict[str, Any]):
        """设置分镜数据"""
        self.data.storyboard_data = storyboard_data
        self._dirty = True
    
    def set_current_stage(self, stage: str):
        """设置当前阶段"""
        self.data.current_stage = stage
        self._dirty = True
    
    def add_workflow_record(self, workflow_data: Dict[str, Any]):
        """添加工作流记录"""
        workflow_record = {
            'timestamp': datetime.now().isoformat(),
            'data': workflow_data
        }
        self.data.workflow_history.append(workflow_record)
        self._dirty = True
    
    def set_cache_data(self, key: str, value: Any):
        """设置缓存数据"""
        self.data.cache_data[key] = value
        self._dirty = True
    
    def get_cache_data(self, key: str, default: Any = None) -> Any:
        """获取缓存数据"""
        return self.data.cache_data.get(key, default)
    
    def update_status(self, status: ProjectStatus):
        """更新项目状态"""
        self.status = status
        self._dirty = True
    
    def is_dirty(self) -> bool:
        """是否有未保存的更改"""
        return self._dirty
    
    def get_project_info(self) -> Dict[str, Any]:
        """获取项目信息"""
        return {
            'id': self.metadata.project_id,
            'name': self.metadata.name,
            'description': self.metadata.description,
            'status': self.status.value,
            'created_at': self.metadata.created_at.isoformat(),
            'updated_at': self.metadata.updated_at.isoformat(),
            'author': self.metadata.author,
            'tags': self.metadata.tags,
            'current_stage': self.data.current_stage,
            'has_storyboard': self.data.storyboard_data is not None,
            'image_count': len(self.data.images),
            'audio_count': len(self.data.audio_files),
            'video_count': len(self.data.video_files),
            'project_dir': str(self.project_dir) if self.project_dir else None
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'metadata': self.metadata.to_dict(),
            'settings': self.settings.to_dict(),
            'data': self.data.to_dict(),
            'status': self.status.value,
            'project_dir': str(self.project_dir) if self.project_dir else None
        }
    
    def __str__(self) -> str:
        return f"Project({self.metadata.name}, {self.metadata.project_id[:8]})"
    
    def __repr__(self) -> str:
        return self.__str__()
