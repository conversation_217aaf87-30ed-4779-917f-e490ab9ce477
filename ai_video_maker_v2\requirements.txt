# AI视频生成器 V2.0 依赖包

# 核心框架
PyQt6>=6.5.0
PyQt6-WebEngine>=6.5.0

# 异步处理
asyncio-mqtt>=0.13.0
aiohttp>=3.8.0
aiofiles>=23.0.0

# 图像处理
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# 音频处理
pydub>=0.25.0
librosa>=0.10.0
soundfile>=0.12.0

# 视频处理
moviepy>=1.0.3
ffmpeg-python>=0.2.0

# HTTP请求
requests>=2.31.0
httpx>=0.24.0

# 数据处理
pandas>=2.0.0
pydantic>=2.0.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# 日志和监控
loguru>=0.7.0
psutil>=5.9.0

# 开发工具
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# 可选依赖（根据需要安装）
# torch>=2.0.0  # 如果需要本地AI模型
# transformers>=4.30.0  # 如果需要Hugging Face模型
# openai>=1.0.0  # 如果使用OpenAI API
# azure-cognitiveservices-speech>=1.30.0  # 如果使用Azure语音服务
