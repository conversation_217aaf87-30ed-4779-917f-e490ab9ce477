# -*- coding: utf-8 -*-
"""
事件系统 - 应用程序事件管理

提供类型安全的事件系统，支持：
- 事件发布/订阅
- 异步事件处理
- 事件过滤
- 事件历史记录
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union
from weakref import WeakSet
import uuid

logger = logging.getLogger(__name__)

class EventType(Enum):
    """事件类型"""
    # 应用程序事件
    APP_STARTED = "app_started"
    APP_SHUTDOWN = "app_shutdown"
    
    # 项目事件
    PROJECT_CREATED = "project_created"
    PROJECT_LOADED = "project_loaded"
    PROJECT_SAVED = "project_saved"
    PROJECT_CLOSED = "project_closed"
    
    # 工作流事件
    WORKFLOW_STARTED = "workflow_started"
    WORKFLOW_COMPLETED = "workflow_completed"
    WORKFLOW_FAILED = "workflow_failed"
    WORKFLOW_PROGRESS = "workflow_progress"
    
    # 任务事件
    TASK_STARTED = "task_started"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    TASK_PROGRESS = "task_progress"
    
    # UI事件
    UI_THEME_CHANGED = "ui_theme_changed"
    UI_LANGUAGE_CHANGED = "ui_language_changed"
    UI_WINDOW_RESIZED = "ui_window_resized"
    
    # 服务事件
    SERVICE_CONNECTED = "service_connected"
    SERVICE_DISCONNECTED = "service_disconnected"
    SERVICE_ERROR = "service_error"

@dataclass
class Event:
    """事件数据类"""
    event_type: EventType
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source: Optional[str] = None
    
    def __str__(self) -> str:
        return f"Event({self.event_type.value}, {self.event_id[:8]})"

EventHandler = Callable[[Event], Union[None, asyncio.Future]]

class EventFilter:
    """事件过滤器"""
    
    def __init__(self, 
                 event_types: Optional[Set[EventType]] = None,
                 source_filter: Optional[str] = None,
                 data_filter: Optional[Callable[[Dict[str, Any]], bool]] = None):
        self.event_types = event_types
        self.source_filter = source_filter
        self.data_filter = data_filter
    
    def matches(self, event: Event) -> bool:
        """检查事件是否匹配过滤条件"""
        if self.event_types and event.event_type not in self.event_types:
            return False
            
        if self.source_filter and event.source != self.source_filter:
            return False
            
        if self.data_filter and not self.data_filter(event.data):
            return False
            
        return True

class EventSubscription:
    """事件订阅"""
    
    def __init__(self, 
                 handler: EventHandler,
                 event_filter: Optional[EventFilter] = None,
                 priority: int = 0,
                 once: bool = False):
        self.handler = handler
        self.filter = event_filter
        self.priority = priority
        self.once = once
        self.subscription_id = str(uuid.uuid4())
        self.created_at = datetime.now()
        self.call_count = 0
    
    def should_handle(self, event: Event) -> bool:
        """检查是否应该处理此事件"""
        if self.filter:
            return self.filter.matches(event)
        return True
    
    async def handle_event(self, event: Event):
        """处理事件"""
        try:
            self.call_count += 1
            
            if asyncio.iscoroutinefunction(self.handler):
                await self.handler(event)
            else:
                self.handler(event)
                
        except Exception as e:
            logger.error(f"事件处理器执行失败: {e}", exc_info=True)

class EventSystem:
    """事件系统"""
    
    def __init__(self, max_history: int = 1000):
        self._subscriptions: Dict[EventType, List[EventSubscription]] = {}
        self._global_subscriptions: List[EventSubscription] = []
        self._event_history: List[Event] = []
        self._max_history = max_history
        self._processing_queue = asyncio.Queue()
        self._processing_task: Optional[asyncio.Task] = None
        self._shutdown = False
        
    async def start(self):
        """启动事件系统"""
        if self._processing_task is None:
            self._processing_task = asyncio.create_task(self._process_events())
            logger.info("事件系统已启动")
    
    async def shutdown(self):
        """关闭事件系统"""
        self._shutdown = True
        
        if self._processing_task:
            await self._processing_queue.put(None)  # 发送停止信号
            await self._processing_task
            self._processing_task = None
            
        logger.info("事件系统已关闭")
    
    def subscribe(self, 
                  event_type: EventType,
                  handler: EventHandler,
                  priority: int = 0,
                  once: bool = False,
                  event_filter: Optional[EventFilter] = None) -> str:
        """订阅事件"""
        subscription = EventSubscription(handler, event_filter, priority, once)
        
        if event_type not in self._subscriptions:
            self._subscriptions[event_type] = []
            
        self._subscriptions[event_type].append(subscription)
        # 按优先级排序（高优先级先执行）
        self._subscriptions[event_type].sort(key=lambda s: s.priority, reverse=True)
        
        logger.debug(f"订阅事件: {event_type.value}, 订阅ID: {subscription.subscription_id}")
        return subscription.subscription_id
    
    def subscribe_all(self,
                      handler: EventHandler,
                      priority: int = 0,
                      event_filter: Optional[EventFilter] = None) -> str:
        """订阅所有事件"""
        subscription = EventSubscription(handler, event_filter, priority, False)
        self._global_subscriptions.append(subscription)
        self._global_subscriptions.sort(key=lambda s: s.priority, reverse=True)
        
        logger.debug(f"订阅所有事件, 订阅ID: {subscription.subscription_id}")
        return subscription.subscription_id
    
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅"""
        # 在特定事件订阅中查找
        for event_type, subscriptions in self._subscriptions.items():
            for i, subscription in enumerate(subscriptions):
                if subscription.subscription_id == subscription_id:
                    subscriptions.pop(i)
                    logger.debug(f"取消订阅: {event_type.value}, 订阅ID: {subscription_id}")
                    return True
        
        # 在全局订阅中查找
        for i, subscription in enumerate(self._global_subscriptions):
            if subscription.subscription_id == subscription_id:
                self._global_subscriptions.pop(i)
                logger.debug(f"取消全局订阅, 订阅ID: {subscription_id}")
                return True
        
        logger.warning(f"未找到订阅ID: {subscription_id}")
        return False
    
    async def emit(self, event: Event):
        """发布事件（异步）"""
        if self._shutdown:
            return
            
        await self._processing_queue.put(event)
    
    def emit_sync(self, event: Event):
        """发布事件（同步）"""
        if self._shutdown:
            return
            
        # 直接处理事件（同步模式）
        asyncio.create_task(self._handle_event(event))
    
    async def _process_events(self):
        """事件处理循环"""
        while not self._shutdown:
            try:
                event = await self._processing_queue.get()
                
                if event is None:  # 停止信号
                    break
                    
                await self._handle_event(event)
                
            except Exception as e:
                logger.error(f"事件处理循环错误: {e}", exc_info=True)
    
    async def _handle_event(self, event: Event):
        """处理单个事件"""
        try:
            # 添加到历史记录
            self._add_to_history(event)
            
            # 处理特定事件类型的订阅
            subscriptions = self._subscriptions.get(event.event_type, [])
            await self._execute_subscriptions(subscriptions, event)
            
            # 处理全局订阅
            await self._execute_subscriptions(self._global_subscriptions, event)
            
            logger.debug(f"事件处理完成: {event}")
            
        except Exception as e:
            logger.error(f"处理事件失败: {event}, 错误: {e}", exc_info=True)
    
    async def _execute_subscriptions(self, subscriptions: List[EventSubscription], event: Event):
        """执行订阅处理器"""
        to_remove = []
        
        for subscription in subscriptions:
            if subscription.should_handle(event):
                try:
                    await subscription.handle_event(event)
                    
                    # 如果是一次性订阅，标记为删除
                    if subscription.once:
                        to_remove.append(subscription)
                        
                except Exception as e:
                    logger.error(f"订阅处理器执行失败: {e}", exc_info=True)
        
        # 移除一次性订阅
        for subscription in to_remove:
            if subscription in subscriptions:
                subscriptions.remove(subscription)
    
    def _add_to_history(self, event: Event):
        """添加事件到历史记录"""
        self._event_history.append(event)
        
        # 限制历史记录大小
        if len(self._event_history) > self._max_history:
            self._event_history = self._event_history[-self._max_history:]
    
    def get_event_history(self, 
                          event_type: Optional[EventType] = None,
                          limit: Optional[int] = None) -> List[Event]:
        """获取事件历史"""
        history = self._event_history
        
        if event_type:
            history = [e for e in history if e.event_type == event_type]
        
        if limit:
            history = history[-limit:]
            
        return history
    
    def get_subscription_stats(self) -> Dict[str, Any]:
        """获取订阅统计信息"""
        total_subscriptions = sum(len(subs) for subs in self._subscriptions.values())
        total_subscriptions += len(self._global_subscriptions)
        
        return {
            'total_subscriptions': total_subscriptions,
            'event_type_subscriptions': {
                event_type.value: len(subs) 
                for event_type, subs in self._subscriptions.items()
            },
            'global_subscriptions': len(self._global_subscriptions),
            'event_history_size': len(self._event_history)
        }
