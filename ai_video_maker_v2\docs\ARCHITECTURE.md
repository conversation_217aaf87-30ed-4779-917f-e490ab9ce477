# 系统架构文档 🏗️

本文档详细描述了AI视频生成器V2.0的系统架构设计、技术选型和实现细节。

## 总体架构 📊

### 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    AI视频生成器 V2.0                          │
├─────────────────────────────────────────────────────────────┤
│  🎨 用户界面层 (PyQt6 + 自定义组件)                           │
│  ├── 主题系统 ├── 卡片组件 ├── 动画效果 ├── 响应式布局        │
├─────────────────────────────────────────────────────────────┤
│  🔧 业务逻辑层                                               │
│  ├── 应用控制器 ├── 工作流管理 ├── 状态管理 ├── 事件系统      │
├─────────────────────────────────────────────────────────────┤
│  🤖 AI服务层                                                │
│  ├── LLM服务 ├── 图像生成 ├── 语音合成 ├── 视频生成          │
├─────────────────────────────────────────────────────────────┤
│  💾 数据层                                                  │
│  ├── 项目模型 ├── 分镜模型 ├── 媒体模型 ├── 配置管理          │
├─────────────────────────────────────────────────────────────┤
│  🛠️ 基础设施层                                              │
│  ├── 日志系统 ├── 错误处理 ├── 性能监控 ├── 工具函数          │
└─────────────────────────────────────────────────────────────┘
```

### 设计原则

1. **模块化设计**: 高内聚、低耦合的模块结构
2. **异步优先**: 使用异步编程提升性能
3. **事件驱动**: 基于事件的松耦合架构
4. **可扩展性**: 支持插件和第三方服务集成
5. **用户体验**: 现代化的界面设计和交互

## 核心组件 🔧

### 1. 应用控制器 (AppController)

**职责**: 应用程序的主控制器，协调各个组件

```python
class AppController:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.event_system = EventSystem()
        self.state_manager = StateManager()
        self.service_manager = ServiceManager()
        self.workflow_manager = WorkflowManager()
    
    async def initialize(self) -> bool
    async def shutdown(self)
    async def create_project(self, name: str) -> Dict
    async def generate_storyboard(self, text: str) -> Dict
```

**特性**:
- 统一的应用程序入口
- 组件生命周期管理
- 高级业务逻辑封装
- 错误处理和恢复

### 2. 配置管理器 (ConfigManager)

**职责**: 统一管理应用程序配置

```python
@dataclass
class AppConfig:
    environment: Environment
    ui: UIConfig
    performance: PerformanceConfig
    llm_apis: Dict[str, APIConfig]
    image_apis: Dict[str, APIConfig]
    voice_apis: Dict[str, APIConfig]
    video_apis: Dict[str, APIConfig]
```

**特性**:
- 类型安全的配置管理
- 多环境配置支持
- 配置验证和默认值
- 热重载功能

### 3. 事件系统 (EventSystem)

**职责**: 应用程序事件管理和通信

```python
class EventSystem:
    async def emit(self, event: Event)
    def subscribe(self, event_type: EventType, handler: EventHandler) -> str
    def unsubscribe(self, subscription_id: str) -> bool
```

**事件类型**:
- 应用程序事件 (启动、关闭)
- 项目事件 (创建、加载、保存)
- 工作流事件 (开始、完成、失败)
- 任务事件 (进度、状态变更)
- UI事件 (主题变更、窗口调整)

### 4. 状态管理器 (StateManager)

**职责**: 集中式状态管理

```python
@dataclass
class AppState:
    current_project: Optional[Dict[str, Any]]
    ui_state: Dict[str, Any]
    workflow_state: Dict[str, Any]
    task_states: Dict[str, Any]
    service_states: Dict[str, Any]
```

**特性**:
- 状态订阅/通知机制
- 状态持久化
- 状态历史记录
- 状态验证

### 5. 服务管理器 (ServiceManager)

**职责**: AI服务的统一管理

```python
class ServiceManager:
    def __init__(self):
        self.registry = ServiceRegistry()
        self.load_balancer = LoadBalancer()
    
    async def call_service(self, service_type: ServiceType, method: str, **kwargs) -> ServiceResult
    async def _perform_health_checks(self)
```

**特性**:
- 服务注册和发现
- 负载均衡
- 健康检查
- 错误处理和重试
- 服务统计

### 6. 工作流管理器 (WorkflowManager)

**职责**: 复杂业务工作流的编排和执行

```python
@dataclass
class Workflow:
    workflow_id: str
    name: str
    steps: List[WorkflowStep]
    status: WorkflowStatus
```

**特性**:
- 工作流定义和模板
- 步骤依赖管理
- 并行执行支持
- 错误处理和回滚
- 进度跟踪

## 数据模型 📊

### 1. 项目模型 (Project)

```python
@dataclass
class Project:
    metadata: ProjectMetadata
    settings: ProjectSettings
    data: ProjectData
    status: ProjectStatus
```

**组成部分**:
- **元数据**: 项目基本信息
- **设置**: 项目配置参数
- **数据**: 项目内容数据
- **状态**: 项目当前状态

### 2. 分镜模型 (Storyboard)

```python
@dataclass
class Storyboard:
    metadata: StoryboardMetadata
    characters: Dict[str, Character]
    scenes: Dict[str, Scene]
    shots: List[Shot]
    stage_data: Dict[str, Any]
```

**核心实体**:
- **角色 (Character)**: 角色信息和特征
- **场景 (Scene)**: 场景环境和设定
- **镜头 (Shot)**: 具体的镜头描述
- **阶段数据**: 五阶段分析结果

### 3. 媒体模型 (MediaAsset)

```python
@dataclass
class MediaAsset:
    metadata: MediaMetadata
    media_type: MediaType
    status: MediaStatus
    processing_log: List[str]
```

**媒体类型**:
- **图像资源 (ImageAsset)**: 图像文件和属性
- **音频资源 (AudioAsset)**: 音频文件和参数
- **视频资源 (VideoAsset)**: 视频文件和配置

## 用户界面架构 🎨

### 1. 主题系统

```python
@dataclass
class Theme:
    name: str
    type: ThemeType
    colors: ColorScheme
    typography: Typography
    spacing: Spacing
    border_radius: BorderRadius
```

**特性**:
- 深色/浅色主题支持
- 自定义颜色方案
- 动态主题切换
- 主题持久化

### 2. 组件库

**卡片组件**:
- `Card`: 基础卡片容器
- `CardHeader`: 卡片头部
- `CardContent`: 卡片内容
- `CardActions`: 卡片操作区

**按钮组件**:
- `PrimaryButton`: 主要按钮
- `SecondaryButton`: 次要按钮
- `IconButton`: 图标按钮
- `FloatingActionButton`: 浮动操作按钮

**输入组件**:
- `ModernLineEdit`: 现代化单行输入
- `ModernTextEdit`: 现代化多行输入
- `ModernComboBox`: 现代化下拉选择

### 3. 动画系统

```python
class AnimationManager:
    def fade_in(self, widget: QWidget, duration: int = 300) -> int
    def slide_in_from_left(self, widget: QWidget, duration: int = 400) -> int
    def scale_in(self, widget: QWidget, duration: int = 300) -> int
    def bounce_in(self, widget: QWidget, duration: int = 600) -> int
```

**动画类型**:
- 淡入淡出效果
- 滑动进入效果
- 缩放动画
- 弹跳效果
- 摇摆动画
- 脉冲效果

## AI服务架构 🤖

### 1. 服务抽象

```python
class BaseService(ABC):
    @abstractmethod
    async def initialize(self) -> bool
    @abstractmethod
    async def health_check(self) -> ServiceHealth
    @abstractmethod
    async def call_service(self, method: str, **kwargs) -> ServiceResult
```

### 2. 服务实现

**LLM服务**:
- `ZhipuLLMService`: 智谱AI GLM服务
- `OpenAILLMService`: OpenAI GPT服务
- `DeepSeekLLMService`: DeepSeek服务

**图像生成服务**:
- `CogViewImageService`: 智谱AI CogView服务
- `DALLEImageService`: OpenAI DALL-E服务
- `StableDiffusionService`: Stable Diffusion服务

**语音合成服务**:
- `AzureVoiceService`: Azure TTS服务
- `ElevenLabsService`: ElevenLabs服务
- `OpenAITTSService`: OpenAI TTS服务

**视频生成服务**:
- `CogVideoService`: 智谱AI CogVideoX服务
- `RunwayMLService`: RunwayML服务
- `PikaLabsService`: Pika Labs服务

### 3. 服务注册表

```python
class ServiceRegistry:
    def register_service(self, service: BaseService)
    def get_services_by_type(self, service_type: ServiceType) -> List[BaseService]
    def get_healthy_services(self, service_type: ServiceType) -> List[BaseService]
```

### 4. 负载均衡

```python
class LoadBalancer:
    def select_service(self, services: List[BaseService]) -> Optional[BaseService]
    def _round_robin_select(self, services: List[BaseService]) -> BaseService
    def _least_loaded_select(self, services: List[BaseService]) -> BaseService
    def _best_health_select(self, services: List[BaseService]) -> BaseService
```

## 工作流架构 🔄

### 1. 五阶段分镜工作流

```
阶段1: 世界观构建
    ↓
阶段2: 角色场景分析
    ↓
阶段3: 情节结构分析
    ↓
阶段4: 分镜脚本生成
    ↓
阶段5: 优化和完善
```

### 2. 工作流定义

```python
@dataclass
class WorkflowStep:
    step_id: str
    name: str
    service_type: ServiceType
    method: str
    params: Dict[str, Any]
    depends_on: List[str]
    timeout: Optional[float]
    max_retries: int
```

### 3. 执行引擎

**特性**:
- 依赖关系解析
- 并行执行支持
- 错误处理和重试
- 进度跟踪
- 结果缓存

## 性能优化 ⚡

### 1. 异步处理

- 所有AI服务调用使用异步模式
- UI操作不阻塞主线程
- 并发任务控制和限流

### 2. 缓存策略

```python
class CacheManager:
    def get(self, key: str) -> Optional[Any]
    def set(self, key: str, value: Any, ttl: int = 3600)
    def invalidate(self, pattern: str)
```

**缓存类型**:
- 内存缓存 (LRU)
- 磁盘缓存
- 分布式缓存 (可选)

### 3. 资源管理

- 智能内存管理
- 文件句柄控制
- 网络连接池
- GPU资源调度

### 4. 性能监控

```python
class PerformanceMonitor:
    def track_execution_time(self, operation: str)
    def monitor_memory_usage(self)
    def track_api_calls(self, service: str, method: str)
```

## 安全性设计 🔒

### 1. API密钥管理

- 加密存储API密钥
- 环境变量支持
- 密钥轮换机制
- 访问权限控制

### 2. 数据安全

- 本地数据加密
- 传输加密 (HTTPS)
- 敏感信息脱敏
- 数据备份和恢复

### 3. 错误处理

- 统一错误处理机制
- 错误日志记录
- 用户友好的错误提示
- 自动错误恢复

## 扩展性设计 🔧

### 1. 插件系统

```python
class PluginManager:
    def load_plugin(self, plugin_path: str) -> bool
    def register_service(self, service: BaseService)
    def register_workflow(self, workflow: Workflow)
```

### 2. 服务扩展

- 新AI服务提供商集成
- 自定义工作流定义
- 第三方组件集成
- API接口扩展

### 3. 配置扩展

- 自定义配置项
- 环境特定配置
- 动态配置更新
- 配置验证规则

## 部署架构 🚀

### 1. 单机部署

```
┌─────────────────────────────────────┐
│  AI视频生成器 V2.0 (单机版)          │
├─────────────────────────────────────┤
│  应用程序 + 数据库 + 缓存             │
│  本地文件存储                       │
│  配置文件管理                       │
└─────────────────────────────────────┘
```

### 2. 分布式部署 (未来)

```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│  前端应用    │  │  API网关     │  │  负载均衡    │
└─────────────┘  └─────────────┘  └─────────────┘
        │                │                │
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│  业务服务    │  │  AI服务集群  │  │  数据服务    │
└─────────────┘  └─────────────┘  └─────────────┘
        │                │                │
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│  消息队列    │  │  缓存集群    │  │  存储集群    │
└─────────────┘  └─────────────┘  └─────────────┘
```

## 技术栈 💻

### 前端技术
- **UI框架**: PyQt6
- **样式系统**: 自定义CSS + 主题管理
- **动画库**: Qt动画框架
- **图标库**: Material Design Icons

### 后端技术
- **编程语言**: Python 3.9+
- **异步框架**: asyncio + aiohttp
- **数据处理**: pandas + numpy
- **图像处理**: Pillow + OpenCV
- **音频处理**: pydub + librosa
- **视频处理**: moviepy + ffmpeg

### AI服务
- **LLM**: 智谱AI GLM, OpenAI GPT, DeepSeek
- **图像生成**: CogView, DALL-E, Stable Diffusion
- **语音合成**: Azure TTS, ElevenLabs, OpenAI TTS
- **视频生成**: CogVideoX, RunwayML, Pika Labs

### 开发工具
- **版本控制**: Git
- **代码格式**: Black + Flake8
- **类型检查**: MyPy
- **测试框架**: pytest
- **文档生成**: Sphinx

---

这个架构设计确保了系统的可扩展性、可维护性和高性能，为用户提供了优秀的AI视频创作体验。
