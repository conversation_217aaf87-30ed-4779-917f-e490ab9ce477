# AI视频生成器 🎬

一个功能强大的AI驱动视频生成工具，支持从文本到视频的完整工作流程。通过五阶段智能分镜系统、多引擎图像生成、语音合成和一致性控制，为用户提供专业级的视频制作体验。

## ✨ 核心特性

### 🎯 五阶段智能分镜系统
- **阶段1**: 世界观构建 - 自动生成故事背景和设定
- **阶段2**: 角色管理 - 智能提取和管理角色信息
- **阶段3**: 场景分析 - 深度分析场景结构和关系
- **阶段4**: 分镜生成 - 生成详细的分镜脚本
- **阶段5**: 优化建议 - 提供专业的优化建议

### 🖼️ 多引擎图像生成
- **Pollinations AI** - 免费在线生成，支持多种模型
- **ComfyUI** - 本地高质量生成，支持自定义工作流
- **Stability AI** - 专业级图像生成服务
- **智能参数同步** - 设置界面与分镜生成界面双向同步
- **批量生成** - 支持一键批量生成所有镜头图像

### 🎙️ 智能语音合成
- **Edge TTS** - 高质量免费语音合成
- **多语言支持** - 支持中文、英文等多种语言
- **配音驱动工作流** - 基于配音自动生成分镜
- **精确时长同步** - 视觉与听觉完美同步

### 🎥 视频生成系统
- **CogVideoX** - 先进的AI视频生成引擎
- **图像到视频** - 将静态图像转换为动态视频
- **批量处理** - 支持批量视频生成
- **质量控制** - 多种质量和时长选项

### 🎨 一致性增强系统
- **角色一致性** - 自动维护角色外观和特征描述
- **场景一致性** - 保持场景风格和氛围统一
- **智能描述增强** - LLM驱动的描述优化和技术细节融合
- **实时预览** - 图像生成后自动刷新预览区域

### 🌐 多语言翻译支持
- **LLM翻译** - 高质量的上下文感知翻译
- **百度翻译** - 备用翻译方案，确保稳定性
- **自动切换** - 翻译失败时自动使用备用方案

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.8+ (推荐3.10+)
- **内存**: 4GB+ RAM (推荐8GB)
- **存储**: 2GB+ 可用空间
- **网络**: 稳定的互联网连接 (用于AI服务调用)

### 一键启动
```bash
# 克隆项目
git clone https://github.com/yourusername/AI_Video_Generator.git
cd AI_Video_Generator

# 使用启动脚本（推荐）
python start.py

# 或直接启动
python main.py
```

### 手动安装

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置API密钥**

复制配置模板：
```bash
# Windows
copy config\llm_config.example.json config\llm_config.json
copy config\tts_config.example.json config\tts_config.json

# Linux/Mac
cp config/llm_config.example.json config/llm_config.json
cp config/tts_config.example.json config/tts_config.json
```

编辑 `config/llm_config.json`：
```json
{
  "models": [
    {
      "name": "智谱AI",
      "type": "zhipu",
      "key": "your-zhipu-api-key",
      "url": "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    }
  ]
}
```

3. **启动应用**
```bash
python main.py
```

## 📖 使用指南

### 🎭 推荐工作流程

#### 方式一：传统文本驱动
1. **创建项目** → 文件菜单 → 新建项目
2. **文本处理** → 输入原始文本 → AI改写优化
3. **五阶段分镜** → 按顺序完成五个阶段
4. **图像生成** → 批量生成分镜图像
5. **语音合成** → 生成配音文件
6. **视频生成** → 合成最终视频

#### 方式二：配音驱动（推荐）
1. **创建项目** → 文件菜单 → 新建项目
2. **语音生成** → 直接录制或生成配音
3. **配音驱动分镜** → 基于配音自动生成分镜
4. **图像生成** → 批量生成分镜图像
5. **视频生成** → 合成最终视频

### 🔧 核心功能

#### 五阶段分镜系统
- **阶段1**: 世界观构建 - 分析故事背景
- **阶段2**: 角色管理 - 提取角色信息
- **阶段3**: 场景分析 - 分析场景结构
- **阶段4**: 分镜生成 - 生成详细分镜
- **阶段5**: 优化建议 - 提供改进建议

#### 图像生成引擎
- **Pollinations AI**: 免费在线服务，快速生成
- **ComfyUI**: 本地部署，高质量定制
- **Stability AI**: 专业级商用服务

#### 语音合成功能
- **Edge TTS**: 免费高质量语音合成
- **多语言**: 支持中英文等多种语言
- **配音驱动**: 基于配音自动生成分镜

#### 视频生成功能
- **CogVideoX**: AI视频生成引擎
- **图像到视频**: 静态图像转动态视频
- **批量处理**: 一键生成所有视频片段

## ⚙️ 配置说明

### 🤖 AI服务配置

#### LLM服务（必需）
支持多种大语言模型：
- **智谱AI (GLM-4)** - 推荐，性价比高
- **通义千问** - 阿里云服务，稳定可靠
- **Deepseek** - 高性能模型，代码能力强
- **Google Gemini** - 谷歌服务，多模态支持

#### 图像生成服务
- **Pollinations AI** (免费) - 无需配置，开箱即用
- **ComfyUI** (本地) - 需要本地部署，高质量定制
- **Stability AI** (付费) - 专业级商用服务

#### 语音合成服务
- **Edge TTS** (免费) - 微软服务，无需配置
- **自定义TTS** - 支持其他TTS服务接入

#### 视频生成服务
- **CogVideoX** (在线) - AI视频生成，需要网络
- **本地视频处理** - 基于MoviePy的本地处理

### 📁 项目结构
```
项目名称/
├── project.json          # 项目配置文件
├── texts/                # 文本文件
├── images/               # 生成的图像
│   ├── pollinations/     # Pollinations生成的图像
│   └── comfyui/         # ComfyUI生成的图像
├── audio/               # 音频文件
├── videos/              # 视频文件
└── temp/                # 临时文件
```

## 🛠️ 故障排除

### 常见问题

#### 启动问题
- **Python版本错误**: 确保使用Python 3.8+
- **依赖缺失**: 运行 `pip install -r requirements.txt`
- **PyQt5问题**: 在某些系统上可能需要额外安装系统依赖

#### 功能问题
- **LLM服务失败**: 检查API密钥配置和网络连接
- **图像生成失败**:
  - Pollinations AI: 检查网络连接
  - ComfyUI: 确保本地服务运行在 http://127.0.0.1:8188
- **语音合成失败**: 检查网络连接，Edge TTS需要联网

#### 性能问题
- **内存不足**: 关闭其他应用，增加虚拟内存
- **生成速度慢**: 使用更快的网络连接，选择合适的AI服务

### 📚 文档资源
- 📖 [完整文档目录](docs/) - 详细使用指南
- 🚀 [快速开始指南](docs/STARTUP_GUIDE.md) - 新手入门
- 🏗️ [项目结构说明](docs/PROJECT_STRUCTURE.md) - 代码架构
- ⚙️ [配置指南](docs/DEPLOYMENT.md) - 部署配置

## 💡 使用技巧

### 🎨 提高生成质量
- **文本准备**: 使用清晰、具体的描述，包含足够细节
- **分镜优化**: 控制镜头时长，保持逻辑连贯性
- **图像生成**: 根据内容选择合适引擎，调整参数获得最佳效果
- **一致性控制**: 利用角色和场景管理保持风格统一

### ⚡ 性能优化
- 使用SSD存储提高I/O性能
- 关闭不必要的后台程序
- 定期清理临时文件和缓存
- 选择合适的AI服务提供商

## 🤝 贡献与支持

### 参与贡献
1. Fork项目 → 创建功能分支 → 提交更改 → 发起Pull Request
2. 提交 [Issue](https://github.com/yourusername/AI_Video_Generator/issues) 报告问题
3. 参与 [讨论](https://github.com/yourusername/AI_Video_Generator/discussions) 交流经验

### 获取支持
- 📖 查看文档获取详细指南
- 🐛 提交Issue报告问题
- 💬 加入社区讨论

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢以下开源项目和服务的支持：
- **PyQt5** - 用户界面框架
- **Pollinations AI** - 免费图像生成服务
- **ComfyUI** - 强大的图像生成工具
- **Edge TTS** - 免费语音合成服务
- **各大LLM提供商** - AI语言模型支持

---

**免责声明**: 本工具仅供学习和研究使用。使用本工具生成内容时，请遵守相关法律法规和平台使用条款。生成的内容仅供参考，请根据实际需要进行调整和优化。
