#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试脚本

测试AI视频生成器V2.0的基本功能是否正常工作。
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心模块
        from src.core.config_manager import ConfigManager
        from src.core.event_system import EventSystem
        from src.core.state_manager import StateManager
        from src.core.service_manager import ServiceManager
        from src.core.workflow_manager import WorkflowManager
        from src.core.app_controller import AppController
        print("✅ 核心模块导入成功")
        
        # 测试数据模型
        from src.models.project import Project, ProjectMetadata, ProjectSettings
        from src.models.storyboard import Storyboard, Character, Scene, Shot
        from src.models.media import MediaAsset, ImageAsset, AudioAsset, VideoAsset
        print("✅ 数据模型导入成功")
        
        # 测试UI组件
        from src.ui.theme import ThemeManager
        from src.ui.components.card import Card, CardHeader, CardContent
        from src.ui.components.button import PrimaryButton, SecondaryButton
        print("✅ UI组件导入成功")
        
        # 测试工作流
        from src.workflows.storyboard_workflow import StoryboardProcessor
        print("✅ 工作流模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("🔍 测试配置管理器...")
    
    try:
        from src.core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 测试加载配置
        config = config_manager.load_config("config/config.example.json")
        
        if config:
            print("✅ 配置文件加载成功")
            print(f"   环境: {config.environment}")
            print(f"   调试模式: {config.debug}")
            return True
        else:
            print("❌ 配置文件加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_project_model():
    """测试项目模型"""
    print("🔍 测试项目模型...")
    
    try:
        from src.models.project import Project
        
        # 创建测试项目
        project = Project.create_new(
            name="测试项目",
            description="这是一个测试项目",
            base_dir="test_data",
            author="测试用户"
        )
        
        print("✅ 项目创建成功")
        print(f"   项目ID: {project.metadata.project_id}")
        print(f"   项目名称: {project.metadata.name}")
        
        # 测试保存项目
        if project.save():
            print("✅ 项目保存成功")
        else:
            print("❌ 项目保存失败")
            return False
        
        # 测试加载项目
        project_file = project._project_file
        loaded_project = Project.load_from_file(str(project_file))
        
        if loaded_project.metadata.name == project.metadata.name:
            print("✅ 项目加载成功")
        else:
            print("❌ 项目加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 项目模型测试失败: {e}")
        return False

def test_storyboard_model():
    """测试分镜模型"""
    print("🔍 测试分镜模型...")
    
    try:
        from src.models.storyboard import Storyboard, Character, Scene, Shot, ShotType, CameraMovement
        
        # 创建分镜板
        storyboard = Storyboard()
        storyboard.metadata.title = "测试分镜"
        storyboard.metadata.style = "电影风格"
        
        # 添加角色
        character = Character(
            name="主角",
            description="故事的主人公",
            appearance="年轻男性，黑发，穿着休闲"
        )
        storyboard.add_character(character)
        
        # 添加场景
        scene = Scene(
            title="客厅",
            location="家中客厅",
            time_of_day="下午",
            lighting="自然光"
        )
        storyboard.add_scene(scene)
        
        # 添加镜头
        shot = Shot(
            sequence_number=1,
            scene_id=scene.scene_id,
            description="主角坐在沙发上思考",
            shot_type=ShotType.MEDIUM_SHOT,
            camera_movement=CameraMovement.STATIC,
            duration=3.0
        )
        storyboard.add_shot(shot)
        
        print("✅ 分镜板创建成功")
        print(f"   角色数量: {storyboard.character_count}")
        print(f"   场景数量: {storyboard.scene_count}")
        print(f"   镜头数量: {storyboard.shot_count}")
        print(f"   总时长: {storyboard.total_duration}秒")
        
        # 测试序列化
        storyboard_dict = storyboard.to_dict()
        restored_storyboard = Storyboard.from_dict(storyboard_dict)
        
        if restored_storyboard.shot_count == storyboard.shot_count:
            print("✅ 分镜板序列化成功")
        else:
            print("❌ 分镜板序列化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 分镜模型测试失败: {e}")
        return False

async def test_service_manager():
    """测试服务管理器"""
    print("🔍 测试服务管理器...")
    
    try:
        from src.core.service_manager import ServiceManager
        from src.core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        config = config_manager.load_config("config/config.example.json")
        
        if not config:
            print("❌ 无法加载配置文件")
            return False
        
        # 创建服务管理器
        service_manager = ServiceManager(config)
        
        # 初始化服务管理器
        success = await service_manager.initialize()
        
        if success:
            print("✅ 服务管理器初始化成功")
        else:
            print("⚠️  服务管理器初始化部分成功（可能缺少API密钥）")
        
        # 测试服务注册
        services = service_manager.get_available_services()
        print(f"   可用服务数量: {len(services)}")
        
        # 关闭服务管理器
        await service_manager.shutdown()
        print("✅ 服务管理器关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务管理器测试失败: {e}")
        return False

async def test_app_controller():
    """测试应用控制器"""
    print("🔍 测试应用控制器...")
    
    try:
        from src.core.app_controller import AppController
        
        # 创建应用控制器
        app_controller = AppController(
            config_dir="config",
            data_dir="test_data"
        )
        
        # 初始化应用控制器
        success = await app_controller.initialize()
        
        if success:
            print("✅ 应用控制器初始化成功")
        else:
            print("⚠️  应用控制器初始化部分成功")
        
        # 测试创建项目
        project_data = await app_controller.create_project(
            name="控制器测试项目",
            description="通过应用控制器创建的测试项目"
        )
        
        if project_data.get("success", False):
            print("✅ 通过控制器创建项目成功")
        else:
            print("❌ 通过控制器创建项目失败")
        
        # 关闭应用控制器
        await app_controller.shutdown()
        print("✅ 应用控制器关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用控制器测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("🧹 清理测试数据...")
    
    import shutil
    
    test_dirs = ["test_data", "logs"]
    
    for test_dir in test_dirs:
        test_path = Path(test_dir)
        if test_path.exists():
            try:
                shutil.rmtree(test_path)
                print(f"✅ 已清理: {test_dir}")
            except Exception as e:
                print(f"⚠️  清理失败 {test_dir}: {e}")

async def main():
    """主测试函数"""
    print("🎬 AI视频生成器 V2.0 - 基础功能测试")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    test_results = []
    
    # 运行测试
    test_results.append(("模块导入", test_imports()))
    test_results.append(("配置管理器", test_config_manager()))
    test_results.append(("项目模型", test_project_model()))
    test_results.append(("分镜模型", test_storyboard_model()))
    test_results.append(("服务管理器", await test_service_manager()))
    test_results.append(("应用控制器", await test_app_controller()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统基本功能正常。")
    elif passed > total // 2:
        print("⚠️  大部分测试通过，系统基本可用。")
    else:
        print("❌ 多个测试失败，请检查系统配置。")
    
    # 清理测试数据
    cleanup_test_data()
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        sys.exit(1)
