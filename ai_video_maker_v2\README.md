# AI视频生成器 V2.0 🎬

## 项目简介

AI视频生成器V2.0是基于原版本的全面重构版本，采用现代化的架构设计和用户界面，提供更流畅、更美观、更高效的AI视频创作体验。

## ✨ 主要特性

### 🎨 现代化界面设计
- **卡片式设计语言** - 统一、美观的视觉风格
- **响应式布局** - 适配不同屏幕尺寸
- **流畅动画效果** - 提升用户交互体验
- **深色/浅色主题** - 个性化界面选择

### 🚀 核心功能
- **智能五阶段分镜** - 从文本到分镜的完整工作流
- **多引擎图像生成** - 支持多种AI图像生成服务
- **高质量语音合成** - 多语言、多音色支持
- **智能视频合成** - 自动化视频制作流程
- **一致性控制** - 角色和场景一致性管理

### 🔧 技术优势
- **模块化架构** - 易于扩展和维护
- **异步处理** - 高性能并发执行
- **智能缓存** - 优化资源使用
- **错误恢复** - 健壮的错误处理机制

## 🏗️ 项目架构

```
ai_video_maker_v2/
├── 📁 src/                     # 源代码
│   ├── 📁 core/                # 核心架构
│   ├── 📁 ui/                  # 用户界面
│   ├── 📁 services/            # AI服务
│   ├── 📁 models/              # 数据模型
│   ├── 📁 utils/               # 工具函数
│   └── 📁 workflows/           # 工作流程
├── 📁 assets/                  # 静态资源
├── 📁 config/                  # 配置文件
├── 📁 tests/                   # 测试代码
├── 📁 docs/                    # 文档
└── 📁 examples/                # 示例项目
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- PyQt6
- 8GB+ RAM
- GPU支持 (推荐)

### 安装步骤
```bash
# 克隆项目
git clone <repository-url>
cd ai_video_maker_v2

# 安装依赖
pip install -r requirements.txt

# 配置API密钥
cp config/config.example.json config/config.json
# 编辑config.json添加您的API密钥

# 启动程序
python main.py
```

## 📖 使用指南

### 基本工作流程
1. **创建项目** - 设置项目名称和基本信息
2. **文本创作** - 输入或生成故事文本
3. **五阶段分镜** - 自动生成详细分镜脚本
4. **图像生成** - 为每个镜头生成对应图像
5. **语音制作** - 生成角色对话和旁白
6. **视频合成** - 自动合成最终视频

### 高级功能
- **一致性控制** - 确保角色和场景的视觉一致性
- **批量处理** - 同时处理多个项目
- **自定义模板** - 创建和使用项目模板
- **插件系统** - 扩展功能模块

## 🔧 配置说明

### API配置
支持多种AI服务提供商：
- **LLM服务**: OpenAI, 智谱AI, 通义千问, DeepSeek
- **图像生成**: DALL-E, Midjourney, Stable Diffusion, CogView
- **语音合成**: Azure TTS, ElevenLabs, OpenAI TTS
- **视频生成**: RunwayML, Pika Labs, CogVideoX

### 性能配置
- **并发任务数** - 根据硬件配置调整
- **缓存策略** - 优化内存和存储使用
- **GPU加速** - 启用GPU加速处理

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](docs/CONTRIBUTING.md) 了解详细信息。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆕 更新日志

### V2.0.0 (2025-06-29)
- 🎨 全新现代化界面设计
- 🚀 重构核心架构
- ⚡ 性能优化和稳定性提升
- 🔧 增强的配置管理
- 📱 响应式布局支持

## 📞 支持与反馈

- 📧 邮箱: <EMAIL>
- 💬 讨论: [GitHub Discussions](https://github.com/example/discussions)
- 🐛 问题报告: [GitHub Issues](https://github.com/example/issues)

---

**让AI视频创作变得简单而美好** ✨
