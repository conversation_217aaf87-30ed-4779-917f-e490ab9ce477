# -*- coding: utf-8 -*-
"""
仪表板页面 - 项目概览和快速操作

提供项目管理和系统状态的总览界面：
- 项目列表和快速访问
- 系统状态监控
- 最近活动记录
- 快速操作入口
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QScrollArea, QFrame, QProgressBar)
from PyQt6.QtGui import QPixmap, QFont

from ..components.card import Card, CardHeader, CardContent, CardActions
from ..components.button import PrimaryButton, SecondaryButton
from ...models.project import Project

logger = logging.getLogger(__name__)

class ProjectCard(Card):
    """项目卡片"""
    
    project_selected = pyqtSignal(str)  # 项目ID
    
    def __init__(self, project_info: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.project_info = project_info
        self._setup_ui()
    
    def _setup_ui(self):
        """设置界面"""
        # 项目标题
        header = CardHeader(
            self.project_info.get("name", "未命名项目"),
            self.project_info.get("description", "")
        )
        self.add_widget(header)
        
        # 项目信息
        content = CardContent()
        
        info_layout = QGridLayout()
        
        # 创建时间
        info_layout.addWidget(QLabel("创建时间:"), 0, 0)
        created_time = self.project_info.get("created_at", "")
        if created_time:
            created_label = QLabel(created_time.strftime("%Y-%m-%d %H:%M") if isinstance(created_time, datetime) else str(created_time))
        else:
            created_label = QLabel("未知")
        created_label.setStyleSheet("color: #757575;")
        info_layout.addWidget(created_label, 0, 1)
        
        # 最后修改
        info_layout.addWidget(QLabel("最后修改:"), 1, 0)
        modified_time = self.project_info.get("modified_at", "")
        if modified_time:
            modified_label = QLabel(modified_time.strftime("%Y-%m-%d %H:%M") if isinstance(modified_time, datetime) else str(modified_time))
        else:
            modified_label = QLabel("未知")
        modified_label.setStyleSheet("color: #757575;")
        info_layout.addWidget(modified_label, 1, 1)
        
        # 项目状态
        info_layout.addWidget(QLabel("状态:"), 2, 0)
        status = self.project_info.get("status", "未知")
        status_label = QLabel(status)
        if status == "完成":
            status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        elif status == "进行中":
            status_label.setStyleSheet("color: #FF9800; font-weight: bold;")
        else:
            status_label.setStyleSheet("color: #757575;")
        info_layout.addWidget(status_label, 2, 1)
        
        content.add_layout(info_layout)
        self.add_widget(content)
        
        # 操作按钮
        actions = CardActions()
        
        open_btn = PrimaryButton("打开项目")
        open_btn.clicked.connect(self._open_project)
        actions.add_button(open_btn)
        
        self.add_widget(actions)
    
    def _open_project(self):
        """打开项目"""
        project_id = self.project_info.get("project_id", "")
        if project_id:
            self.project_selected.emit(project_id)

class SystemStatusCard(Card):
    """系统状态卡片"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
        
        # 定时更新状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_status)
        self.update_timer.start(5000)  # 每5秒更新一次
    
    def _setup_ui(self):
        """设置界面"""
        header = CardHeader("系统状态", "AI服务和系统资源状态")
        self.add_widget(header)
        
        content = CardContent()
        
        # 服务状态
        services_layout = QGridLayout()
        
        # LLM服务状态
        services_layout.addWidget(QLabel("LLM服务:"), 0, 0)
        self.llm_status = QLabel("检查中...")
        self.llm_status.setStyleSheet("color: #757575;")
        services_layout.addWidget(self.llm_status, 0, 1)
        
        # 图像生成服务状态
        services_layout.addWidget(QLabel("图像生成:"), 1, 0)
        self.image_status = QLabel("检查中...")
        self.image_status.setStyleSheet("color: #757575;")
        services_layout.addWidget(self.image_status, 1, 1)
        
        # 语音合成服务状态
        services_layout.addWidget(QLabel("语音合成:"), 2, 0)
        self.voice_status = QLabel("检查中...")
        self.voice_status.setStyleSheet("color: #757575;")
        services_layout.addWidget(self.voice_status, 2, 1)
        
        # 视频生成服务状态
        services_layout.addWidget(QLabel("视频生成:"), 3, 0)
        self.video_status = QLabel("检查中...")
        self.video_status.setStyleSheet("color: #757575;")
        services_layout.addWidget(self.video_status, 3, 1)
        
        content.add_layout(services_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("color: #E0E0E0;")
        content.add_widget(separator)
        
        # 系统资源
        resources_layout = QGridLayout()
        
        # 内存使用
        resources_layout.addWidget(QLabel("内存使用:"), 0, 0)
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        self.memory_progress.setValue(0)
        resources_layout.addWidget(self.memory_progress, 0, 1)
        
        # CPU使用
        resources_layout.addWidget(QLabel("CPU使用:"), 1, 0)
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setMaximum(100)
        self.cpu_progress.setValue(0)
        resources_layout.addWidget(self.cpu_progress, 1, 1)
        
        content.add_layout(resources_layout)
        self.add_widget(content)
    
    def _update_status(self):
        """更新状态"""
        # 这里应该从服务管理器获取实际状态
        # 暂时使用模拟数据
        import random
        
        # 模拟服务状态
        services = [
            (self.llm_status, "LLM服务"),
            (self.image_status, "图像生成"),
            (self.voice_status, "语音合成"),
            (self.video_status, "视频生成")
        ]
        
        for status_label, service_name in services:
            if random.random() > 0.2:  # 80%概率正常
                status_label.setText("✅ 正常")
                status_label.setStyleSheet("color: #4CAF50;")
            else:
                status_label.setText("❌ 异常")
                status_label.setStyleSheet("color: #F44336;")
        
        # 模拟资源使用
        self.memory_progress.setValue(random.randint(30, 80))
        self.cpu_progress.setValue(random.randint(10, 60))

class QuickActionsCard(Card):
    """快速操作卡片"""
    
    new_project_requested = pyqtSignal()
    import_project_requested = pyqtSignal()
    open_settings_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置界面"""
        header = CardHeader("快速操作", "常用功能快速入口")
        self.add_widget(header)
        
        content = CardContent()
        
        # 操作按钮网格
        actions_layout = QGridLayout()
        
        # 新建项目
        new_project_btn = PrimaryButton("新建项目")
        new_project_btn.clicked.connect(self.new_project_requested.emit)
        actions_layout.addWidget(new_project_btn, 0, 0)
        
        # 导入项目
        import_project_btn = SecondaryButton("导入项目")
        import_project_btn.clicked.connect(self.import_project_requested.emit)
        actions_layout.addWidget(import_project_btn, 0, 1)
        
        # 打开设置
        settings_btn = SecondaryButton("系统设置")
        settings_btn.clicked.connect(self.open_settings_requested.emit)
        actions_layout.addWidget(settings_btn, 1, 0)
        
        # 查看文档
        docs_btn = SecondaryButton("使用指南")
        actions_layout.addWidget(docs_btn, 1, 1)
        
        content.add_layout(actions_layout)
        self.add_widget(content)

class RecentActivityCard(Card):
    """最近活动卡片"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()
    
    def _setup_ui(self):
        """设置界面"""
        header = CardHeader("最近活动", "系统操作记录")
        self.add_widget(header)
        
        content = CardContent()
        
        # 活动列表
        self.activity_layout = QVBoxLayout()
        
        # 添加一些示例活动
        activities = [
            ("2025-06-29 14:30", "创建了新项目 '测试视频'"),
            ("2025-06-29 14:25", "完成了分镜生成"),
            ("2025-06-29 14:20", "开始图像生成任务"),
            ("2025-06-29 14:15", "保存了项目设置"),
        ]
        
        for time_str, activity in activities:
            activity_item = self._create_activity_item(time_str, activity)
            self.activity_layout.addWidget(activity_item)
        
        content.add_layout(self.activity_layout)
        self.add_widget(content)
    
    def _create_activity_item(self, time_str: str, activity: str) -> QWidget:
        """创建活动项"""
        item = QWidget()
        layout = QHBoxLayout(item)
        layout.setContentsMargins(0, 4, 0, 4)
        
        # 时间标签
        time_label = QLabel(time_str)
        time_label.setStyleSheet("color: #757575; font-size: 12px;")
        time_label.setFixedWidth(120)
        layout.addWidget(time_label)
        
        # 活动描述
        activity_label = QLabel(activity)
        activity_label.setStyleSheet("font-size: 14px;")
        layout.addWidget(activity_label)
        
        layout.addStretch()
        
        return item
    
    def add_activity(self, activity: str):
        """添加新活动"""
        time_str = datetime.now().strftime("%Y-%m-%d %H:%M")
        activity_item = self._create_activity_item(time_str, activity)
        self.activity_layout.insertWidget(0, activity_item)
        
        # 限制显示的活动数量
        if self.activity_layout.count() > 10:
            item = self.activity_layout.takeAt(self.activity_layout.count() - 1)
            if item.widget():
                item.widget().deleteLater()

class DashboardPage(QWidget):
    """仪表板页面"""
    
    project_selected = pyqtSignal(str)
    new_project_requested = pyqtSignal()
    import_project_requested = pyqtSignal()
    open_settings_requested = pyqtSignal()
    
    def __init__(self, app_controller=None, parent=None):
        super().__init__(parent)
        self.app_controller = app_controller
        self.projects: List[Dict[str, Any]] = []
        
        self._setup_ui()
        self._load_projects()
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)
        
        # 页面标题
        title_card = Card()
        title_header = CardHeader("仪表板", "项目管理和系统概览")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧：项目和快速操作
        left_panel = self._create_left_panel()
        content_layout.addWidget(left_panel, 2)
        
        # 右侧：系统状态和活动
        right_panel = self._create_right_panel()
        content_layout.addWidget(right_panel, 1)
        
        layout.addLayout(content_layout)
    
    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)
        
        # 快速操作卡片
        self.quick_actions_card = QuickActionsCard()
        self.quick_actions_card.new_project_requested.connect(self.new_project_requested.emit)
        self.quick_actions_card.import_project_requested.connect(self.import_project_requested.emit)
        self.quick_actions_card.open_settings_requested.connect(self.open_settings_requested.emit)
        layout.addWidget(self.quick_actions_card)
        
        # 项目列表卡片
        self.projects_card = self._create_projects_card()
        layout.addWidget(self.projects_card)
        
        return panel
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)
        
        # 系统状态卡片
        self.system_status_card = SystemStatusCard()
        layout.addWidget(self.system_status_card)
        
        # 最近活动卡片
        self.recent_activity_card = RecentActivityCard()
        layout.addWidget(self.recent_activity_card)
        
        layout.addStretch()
        
        return panel
    
    def _create_projects_card(self) -> Card:
        """创建项目列表卡片"""
        card = Card()
        header = CardHeader("我的项目", "最近使用的项目")
        card.add_widget(header)
        
        content = CardContent()
        
        # 项目列表滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(400)
        
        self.projects_container = QWidget()
        self.projects_layout = QVBoxLayout(self.projects_container)
        self.projects_layout.setSpacing(8)
        
        scroll_area.setWidget(self.projects_container)
        content.add_widget(scroll_area)
        
        card.add_widget(content)
        
        return card
    
    def _load_projects(self):
        """加载项目列表"""
        # 这里应该从项目管理器获取实际项目列表
        # 暂时使用模拟数据
        sample_projects = [
            {
                "project_id": "proj_001",
                "name": "我的第一个视频",
                "description": "学习使用AI视频生成器",
                "status": "进行中",
                "created_at": "2025-06-29 10:00",
                "modified_at": "2025-06-29 14:30"
            },
            {
                "project_id": "proj_002", 
                "name": "产品宣传片",
                "description": "公司新产品的宣传视频",
                "status": "完成",
                "created_at": "2025-06-28 15:30",
                "modified_at": "2025-06-29 09:15"
            }
        ]
        
        self.projects = sample_projects
        self._update_projects_display()
    
    def _update_projects_display(self):
        """更新项目显示"""
        # 清除现有项目卡片
        for i in reversed(range(self.projects_layout.count())):
            child = self.projects_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 添加项目卡片
        if not self.projects:
            no_projects_label = QLabel("暂无项目，点击上方按钮创建新项目")
            no_projects_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_projects_label.setStyleSheet("color: #757575; font-style: italic; padding: 20px;")
            self.projects_layout.addWidget(no_projects_label)
        else:
            for project_info in self.projects:
                project_card = ProjectCard(project_info)
                project_card.project_selected.connect(self.project_selected.emit)
                self.projects_layout.addWidget(project_card)
        
        self.projects_layout.addStretch()
    
    def refresh_projects(self):
        """刷新项目列表"""
        self._load_projects()
    
    def add_activity(self, activity: str):
        """添加活动记录"""
        self.recent_activity_card.add_activity(activity)
