{"success": [], "failed": [{"shot_id": "test_segment_001", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_002", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_003", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_004", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_005", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_006", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_007", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_008", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_009", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_010", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_011", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}, {"shot_id": "test_segment_012", "error": "VideoGenerationConfig.__init__() got an unexpected keyword argument 'input_image'", "generation_time": 0}], "total_time": 0.00878763198852539, "start_time": 1751101390.3467}