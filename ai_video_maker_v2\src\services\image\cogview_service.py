# -*- coding: utf-8 -*-
"""
智谱AI CogView图像生成服务

支持CogView-3等模型，提供高质量的图像生成功能。
"""

import asyncio
import base64
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import aiohttp
import aiofiles
from PIL import Image
import io

from ...core.service_manager import BaseService, ServiceResult, ServiceHealth, ServiceStatus, ServiceType
from ...core.config_manager import APIConfig

logger = logging.getLogger(__name__)

class CogViewImageService(BaseService):
    """智谱AI CogView图像生成服务"""
    
    def __init__(self, provider: str, config: APIConfig):
        super().__init__(ServiceType.IMAGE, provider, config)
        self.base_url = config.base_url or "https://open.bigmodel.cn/api/paas/v4/"
        self.api_key = config.api_key
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 支持的模型
        self.models = {
            "cogview-3": "cogview-3",
            "cogview-3-plus": "cogview-3-plus"
        }
        
        # 支持的尺寸
        self.supported_sizes = [
            "1024x1024", "1280x720", "720x1280", 
            "1440x720", "720x1440", "1024x576", 
            "576x1024"
        ]
        
        # 默认参数
        self.default_params = {
            "model": "cogview-3-plus",
            "size": "1024x1024",
            "quality": "standard",
            "n": 1
        }
    
    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 创建HTTP会话
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            # 测试连接
            health = await self.health_check()
            if health.status == ServiceStatus.HEALTHY:
                logger.info(f"CogView图像服务初始化成功: {self.service_id}")
                return True
            else:
                logger.error(f"CogView图像服务初始化失败: {health.status}")
                return False
                
        except Exception as e:
            logger.error(f"CogView图像服务初始化异常: {e}")
            return False
    
    async def health_check(self) -> ServiceHealth:
        """健康检查"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if not self.session:
                return ServiceHealth(
                    status=ServiceStatus.OFFLINE,
                    last_check=datetime.now(),
                    response_time=0.0,
                    error_count=self.health.error_count + 1
                )
            
            # 发送简单的测试请求（不实际生成图像）
            test_data = {
                "model": "cogview-3-plus",
                "prompt": "test",
                "size": "1024x1024",
                "n": 1
            }
            
            # 这里只测试API的可达性，不实际生成图像
            async with self.session.post(
                f"{self.base_url}images/generations",
                json=test_data
            ) as response:
                response_time = asyncio.get_event_loop().time() - start_time
                
                # 即使返回错误，只要能连接到API就认为是健康的
                if response.status in [200, 400, 401]:
                    return ServiceHealth(
                        status=ServiceStatus.HEALTHY,
                        last_check=datetime.now(),
                        response_time=response_time,
                        success_count=self.health.success_count + 1
                    )
                else:
                    return ServiceHealth(
                        status=ServiceStatus.UNHEALTHY,
                        last_check=datetime.now(),
                        response_time=response_time,
                        error_count=self.health.error_count + 1
                    )
                    
        except Exception as e:
            response_time = asyncio.get_event_loop().time() - start_time
            logger.warning(f"CogView健康检查失败: {e}")
            
            return ServiceHealth(
                status=ServiceStatus.UNHEALTHY,
                last_check=datetime.now(),
                response_time=response_time,
                error_count=self.health.error_count + 1
            )
    
    async def call_service(self, method: str, **kwargs) -> ServiceResult:
        """调用服务方法"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if method == "generate_image":
                return await self._generate_image(**kwargs)
            elif method == "generate_images_batch":
                return await self._generate_images_batch(**kwargs)
            else:
                return ServiceResult(
                    success=False,
                    error=f"不支持的方法: {method}"
                )
                
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"CogView服务调用失败: {e}")
            
            return ServiceResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    async def _generate_image(self, prompt: str, output_dir: str, filename: str = None, **kwargs) -> ServiceResult:
        """生成单张图像"""
        try:
            # 合并参数
            params = {**self.default_params, **kwargs}
            
            # 验证尺寸
            size = params.get("size", "1024x1024")
            if size not in self.supported_sizes:
                return ServiceResult(
                    success=False,
                    error=f"不支持的图像尺寸: {size}，支持的尺寸: {self.supported_sizes}"
                )
            
            # 构建请求数据
            request_data = {
                "model": params.get("model", "cogview-3-plus"),
                "prompt": prompt,
                "size": size,
                "quality": params.get("quality", "standard"),
                "n": 1
            }
            
            # 发送请求
            async with self.session.post(
                f"{self.base_url}images/generations",
                json=request_data
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    return ServiceResult(
                        success=False,
                        error=f"API请求失败 ({response.status}): {error_text}"
                    )
                
                result = await response.json()
                
                # 提取图像数据
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]
                    
                    # 保存图像
                    if "url" in image_data:
                        # 从URL下载图像
                        image_path = await self._download_image(image_data["url"], output_dir, filename)
                    elif "b64_json" in image_data:
                        # 从base64数据保存图像
                        image_path = await self._save_base64_image(image_data["b64_json"], output_dir, filename)
                    else:
                        return ServiceResult(
                            success=False,
                            error="API返回格式异常：缺少图像数据"
                        )
                    
                    # 获取图像信息
                    image_info = await self._get_image_info(image_path)
                    
                    return ServiceResult(
                        success=True,
                        data={
                            "image_path": str(image_path),
                            "prompt": prompt,
                            "model": request_data["model"],
                            "size": size,
                            "width": image_info["width"],
                            "height": image_info["height"],
                            "format": image_info["format"]
                        },
                        metadata={
                            "created": result.get("created"),
                            "revised_prompt": image_data.get("revised_prompt")
                        }
                    )
                else:
                    return ServiceResult(
                        success=False,
                        error="API返回格式异常：缺少data字段"
                    )
                    
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            return ServiceResult(
                success=False,
                error=str(e)
            )
    
    async def _generate_images_batch(self, prompts: List[str], output_dir: str, **kwargs) -> ServiceResult:
        """批量生成图像"""
        try:
            results = []
            failed_count = 0
            
            # 并发控制
            max_concurrent = kwargs.get("max_concurrent", 3)
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def generate_single(prompt: str, index: int):
                async with semaphore:
                    filename = f"image_{index:04d}.png"
                    result = await self._generate_image(prompt, output_dir, filename, **kwargs)
                    return result
            
            # 创建任务
            tasks = [
                generate_single(prompt, i) 
                for i, prompt in enumerate(prompts)
            ]
            
            # 执行任务
            task_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(task_results):
                if isinstance(result, Exception):
                    failed_count += 1
                    results.append({
                        "index": i,
                        "prompt": prompts[i],
                        "success": False,
                        "error": str(result)
                    })
                elif result.success:
                    results.append({
                        "index": i,
                        "prompt": prompts[i],
                        "success": True,
                        "image_path": result.data["image_path"],
                        "width": result.data["width"],
                        "height": result.data["height"]
                    })
                else:
                    failed_count += 1
                    results.append({
                        "index": i,
                        "prompt": prompts[i],
                        "success": False,
                        "error": result.error
                    })
            
            success_count = len(prompts) - failed_count
            
            return ServiceResult(
                success=success_count > 0,
                data={
                    "results": results,
                    "total_count": len(prompts),
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "output_dir": output_dir
                },
                metadata={
                    "batch_size": len(prompts),
                    "max_concurrent": max_concurrent
                }
            )
            
        except Exception as e:
            logger.error(f"批量图像生成失败: {e}")
            return ServiceResult(
                success=False,
                error=str(e)
            )
    
    async def _download_image(self, url: str, output_dir: str, filename: str = None) -> Path:
        """从URL下载图像"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        if not filename:
            filename = f"image_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        image_path = output_path / filename
        
        async with self.session.get(url) as response:
            if response.status == 200:
                async with aiofiles.open(image_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
            else:
                raise Exception(f"下载图像失败: {response.status}")
        
        return image_path
    
    async def _save_base64_image(self, b64_data: str, output_dir: str, filename: str = None) -> Path:
        """保存base64图像数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        if not filename:
            filename = f"image_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        image_path = output_path / filename
        
        # 解码base64数据
        image_data = base64.b64decode(b64_data)
        
        # 保存图像
        async with aiofiles.open(image_path, 'wb') as f:
            await f.write(image_data)
        
        return image_path
    
    async def _get_image_info(self, image_path: Path) -> Dict[str, Any]:
        """获取图像信息"""
        try:
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "format": img.format,
                    "mode": img.mode
                }
        except Exception as e:
            logger.warning(f"获取图像信息失败: {e}")
            return {
                "width": 0,
                "height": 0,
                "format": "unknown",
                "mode": "unknown"
            }
    
    def _find_closest_size(self, target_width: int, target_height: int) -> str:
        """找到最接近的支持尺寸"""
        target_ratio = target_width / target_height
        best_size = "1024x1024"
        best_diff = float('inf')
        
        for size in self.supported_sizes:
            width, height = map(int, size.split('x'))
            ratio = width / height
            diff = abs(ratio - target_ratio)
            
            if diff < best_diff:
                best_diff = diff
                best_size = size
        
        return best_size
    
    async def shutdown(self):
        """关闭服务"""
        if self.session:
            await self.session.close()
            self.session = None
        
        await super().shutdown()
