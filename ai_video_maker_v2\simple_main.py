#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 V2.0 - 简化可用版本
完全可用的基础版本，专注于核心功能
"""

import sys
import os
import json
import asyncio
import aiohttp
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QTabWidget, QMessageBox,
    QProgressBar, QFileDialog, QComboBox, QSpinBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

class AIService:
    """AI服务类 - 处理API调用"""
    
    def __init__(self, config_path="config/config.json"):
        self.config = self.load_config(config_path)
    
    def load_config(self, config_path):
        """加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {
                "llm_apis": {
                    "zhipu": {
                        "api_key": "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY",
                        "base_url": "https://open.bigmodel.cn/api/paas/v4/"
                    }
                }
            }
    
    async def generate_text(self, prompt, model="glm-4"):
        """生成文本"""
        try:
            api_config = self.config["llm_apis"]["zhipu"]
            headers = {
                "Authorization": f"Bearer {api_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{api_config['base_url']}chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        return f"API调用失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"

class WorkerThread(QThread):
    """工作线程"""
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    
    def __init__(self, ai_service, prompt, task_type):
        super().__init__()
        self.ai_service = ai_service
        self.prompt = prompt
        self.task_type = task_type
    
    def run(self):
        """运行任务"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.task_type == "story":
                result = loop.run_until_complete(
                    self.ai_service.generate_text(f"请创作一个有趣的故事：{self.prompt}")
                )
            elif self.task_type == "storyboard":
                result = loop.run_until_complete(
                    self.ai_service.generate_text(f"请将以下故事分解为详细的分镜脚本，包含场景描述、镜头角度、人物动作等：\n\n{self.prompt}")
                )
            else:
                result = loop.run_until_complete(
                    self.ai_service.generate_text(self.prompt)
                )
            
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class SimpleMainWindow(QMainWindow):
    """简化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.ai_service = AIService()
        self.worker = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("AI视频生成器 V2.0 - 可用版本")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧导航
        nav_widget = self.create_navigation()
        main_layout.addWidget(nav_widget, 1)
        
        # 右侧内容
        self.content_widget = self.create_content()
        main_layout.addWidget(self.content_widget, 4)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_navigation(self):
        """创建导航栏"""
        nav_widget = QWidget()
        nav_widget.setMaximumWidth(200)
        nav_widget.setStyleSheet("background-color: #f0f0f0; padding: 10px;")
        
        layout = QVBoxLayout(nav_widget)
        
        # 标题
        title = QLabel("AI视频生成器")
        title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 导航按钮
        buttons = [
            ("📝 文本创作", self.show_text_creation),
            ("🎬 分镜设计", self.show_storyboard),
            ("🎨 图像生成", self.show_image_generation),
            ("🎤 语音制作", self.show_voice_generation),
            ("🎥 视频合成", self.show_video_composition),
            ("⚙️ 设置", self.show_settings)
        ]
        
        for text, callback in buttons:
            btn = QPushButton(text)
            btn.clicked.connect(callback)
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 10px;
                    margin: 2px;
                    background-color: white;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #e3f2fd;
                }
                QPushButton:pressed {
                    background-color: #bbdefb;
                }
            """)
            layout.addWidget(btn)
        
        layout.addStretch()
        return nav_widget
    
    def create_content(self):
        """创建内容区域"""
        content_widget = QTabWidget()
        
        # 文本创作页面
        self.text_page = self.create_text_page()
        content_widget.addTab(self.text_page, "文本创作")
        
        # 分镜设计页面
        self.storyboard_page = self.create_storyboard_page()
        content_widget.addTab(self.storyboard_page, "分镜设计")
        
        # 其他页面
        content_widget.addTab(QLabel("图像生成功能开发中..."), "图像生成")
        content_widget.addTab(QLabel("语音制作功能开发中..."), "语音制作")
        content_widget.addTab(QLabel("视频合成功能开发中..."), "视频合成")
        content_widget.addTab(self.create_settings_page(), "设置")
        
        return content_widget
    
    def create_text_page(self):
        """创建文本创作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        
        # 标题
        title = QLabel("📝 AI文本创作")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 输入区域
        layout.addWidget(QLabel("请输入故事主题或关键词："))
        self.text_input = QTextEdit()
        self.text_input.setMaximumHeight(100)
        self.text_input.setPlaceholderText("例如：一个关于时间旅行的科幻故事...")
        layout.addWidget(self.text_input)
        
        # 按钮
        btn_layout = QHBoxLayout()
        self.generate_story_btn = QPushButton("🚀 生成故事")
        self.generate_story_btn.clicked.connect(self.generate_story)
        btn_layout.addWidget(self.generate_story_btn)
        
        self.save_story_btn = QPushButton("💾 保存故事")
        self.save_story_btn.clicked.connect(self.save_story)
        btn_layout.addWidget(self.save_story_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 输出区域
        layout.addWidget(QLabel("生成的故事："))
        self.story_output = QTextEdit()
        layout.addWidget(self.story_output)
        
        return page
    
    def create_storyboard_page(self):
        """创建分镜设计页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        
        # 标题
        title = QLabel("🎬 AI分镜设计")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # 输入区域
        layout.addWidget(QLabel("请输入故事文本："))
        self.storyboard_input = QTextEdit()
        self.storyboard_input.setMaximumHeight(150)
        self.storyboard_input.setPlaceholderText("粘贴您的故事文本，AI将为您生成详细的分镜脚本...")
        layout.addWidget(self.storyboard_input)
        
        # 按钮
        btn_layout = QHBoxLayout()
        self.generate_storyboard_btn = QPushButton("🎬 生成分镜")
        self.generate_storyboard_btn.clicked.connect(self.generate_storyboard)
        btn_layout.addWidget(self.generate_storyboard_btn)
        
        self.save_storyboard_btn = QPushButton("💾 保存分镜")
        self.save_storyboard_btn.clicked.connect(self.save_storyboard)
        btn_layout.addWidget(self.save_storyboard_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        # 输出区域
        layout.addWidget(QLabel("生成的分镜脚本："))
        self.storyboard_output = QTextEdit()
        layout.addWidget(self.storyboard_output)
        
        return page
    
    def create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        
        # 标题
        title = QLabel("⚙️ 系统设置")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(title)
        
        # API设置
        layout.addWidget(QLabel("API配置："))
        api_info = QLabel(f"智谱AI API: {'已配置' if self.ai_service.config.get('llm_apis', {}).get('zhipu', {}).get('api_key') else '未配置'}")
        layout.addWidget(api_info)
        
        # 其他设置
        layout.addWidget(QLabel("其他设置功能开发中..."))
        
        layout.addStretch()
        return page
    
    # 导航回调函数
    def show_text_creation(self):
        self.content_widget.setCurrentIndex(0)
    
    def show_storyboard(self):
        self.content_widget.setCurrentIndex(1)
    
    def show_image_generation(self):
        self.content_widget.setCurrentIndex(2)
    
    def show_voice_generation(self):
        self.content_widget.setCurrentIndex(3)
    
    def show_video_composition(self):
        self.content_widget.setCurrentIndex(4)
    
    def show_settings(self):
        self.content_widget.setCurrentIndex(5)
    
    # 功能函数
    def generate_story(self):
        """生成故事"""
        prompt = self.text_input.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请输入故事主题或关键词")
            return
        
        self.generate_story_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.statusBar().showMessage("正在生成故事...")
        
        self.worker = WorkerThread(self.ai_service, prompt, "story")
        self.worker.finished.connect(self.on_story_generated)
        self.worker.error.connect(self.on_error)
        self.worker.start()
    
    def on_story_generated(self, result):
        """故事生成完成"""
        self.story_output.setPlainText(result)
        self.generate_story_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("故事生成完成")
    
    def generate_storyboard(self):
        """生成分镜"""
        story_text = self.storyboard_input.toPlainText().strip()
        if not story_text:
            QMessageBox.warning(self, "警告", "请输入故事文本")
            return
        
        self.generate_storyboard_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.statusBar().showMessage("正在生成分镜脚本...")
        
        self.worker = WorkerThread(self.ai_service, story_text, "storyboard")
        self.worker.finished.connect(self.on_storyboard_generated)
        self.worker.error.connect(self.on_error)
        self.worker.start()
    
    def on_storyboard_generated(self, result):
        """分镜生成完成"""
        self.storyboard_output.setPlainText(result)
        self.generate_storyboard_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("分镜脚本生成完成")
    
    def on_error(self, error_msg):
        """处理错误"""
        QMessageBox.critical(self, "错误", f"操作失败：{error_msg}")
        self.generate_story_btn.setEnabled(True)
        self.generate_storyboard_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.statusBar().showMessage("操作失败")
    
    def save_story(self):
        """保存故事"""
        content = self.story_output.toPlainText()
        if not content:
            QMessageBox.warning(self, "警告", "没有内容可保存")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存故事", "story.txt", "文本文件 (*.txt)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                QMessageBox.information(self, "成功", "故事已保存")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")
    
    def save_storyboard(self):
        """保存分镜"""
        content = self.storyboard_output.toPlainText()
        if not content:
            QMessageBox.warning(self, "警告", "没有内容可保存")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存分镜脚本", "storyboard.txt", "文本文件 (*.txt)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                QMessageBox.information(self, "成功", "分镜脚本已保存")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("AI视频生成器 V2.0")
    app.setApplicationVersion("2.0.0")
    
    # 创建主窗口
    window = SimpleMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
