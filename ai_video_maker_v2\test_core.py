#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心功能测试 - 不依赖GUI的基础功能测试

测试AI视频生成器V2.0的核心功能是否正常工作。
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    
    try:
        # 测试核心模块
        from src.core.config_manager import ConfigManager
        from src.core.event_system import EventSystem
        from src.core.state_manager import StateManager
        print("✅ 核心模块导入成功")
        
        # 测试数据模型
        from src.models.project import Project, ProjectMetadata, ProjectSettings
        from src.models.storyboard import Storyboard, Character, Scene, Shot
        print("✅ 数据模型导入成功")
        
        # 测试工作流
        from src.workflows.storyboard_workflow import StoryboardProcessor
        print("✅ 工作流模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_project_functionality():
    """测试项目功能"""
    print("🔍 测试项目功能...")
    
    try:
        from src.models.project import Project
        
        # 创建测试项目
        project = Project.create_new(
            name="核心测试项目",
            description="这是一个核心功能测试项目",
            base_dir="test_data",
            author="测试用户"
        )
        
        print("✅ 项目创建成功")
        print(f"   项目ID: {project.metadata.project_id}")
        print(f"   项目名称: {project.metadata.name}")
        
        # 测试保存项目
        if project.save():
            print("✅ 项目保存成功")
        else:
            print("❌ 项目保存失败")
            return False
        
        # 测试加载项目
        project_file = project._project_file
        loaded_project = Project.load_from_file(str(project_file))
        
        if loaded_project and loaded_project.metadata.name == project.metadata.name:
            print("✅ 项目加载成功")
        else:
            print("❌ 项目加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 项目功能测试失败: {e}")
        return False

def test_storyboard_functionality():
    """测试分镜功能"""
    print("🔍 测试分镜功能...")
    
    try:
        from src.models.storyboard import Storyboard, Character, Scene, Shot, ShotType, CameraMovement
        
        # 创建分镜板
        storyboard = Storyboard()
        storyboard.metadata.title = "核心测试分镜"
        storyboard.metadata.style = "电影风格"
        
        # 添加角色
        character = Character(
            name="测试主角",
            description="故事的主人公",
            appearance="年轻男性，黑发，穿着休闲"
        )
        storyboard.add_character(character)
        
        # 添加场景
        scene = Scene(
            title="测试客厅",
            location="家中客厅",
            time_of_day="下午",
            lighting="自然光"
        )
        storyboard.add_scene(scene)
        
        # 添加镜头
        shot = Shot(
            sequence_number=1,
            scene_id=scene.scene_id,
            description="主角坐在沙发上思考",
            shot_type=ShotType.MEDIUM_SHOT,
            camera_movement=CameraMovement.STATIC,
            duration=3.0
        )
        storyboard.add_shot(shot)
        
        print("✅ 分镜板创建成功")
        print(f"   角色数量: {storyboard.character_count}")
        print(f"   场景数量: {storyboard.scene_count}")
        print(f"   镜头数量: {storyboard.shot_count}")
        print(f"   总时长: {storyboard.total_duration}秒")
        
        # 测试序列化
        storyboard_dict = storyboard.to_dict()
        restored_storyboard = Storyboard.from_dict(storyboard_dict)
        
        if restored_storyboard.shot_count == storyboard.shot_count:
            print("✅ 分镜板序列化成功")
        else:
            print("❌ 分镜板序列化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 分镜功能测试失败: {e}")
        return False

def test_config_functionality():
    """测试配置功能"""
    print("🔍 测试配置功能...")
    
    try:
        from src.core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 测试加载配置
        config_file = "config/config.json"
        if Path(config_file).exists():
            config = config_manager.get_config()
            
            if config:
                print("✅ 配置加载成功")
                print(f"   环境: {config.environment}")
                print(f"   调试模式: {config.debug}")
                return True
            else:
                print("❌ 配置加载失败")
                return False
        else:
            print("⚠️  配置文件不存在，跳过测试")
            return True
            
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        return False

def test_event_system():
    """测试事件系统"""
    print("🔍 测试事件系统...")
    
    try:
        from src.core.event_system import EventSystem
        
        # 创建事件系统
        event_system = EventSystem()
        
        # 测试事件订阅和发布
        received_events = []
        
        def test_handler(event_data):
            received_events.append(event_data)
        
        # 订阅事件
        event_system.subscribe("test_event", test_handler)
        
        # 发布事件
        event_system.publish("test_event", {"message": "测试消息"})
        
        # 验证事件接收
        if len(received_events) == 1 and received_events[0]["message"] == "测试消息":
            print("✅ 事件系统测试成功")
            return True
        else:
            print("❌ 事件系统测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 事件系统测试失败: {e}")
        return False

def test_performance_manager():
    """测试性能管理器"""
    print("🔍 测试性能管理器...")
    
    try:
        from src.core.performance_manager import PerformanceManager
        
        # 创建性能管理器
        config = {
            "memory_limit_mb": 1024,
            "cache_size_mb": 256,
            "max_concurrent_tasks": 2
        }
        performance_manager = PerformanceManager(config)
        
        # 测试缓存功能
        cache = performance_manager.cache_manager
        
        # 设置和获取缓存
        cache.set("test_key", "test_value")
        value = cache.get("test_key")
        
        if value == "test_value":
            print("✅ 缓存功能测试成功")
        else:
            print("❌ 缓存功能测试失败")
            return False
        
        # 测试内存管理
        memory_manager = performance_manager.memory_manager
        usage = memory_manager.get_memory_usage()
        
        if "used_mb" in usage and "percent" in usage:
            print("✅ 内存管理测试成功")
            print(f"   内存使用: {usage['used_mb']:.1f}MB ({usage['percent']:.1f}%)")
        else:
            print("❌ 内存管理测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 性能管理器测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("🧹 清理测试数据...")
    
    import shutil
    
    test_dirs = ["test_data"]
    
    for test_dir in test_dirs:
        test_path = Path(test_dir)
        if test_path.exists():
            try:
                shutil.rmtree(test_path)
                print(f"✅ 已清理: {test_dir}")
            except Exception as e:
                print(f"⚠️  清理失败 {test_dir}: {e}")

def main():
    """主测试函数"""
    print("🎬 AI视频生成器 V2.0 - 核心功能测试")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    test_results = []
    
    # 运行测试
    test_results.append(("核心模块导入", test_core_imports()))
    test_results.append(("项目功能", test_project_functionality()))
    test_results.append(("分镜功能", test_storyboard_functionality()))
    test_results.append(("配置功能", test_config_functionality()))
    test_results.append(("事件系统", test_event_system()))
    test_results.append(("性能管理器", test_performance_manager()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 核心功能测试结果:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} - {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！系统核心架构正常。")
        print("\n📋 下一步:")
        print("1. 安装依赖包: pip install -r requirements.txt")
        print("2. 配置API密钥: 编辑 config/config.json")
        print("3. 运行完整程序: python run.py")
    elif passed > total // 2:
        print("⚠️  大部分核心功能正常，系统基本可用。")
        print("请检查失败的测试项目。")
    else:
        print("❌ 多个核心功能测试失败，请检查系统配置。")
    
    # 清理测试数据
    cleanup_test_data()
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        sys.exit(1)
