# -*- coding: utf-8 -*-
"""
分镜设计页面 - 五阶段智能分镜生成

提供完整的分镜设计功能：
- 五阶段分镜生成
- 分镜预览和编辑
- 一致性检查
- 导出功能
"""

import asyncio
import logging
from typing import Optional, Dict, Any, List
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QProgressBar, QTextEdit, QScrollArea,
                            QFrame, QMessageBox, QTabWidget, QTableWidget,
                            QTableWidgetItem, QHeaderView, QSplitter)
from PyQt6.QtGui import QPixmap, QFont

from ..components.card import Card, CardHeader, CardContent, CardActions
from ..components.button import PrimaryButton, SecondaryButton, LoadingButton
from ..components.input import ModernLineEdit, ModernComboBox
from ...workflows.storyboard_workflow import StoryboardProcessor
from ...models.project import Project
from ...models.storyboard import Storyboard
from ...core.consistency_manager import ConsistencyManager

logger = logging.getLogger(__name__)

class StoryboardGenerationThread(QThread):
    """分镜生成线程"""
    
    stage_completed = pyqtSignal(int, dict)  # 阶段号, 结果
    progress_updated = pyqtSignal(int, str)  # 进度, 消息
    generation_finished = pyqtSignal(bool, str, object)  # 成功, 消息, 分镜板
    
    def __init__(self, service_manager, text: str, style: str):
        super().__init__()
        self.service_manager = service_manager
        self.text = text
        self.style = style
        self.processor = StoryboardProcessor(service_manager)
    
    def run(self):
        """执行分镜生成"""
        try:
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            stage_results = {}
            
            # 阶段1: 世界观构建
            self.progress_updated.emit(1, "正在构建世界观...")
            stage1_result = loop.run_until_complete(
                self.processor.process_stage_1_world_building(self.text, self.style)
            )
            
            if not stage1_result.success:
                self.generation_finished.emit(False, f"阶段1失败: {stage1_result.error}", None)
                return
            
            stage_results["stage_1"] = stage1_result.data
            self.stage_completed.emit(1, stage1_result.data)
            
            # 阶段2: 角色场景分析
            self.progress_updated.emit(2, "正在分析角色和场景...")
            stage2_result = loop.run_until_complete(
                self.processor.process_stage_2_character_scene_analysis(
                    self.text, stage1_result.data.get("world_setting", "")
                )
            )
            
            if not stage2_result.success:
                self.generation_finished.emit(False, f"阶段2失败: {stage2_result.error}", None)
                return
            
            stage_results["stage_2"] = stage2_result.data
            self.stage_completed.emit(2, stage2_result.data)
            
            # 阶段3: 情节结构分析
            self.progress_updated.emit(3, "正在分析情节结构...")
            stage3_result = loop.run_until_complete(
                self.processor.process_stage_3_plot_structure(
                    self.text,
                    stage2_result.data.get("characters", []),
                    stage2_result.data.get("scenes", [])
                )
            )
            
            if not stage3_result.success:
                self.generation_finished.emit(False, f"阶段3失败: {stage3_result.error}", None)
                return
            
            stage_results["stage_3"] = stage3_result.data
            self.stage_completed.emit(3, stage3_result.data)
            
            # 阶段4: 分镜脚本生成
            self.progress_updated.emit(4, "正在生成分镜脚本...")
            stage4_result = loop.run_until_complete(
                self.processor.process_stage_4_shot_generation(
                    stage3_result.data.get("plot_segments", []),
                    stage2_result.data.get("characters", []),
                    stage2_result.data.get("scenes", []),
                    self.style
                )
            )
            
            if not stage4_result.success:
                self.generation_finished.emit(False, f"阶段4失败: {stage4_result.error}", None)
                return
            
            stage_results["stage_4"] = stage4_result.data
            self.stage_completed.emit(4, stage4_result.data)
            
            # 阶段5: 优化和完善
            self.progress_updated.emit(5, "正在优化分镜...")
            stage5_result = loop.run_until_complete(
                self.processor.process_stage_5_optimization(
                    stage4_result.data.get("shots", []),
                    self.style
                )
            )
            
            if not stage5_result.success:
                self.generation_finished.emit(False, f"阶段5失败: {stage5_result.error}", None)
                return
            
            stage_results["stage_5"] = stage5_result.data
            self.stage_completed.emit(5, stage5_result.data)
            
            # 创建分镜板对象
            storyboard = loop.run_until_complete(
                self.processor.create_storyboard_from_results(
                    self.text, stage_results, self.style
                )
            )
            
            self.generation_finished.emit(True, "分镜生成完成", storyboard)
            
        except Exception as e:
            logger.error(f"分镜生成异常: {e}")
            self.generation_finished.emit(False, str(e), None)
        finally:
            loop.close()

class StageProgressCard(Card):
    """阶段进度卡片"""
    
    def __init__(self, stage_num: int, stage_name: str, parent=None):
        super().__init__(parent)
        self.stage_num = stage_num
        self.stage_name = stage_name
        self._setup_ui()
    
    def _setup_ui(self):
        """设置界面"""
        header = CardHeader(f"阶段{self.stage_num}: {self.stage_name}", "")
        self.add_widget(header)
        
        content = CardContent()
        
        # 状态指示器
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("等待中...")
        self.status_label.setStyleSheet("color: #757575; font-size: 14px;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.progress_indicator = QLabel("⏳")
        self.progress_indicator.setStyleSheet("font-size: 16px;")
        status_layout.addWidget(self.progress_indicator)
        
        content.add_layout(status_layout)
        
        # 结果预览
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(100)
        self.result_text.setVisible(False)
        content.add_widget(self.result_text)
        
        self.add_widget(content)
    
    def set_status(self, status: str):
        """设置状态"""
        if status == "waiting":
            self.status_label.setText("等待中...")
            self.status_label.setStyleSheet("color: #757575;")
            self.progress_indicator.setText("⏳")
        elif status == "processing":
            self.status_label.setText("处理中...")
            self.status_label.setStyleSheet("color: #2196F3;")
            self.progress_indicator.setText("🔄")
        elif status == "completed":
            self.status_label.setText("已完成")
            self.status_label.setStyleSheet("color: #4CAF50;")
            self.progress_indicator.setText("✅")
        elif status == "failed":
            self.status_label.setText("失败")
            self.status_label.setStyleSheet("color: #F44336;")
            self.progress_indicator.setText("❌")
    
    def set_result(self, result: Dict[str, Any]):
        """设置结果"""
        if result:
            # 显示结果摘要
            summary = self._format_result_summary(result)
            self.result_text.setPlainText(summary)
            self.result_text.setVisible(True)
    
    def _format_result_summary(self, result: Dict[str, Any]) -> str:
        """格式化结果摘要"""
        if self.stage_num == 1:
            # 世界观构建结果
            return f"世界观设定: {result.get('world_setting', '')[:100]}..."
        elif self.stage_num == 2:
            # 角色场景分析结果
            characters = result.get('characters', [])
            scenes = result.get('scenes', [])
            return f"识别角色: {len(characters)}个, 场景: {len(scenes)}个"
        elif self.stage_num == 3:
            # 情节结构分析结果
            segments = result.get('plot_segments', [])
            return f"情节段落: {len(segments)}个"
        elif self.stage_num == 4:
            # 分镜脚本生成结果
            shots = result.get('shots', [])
            return f"生成镜头: {len(shots)}个"
        elif self.stage_num == 5:
            # 优化建议结果
            return f"优化建议: {result.get('optimization_suggestions', '')[:100]}..."
        
        return str(result)[:200] + "..."

class StoryboardPage(QWidget):
    """分镜设计页面"""
    
    def __init__(self, app_controller=None, parent=None):
        super().__init__(parent)
        self.app_controller = app_controller
        self.current_project: Optional[Project] = None
        self.current_storyboard: Optional[Storyboard] = None
        self.generation_thread: Optional[StoryboardGenerationThread] = None
        self.consistency_manager = ConsistencyManager()
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)
        
        # 页面标题
        title_card = Card()
        title_header = CardHeader("分镜设计", "五阶段智能分镜生成")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：生成控制和进度
        left_panel = self._create_generation_panel()
        splitter.addWidget(left_panel)
        
        # 右侧：分镜预览和编辑
        right_panel = self._create_preview_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
    
    def _create_generation_panel(self) -> QWidget:
        """创建生成面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)
        
        # 生成设置卡片
        self.settings_card = self._create_settings_card()
        layout.addWidget(self.settings_card)
        
        # 五阶段进度卡片
        self.stage_cards = []
        stages = [
            (1, "世界观构建"),
            (2, "角色场景分析"),
            (3, "情节结构分析"),
            (4, "分镜脚本生成"),
            (5, "优化完善")
        ]
        
        for stage_num, stage_name in stages:
            stage_card = StageProgressCard(stage_num, stage_name)
            self.stage_cards.append(stage_card)
            layout.addWidget(stage_card)
        
        layout.addStretch()
        
        return panel
    
    def _create_settings_card(self) -> Card:
        """创建设置卡片"""
        card = Card()
        header = CardHeader("生成设置", "配置分镜生成参数")
        card.add_widget(header)
        
        content = CardContent()
        
        # 风格选择
        style_layout = QVBoxLayout()
        
        style_layout.addWidget(QLabel("视觉风格:"))
        self.style_combo = ModernComboBox()
        self.style_combo.addItems([
            "电影风格",
            "动画风格",
            "纪录片风格",
            "广告风格",
            "艺术风格"
        ])
        style_layout.addWidget(self.style_combo)
        
        content.add_layout(style_layout)
        card.add_widget(content)
        
        # 操作按钮
        actions = CardActions()
        
        self.generate_btn = LoadingButton("开始生成分镜")
        self.generate_btn.setEnabled(False)
        actions.add_button(self.generate_btn)
        
        self.stop_btn = SecondaryButton("停止生成")
        self.stop_btn.setEnabled(False)
        actions.add_button(self.stop_btn)
        
        card.add_widget(actions)
        
        return card
    
    def _create_preview_panel(self) -> QWidget:
        """创建预览面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)
        
        # 分镜预览标签页
        self.preview_tabs = QTabWidget()
        
        # 镜头列表标签页
        self.shots_tab = self._create_shots_tab()
        self.preview_tabs.addTab(self.shots_tab, "镜头列表")
        
        # 角色列表标签页
        self.characters_tab = self._create_characters_tab()
        self.preview_tabs.addTab(self.characters_tab, "角色列表")
        
        # 场景列表标签页
        self.scenes_tab = self._create_scenes_tab()
        self.preview_tabs.addTab(self.scenes_tab, "场景列表")
        
        # 一致性检查标签页
        self.consistency_tab = self._create_consistency_tab()
        self.preview_tabs.addTab(self.consistency_tab, "一致性检查")
        
        layout.addWidget(self.preview_tabs)
        
        return panel
    
    def _create_shots_tab(self) -> QWidget:
        """创建镜头标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 镜头表格
        self.shots_table = QTableWidget()
        self.shots_table.setColumnCount(6)
        self.shots_table.setHorizontalHeaderLabels([
            "序号", "镜头类型", "时长", "描述", "对话", "图像提示"
        ])
        
        # 设置表格属性
        header = self.shots_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 描述列自适应
        
        layout.addWidget(self.shots_table)
        
        return tab
    
    def _create_characters_tab(self) -> QWidget:
        """创建角色标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 角色表格
        self.characters_table = QTableWidget()
        self.characters_table.setColumnCount(4)
        self.characters_table.setHorizontalHeaderLabels([
            "姓名", "外貌", "性格", "作用"
        ])
        
        # 设置表格属性
        header = self.characters_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # 外貌列自适应
        
        layout.addWidget(self.characters_table)
        
        return tab
    
    def _create_scenes_tab(self) -> QWidget:
        """创建场景标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 场景表格
        self.scenes_table = QTableWidget()
        self.scenes_table.setColumnCount(5)
        self.scenes_table.setHorizontalHeaderLabels([
            "名称", "位置", "时间", "环境", "光线"
        ])
        
        # 设置表格属性
        header = self.scenes_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 环境列自适应
        
        layout.addWidget(self.scenes_table)
        
        return tab
    
    def _create_consistency_tab(self) -> QWidget:
        """创建一致性检查标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 一致性报告
        self.consistency_text = QTextEdit()
        self.consistency_text.setPlaceholderText("生成分镜后将显示一致性检查报告...")
        layout.addWidget(self.consistency_text)
        
        # 检查按钮
        check_layout = QHBoxLayout()
        check_layout.addStretch()
        
        self.check_consistency_btn = SecondaryButton("检查一致性")
        self.check_consistency_btn.setEnabled(False)
        check_layout.addWidget(self.check_consistency_btn)
        
        layout.addLayout(check_layout)
        
        return tab
    
    def _setup_connections(self):
        """设置信号连接"""
        self.generate_btn.clicked.connect(self._start_generation)
        self.stop_btn.clicked.connect(self._stop_generation)
        self.check_consistency_btn.clicked.connect(self._check_consistency)

    def set_project(self, project: Project):
        """设置当前项目"""
        self.current_project = project

        # 检查项目是否有文本内容
        if project and project.data.text_content:
            self.generate_btn.setEnabled(True)
        else:
            self.generate_btn.setEnabled(False)

        # 加载已有的分镜数据
        if project and project.data.storyboard_data:
            self.current_storyboard = Storyboard.from_dict(project.data.storyboard_data)
            self._update_preview()

    def _start_generation(self):
        """开始生成分镜"""
        if not self.current_project or not self.current_project.data.text_content:
            QMessageBox.warning(self, "警告", "请先在文本创作页面输入故事内容")
            return

        if not self.app_controller or not self.app_controller.service_manager:
            QMessageBox.warning(self, "警告", "AI服务未初始化")
            return

        # 重置阶段状态
        for stage_card in self.stage_cards:
            stage_card.set_status("waiting")

        # 启动生成线程
        text = self.current_project.data.text_content
        style = self.style_combo.currentText()

        self.generation_thread = StoryboardGenerationThread(
            self.app_controller.service_manager,
            text,
            style
        )

        # 连接信号
        self.generation_thread.stage_completed.connect(self._on_stage_completed)
        self.generation_thread.progress_updated.connect(self._on_progress_updated)
        self.generation_thread.generation_finished.connect(self._on_generation_finished)

        # 更新UI状态
        self.generate_btn.set_loading(True)
        self.stop_btn.setEnabled(True)

        # 启动线程
        self.generation_thread.start()

    def _stop_generation(self):
        """停止生成"""
        if self.generation_thread and self.generation_thread.isRunning():
            self.generation_thread.terminate()
            self.generation_thread.wait()

            # 重置UI状态
            self._reset_generation_ui()

            QMessageBox.information(self, "提示", "分镜生成已停止")

    def _on_stage_completed(self, stage_num: int, result: Dict[str, Any]):
        """阶段完成回调"""
        if stage_num <= len(self.stage_cards):
            stage_card = self.stage_cards[stage_num - 1]
            stage_card.set_status("completed")
            stage_card.set_result(result)

    def _on_progress_updated(self, stage_num: int, message: str):
        """进度更新回调"""
        if stage_num <= len(self.stage_cards):
            stage_card = self.stage_cards[stage_num - 1]
            stage_card.set_status("processing")

    def _on_generation_finished(self, success: bool, message: str, storyboard: Optional[Storyboard]):
        """生成完成回调"""
        self._reset_generation_ui()

        if success and storyboard:
            self.current_storyboard = storyboard

            # 保存到项目
            if self.current_project:
                self.current_project.set_storyboard_data(storyboard.to_dict())
                self.current_project.save()

            # 更新预览
            self._update_preview()

            # 启用一致性检查
            self.check_consistency_btn.setEnabled(True)

            QMessageBox.information(self, "成功", "分镜生成完成！")
        else:
            # 标记失败的阶段
            for stage_card in self.stage_cards:
                if stage_card.status_label.text() == "处理中...":
                    stage_card.set_status("failed")
                    break

            QMessageBox.critical(self, "生成失败", f"分镜生成失败:\n{message}")

    def _reset_generation_ui(self):
        """重置生成UI状态"""
        self.generate_btn.set_loading(False)
        self.stop_btn.setEnabled(False)

    def _update_preview(self):
        """更新预览"""
        if not self.current_storyboard:
            return

        # 更新镜头表格
        self._update_shots_table()

        # 更新角色表格
        self._update_characters_table()

        # 更新场景表格
        self._update_scenes_table()

    def _update_shots_table(self):
        """更新镜头表格"""
        if not self.current_storyboard:
            return

        shots = self.current_storyboard.shots
        self.shots_table.setRowCount(len(shots))

        for i, shot in enumerate(shots):
            # 序号
            self.shots_table.setItem(i, 0, QTableWidgetItem(str(shot.sequence_number)))

            # 镜头类型
            self.shots_table.setItem(i, 1, QTableWidgetItem(shot.shot_type.value if shot.shot_type else ""))

            # 时长
            self.shots_table.setItem(i, 2, QTableWidgetItem(f"{shot.duration}秒"))

            # 描述
            self.shots_table.setItem(i, 3, QTableWidgetItem(shot.description or ""))

            # 对话
            self.shots_table.setItem(i, 4, QTableWidgetItem(shot.dialogue or ""))

            # 图像提示
            self.shots_table.setItem(i, 5, QTableWidgetItem(shot.image_prompt or ""))

    def _update_characters_table(self):
        """更新角色表格"""
        if not self.current_storyboard:
            return

        characters = list(self.current_storyboard.characters.values())
        self.characters_table.setRowCount(len(characters))

        for i, character in enumerate(characters):
            # 姓名
            self.characters_table.setItem(i, 0, QTableWidgetItem(character.name))

            # 外貌
            self.characters_table.setItem(i, 1, QTableWidgetItem(character.appearance or ""))

            # 性格
            self.characters_table.setItem(i, 2, QTableWidgetItem(character.personality or ""))

            # 作用
            self.characters_table.setItem(i, 3, QTableWidgetItem(character.role or ""))

    def _update_scenes_table(self):
        """更新场景表格"""
        if not self.current_storyboard:
            return

        scenes = list(self.current_storyboard.scenes.values())
        self.scenes_table.setRowCount(len(scenes))

        for i, scene in enumerate(scenes):
            # 名称
            self.scenes_table.setItem(i, 0, QTableWidgetItem(scene.title))

            # 位置
            self.scenes_table.setItem(i, 1, QTableWidgetItem(scene.location or ""))

            # 时间
            self.scenes_table.setItem(i, 2, QTableWidgetItem(scene.time_of_day or ""))

            # 环境
            self.scenes_table.setItem(i, 3, QTableWidgetItem(scene.environment or ""))

            # 光线
            self.scenes_table.setItem(i, 4, QTableWidgetItem(scene.lighting or ""))

    def _check_consistency(self):
        """检查一致性"""
        if not self.current_storyboard:
            QMessageBox.warning(self, "警告", "请先生成分镜")
            return

        try:
            # 执行一致性检查
            report = self.consistency_manager.analyze_consistency(
                self.current_storyboard,
                self.current_project
            )

            # 格式化报告
            report_text = self._format_consistency_report(report)
            self.consistency_text.setPlainText(report_text)

            # 切换到一致性检查标签页
            self.preview_tabs.setCurrentWidget(self.consistency_tab)

        except Exception as e:
            QMessageBox.critical(self, "检查失败", f"一致性检查失败:\n{e}")

    def _format_consistency_report(self, report) -> str:
        """格式化一致性报告"""
        lines = []

        # 总体评分
        lines.append("=== 一致性检查报告 ===\n")
        lines.append(f"总体评分: {report.overall_score:.2%}")
        lines.append(f"问题总数: {report.total_issues}")
        lines.append(f"  - 错误: {report.error_count}")
        lines.append(f"  - 警告: {report.warning_count}")
        lines.append(f"  - 建议: {report.suggestion_count}")
        lines.append("")

        # 角色一致性
        if report.character_consistency:
            lines.append("=== 角色一致性 ===")
            for char_id, score in report.character_consistency.items():
                character_name = "未知角色"
                if self.current_storyboard and char_id in self.current_storyboard.characters:
                    character_name = self.current_storyboard.characters[char_id].name
                lines.append(f"{character_name}: {score:.2%}")
            lines.append("")

        # 场景一致性
        if report.scene_consistency:
            lines.append("=== 场景一致性 ===")
            for scene_id, score in report.scene_consistency.items():
                scene_name = "未知场景"
                if self.current_storyboard and scene_id in self.current_storyboard.scenes:
                    scene_name = self.current_storyboard.scenes[scene_id].title
                lines.append(f"{scene_name}: {score:.2%}")
            lines.append("")

        # 问题详情
        if report.issues:
            lines.append("=== 发现的问题 ===")
            for issue in report.issues:
                severity_icon = {"error": "❌", "warning": "⚠️", "suggestion": "💡"}.get(issue.severity, "")
                lines.append(f"{severity_icon} {issue.title}")
                lines.append(f"   {issue.description}")
                if issue.suggestions:
                    lines.append("   建议:")
                    for suggestion in issue.suggestions:
                        lines.append(f"   - {suggestion}")
                lines.append("")

        # 优化建议
        if hasattr(report, 'optimization_suggestions'):
            suggestions = self.consistency_manager.generate_optimization_suggestions(report)
            if suggestions:
                lines.append("=== 优化建议 ===")
                for suggestion in suggestions:
                    lines.append(f"• {suggestion}")
                lines.append("")

        return "\n".join(lines)
