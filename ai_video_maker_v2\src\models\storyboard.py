# -*- coding: utf-8 -*-
"""
分镜模型 - 分镜数据结构

定义分镜相关的数据结构：
- 角色模型
- 场景模型
- 镜头模型
- 分镜板模型
"""

import uuid
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any

class ShotType(Enum):
    """镜头类型"""
    CLOSE_UP = "close_up"          # 特写
    MEDIUM_SHOT = "medium_shot"    # 中景
    LONG_SHOT = "long_shot"        # 远景
    WIDE_SHOT = "wide_shot"        # 全景
    EXTREME_CLOSE_UP = "extreme_close_up"  # 大特写
    EXTREME_LONG_SHOT = "extreme_long_shot"  # 大远景

class CameraMovement(Enum):
    """摄像机运动"""
    STATIC = "static"              # 静止
    PAN = "pan"                    # 摇镜
    TILT = "tilt"                  # 俯仰
    ZOOM_IN = "zoom_in"            # 推镜
    ZOOM_OUT = "zoom_out"          # 拉镜
    DOLLY = "dolly"                # 移动
    TRACKING = "tracking"          # 跟踪

@dataclass
class Character:
    """角色模型"""
    character_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    appearance: str = ""
    personality: str = ""
    voice_style: str = ""
    
    # 视觉特征
    age: Optional[str] = None
    gender: Optional[str] = None
    clothing: str = ""
    accessories: str = ""
    
    # 角色关系
    relationships: Dict[str, str] = field(default_factory=dict)
    
    # 一致性参考
    reference_images: List[str] = field(default_factory=list)
    style_keywords: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """从字典创建"""
        return cls(**data)

@dataclass
class Scene:
    """场景模型"""
    scene_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    location: str = ""
    time_of_day: str = ""
    weather: str = ""
    mood: str = ""
    
    # 场景元素
    environment: str = ""
    lighting: str = ""
    atmosphere: str = ""
    
    # 参与角色
    characters: List[str] = field(default_factory=list)  # character_ids
    
    # 场景设置
    background_music: str = ""
    sound_effects: List[str] = field(default_factory=list)
    
    # 一致性参考
    reference_images: List[str] = field(default_factory=list)
    style_keywords: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Scene':
        """从字典创建"""
        return cls(**data)

@dataclass
class Shot:
    """镜头模型"""
    shot_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sequence_number: int = 0
    scene_id: str = ""
    
    # 镜头内容
    description: str = ""
    dialogue: str = ""
    action: str = ""
    
    # 镜头技术参数
    shot_type: ShotType = ShotType.MEDIUM_SHOT
    camera_movement: CameraMovement = CameraMovement.STATIC
    duration: float = 3.0  # 秒
    
    # 视觉描述
    visual_description: str = ""
    image_prompt: str = ""
    
    # 音频信息
    voice_over: str = ""
    sound_effects: List[str] = field(default_factory=list)
    background_music: str = ""
    
    # 参与角色
    characters: List[str] = field(default_factory=list)  # character_ids
    
    # 生成的媒体资源
    generated_image: Optional[str] = None  # 图像文件路径
    generated_audio: Optional[str] = None  # 音频文件路径
    generated_video: Optional[str] = None  # 视频文件路径
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['shot_type'] = self.shot_type.value
        data['camera_movement'] = self.camera_movement.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Shot':
        """从字典创建"""
        data = data.copy()
        if 'shot_type' in data:
            data['shot_type'] = ShotType(data['shot_type'])
        if 'camera_movement' in data:
            data['camera_movement'] = CameraMovement(data['camera_movement'])
        if 'created_at' in data:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)

@dataclass
class StoryboardMetadata:
    """分镜板元数据"""
    storyboard_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    style: str = "电影风格"
    genre: str = ""
    target_duration: float = 0.0  # 目标时长（秒）
    
    # 创建信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    created_by: str = ""
    
    # 版本信息
    version: str = "1.0"
    revision: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StoryboardMetadata':
        """从字典创建"""
        data = data.copy()
        if 'created_at' in data:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)

@dataclass
class Storyboard:
    """分镜板模型"""
    metadata: StoryboardMetadata = field(default_factory=StoryboardMetadata)
    
    # 原始文本
    original_text: str = ""
    processed_text: str = ""
    
    # 世界观设定
    world_setting: str = ""
    visual_style: str = ""
    
    # 角色和场景
    characters: Dict[str, Character] = field(default_factory=dict)
    scenes: Dict[str, Scene] = field(default_factory=dict)
    
    # 镜头序列
    shots: List[Shot] = field(default_factory=list)
    
    # 五阶段数据
    stage_data: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    total_duration: float = 0.0
    shot_count: int = 0
    character_count: int = 0
    scene_count: int = 0
    
    def add_character(self, character: Character):
        """添加角色"""
        self.characters[character.character_id] = character
        self.character_count = len(self.characters)
        self._update_timestamp()
    
    def add_scene(self, scene: Scene):
        """添加场景"""
        self.scenes[scene.scene_id] = scene
        self.scene_count = len(self.scenes)
        self._update_timestamp()
    
    def add_shot(self, shot: Shot):
        """添加镜头"""
        self.shots.append(shot)
        self.shot_count = len(self.shots)
        self.total_duration = sum(shot.duration for shot in self.shots)
        self._update_timestamp()
    
    def get_character(self, character_id: str) -> Optional[Character]:
        """获取角色"""
        return self.characters.get(character_id)
    
    def get_scene(self, scene_id: str) -> Optional[Scene]:
        """获取场景"""
        return self.scenes.get(scene_id)
    
    def get_shot(self, shot_id: str) -> Optional[Shot]:
        """获取镜头"""
        for shot in self.shots:
            if shot.shot_id == shot_id:
                return shot
        return None
    
    def get_shots_by_scene(self, scene_id: str) -> List[Shot]:
        """获取场景的所有镜头"""
        return [shot for shot in self.shots if shot.scene_id == scene_id]
    
    def get_shots_by_character(self, character_id: str) -> List[Shot]:
        """获取角色参与的所有镜头"""
        return [shot for shot in self.shots if character_id in shot.characters]
    
    def update_shot_sequence(self):
        """更新镜头序号"""
        for i, shot in enumerate(self.shots):
            shot.sequence_number = i + 1
        self._update_timestamp()
    
    def set_stage_data(self, stage: str, data: Any):
        """设置阶段数据"""
        self.stage_data[stage] = data
        self._update_timestamp()
    
    def get_stage_data(self, stage: str) -> Any:
        """获取阶段数据"""
        return self.stage_data.get(stage)
    
    def _update_timestamp(self):
        """更新时间戳"""
        self.metadata.updated_at = datetime.now()
    
    def get_summary(self) -> Dict[str, Any]:
        """获取分镜板摘要"""
        return {
            'storyboard_id': self.metadata.storyboard_id,
            'title': self.metadata.title,
            'style': self.metadata.style,
            'total_duration': self.total_duration,
            'shot_count': self.shot_count,
            'character_count': self.character_count,
            'scene_count': self.scene_count,
            'created_at': self.metadata.created_at.isoformat(),
            'updated_at': self.metadata.updated_at.isoformat()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'metadata': self.metadata.to_dict(),
            'original_text': self.original_text,
            'processed_text': self.processed_text,
            'world_setting': self.world_setting,
            'visual_style': self.visual_style,
            'characters': {k: v.to_dict() for k, v in self.characters.items()},
            'scenes': {k: v.to_dict() for k, v in self.scenes.items()},
            'shots': [shot.to_dict() for shot in self.shots],
            'stage_data': self.stage_data,
            'total_duration': self.total_duration,
            'shot_count': self.shot_count,
            'character_count': self.character_count,
            'scene_count': self.scene_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Storyboard':
        """从字典创建"""
        metadata = StoryboardMetadata.from_dict(data.get('metadata', {}))
        
        characters = {}
        for char_id, char_data in data.get('characters', {}).items():
            characters[char_id] = Character.from_dict(char_data)
        
        scenes = {}
        for scene_id, scene_data in data.get('scenes', {}).items():
            scenes[scene_id] = Scene.from_dict(scene_data)
        
        shots = []
        for shot_data in data.get('shots', []):
            shots.append(Shot.from_dict(shot_data))
        
        return cls(
            metadata=metadata,
            original_text=data.get('original_text', ''),
            processed_text=data.get('processed_text', ''),
            world_setting=data.get('world_setting', ''),
            visual_style=data.get('visual_style', ''),
            characters=characters,
            scenes=scenes,
            shots=shots,
            stage_data=data.get('stage_data', {}),
            total_duration=data.get('total_duration', 0.0),
            shot_count=data.get('shot_count', 0),
            character_count=data.get('character_count', 0),
            scene_count=data.get('scene_count', 0)
        )
