#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 V2.0 - 独立运行脚本

这个脚本确保程序可以完全独立运行，不依赖任何外部代码。
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.absolute()
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志系统"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'ai_video_maker.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print(f"❌ Python版本过低: {sys.version}")
        print("   需要Python 3.9或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的目录
    required_dirs = [
        "src",
        "config", 
        "data",
        "assets",
        "docs"
    ]
    
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            print(f"⚠️  创建目录: {dir_name}")
            dir_path.mkdir(parents=True, exist_ok=True)
        else:
            print(f"✅ 目录存在: {dir_name}")
    
    # 检查配置文件
    config_file = project_root / "config" / "config.json"
    if not config_file.exists():
        print("⚠️  配置文件不存在，创建默认配置...")
        create_default_config()
    else:
        print("✅ 配置文件存在")
    
    return True

def create_default_config():
    """创建默认配置文件"""
    config_dir = project_root / "config"
    config_dir.mkdir(exist_ok=True)
    
    default_config = {
        "environment": "development",
        "debug": True,
        "log_level": "INFO",
        "data_dir": "data",
        "cache_dir": "cache",
        
        "ui": {
            "theme": "light",
            "language": "zh_CN",
            "window_width": 1400,
            "window_height": 900,
            "auto_save_interval": 300
        },
        
        "performance": {
            "max_concurrent_tasks": 3,
            "cache_size_mb": 512,
            "enable_gpu": True,
            "memory_limit_mb": 2048
        },
        
        "llm_apis": {
            "zhipu": {
                "provider": "zhipu",
                "api_key": "",
                "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                "timeout": 30,
                "max_retries": 3
            }
        },
        
        "image_apis": {
            "cogview": {
                "provider": "cogview", 
                "api_key": "",
                "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                "timeout": 60,
                "max_retries": 3
            }
        },
        
        "voice_apis": {
            "azure": {
                "provider": "azure",
                "api_key": "",
                "base_url": "",
                "timeout": 30,
                "max_retries": 3
            }
        },
        
        "video_apis": {
            "cogvideo": {
                "provider": "cogvideo",
                "api_key": "",
                "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                "timeout": 120,
                "max_retries": 3
            }
        }
    }
    
    import json
    config_file = config_dir / "config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 默认配置已创建: {config_file}")

def check_dependencies():
    """检查依赖包"""
    print("📦 检查依赖包...")
    
    required_packages = [
        ('PyQt6', 'PyQt6'),
        ('asyncio', None),  # 内置模块
        ('aiohttp', 'aiohttp'),
        ('PIL', 'Pillow'),
        ('numpy', 'numpy')
    ]
    
    missing_packages = []
    
    for package_name, install_name in required_packages:
        try:
            if package_name == 'PIL':
                import PIL
            else:
                __import__(package_name)
            print(f"✅ {package_name}")
        except ImportError:
            missing_packages.append(install_name or package_name)
            print(f"❌ {package_name}")
    
    if missing_packages:
        print("\n⚠️  缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def show_welcome():
    """显示欢迎信息"""
    print("\n" + "="*60)
    print("🎬 AI视频生成器 V2.0")
    print("="*60)
    print("现代化的AI视频创作工具")
    print("- 五阶段智能分镜生成")
    print("- 多引擎AI服务集成") 
    print("- 完整的视频制作流程")
    print("- 现代化卡片式界面")
    print("="*60)

def show_api_key_reminder():
    """显示API密钥提醒"""
    config_file = project_root / "config" / "config.json"
    
    print("\n⚠️  重要提醒:")
    print("首次使用前，请配置API密钥:")
    print(f"1. 编辑配置文件: {config_file}")
    print("2. 添加您的API密钥:")
    print("   - 智谱AI: llm_apis.zhipu.api_key")
    print("   - CogView: image_apis.cogview.api_key") 
    print("   - Azure语音: voice_apis.azure.api_key")
    print("3. 保存配置文件并重启程序")
    print("\n详细配置说明请参考: docs/INSTALLATION.md")

async def run_application():
    """运行应用程序"""
    try:
        # 导入主程序
        from main import Application
        
        # 创建并运行应用
        app = Application()
        return await app.run()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有源代码文件完整")
        return 1
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        return 1

def main():
    """主函数"""
    try:
        # 显示欢迎信息
        show_welcome()
        
        # 设置日志
        setup_logging()
        
        # 检查环境
        if not check_environment():
            print("\n❌ 环境检查失败")
            return 1
        
        # 检查依赖
        if not check_dependencies():
            print("\n❌ 依赖检查失败")
            return 1
        
        # 显示API密钥提醒
        show_api_key_reminder()
        
        print("\n🚀 启动应用程序...")
        
        # 设置事件循环策略（Windows）
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行应用程序
        return asyncio.run(run_application())
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
        return 0
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        logging.exception("程序异常")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
