# -*- coding: utf-8 -*-
"""
配置管理器 - 统一管理应用程序配置

提供类型安全的配置管理，支持：
- 多环境配置
- 配置验证
- 热重载
- 默认值处理
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union, TypeVar, Generic
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')

class ConfigError(Exception):
    """配置相关错误"""
    pass

class Environment(Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"

@dataclass
class APIConfig:
    """API配置"""
    provider: str
    api_key: str
    base_url: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    
@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "light"
    language: str = "zh_CN"
    window_width: int = 1400
    window_height: int = 900
    auto_save_interval: int = 300  # 秒
    
@dataclass
class PerformanceConfig:
    """性能配置"""
    max_concurrent_tasks: int = 3
    cache_size_mb: int = 512
    enable_gpu: bool = True
    memory_limit_mb: int = 2048

@dataclass
class AppConfig:
    """应用程序配置"""
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    log_level: str = "INFO"
    data_dir: str = "data"
    cache_dir: str = "cache"
    
    # 子配置
    ui: UIConfig = None
    performance: PerformanceConfig = None
    
    # API配置
    llm_apis: Dict[str, APIConfig] = None
    image_apis: Dict[str, APIConfig] = None
    voice_apis: Dict[str, APIConfig] = None
    video_apis: Dict[str, APIConfig] = None
    
    def __post_init__(self):
        if self.ui is None:
            self.ui = UIConfig()
        if self.performance is None:
            self.performance = PerformanceConfig()
        if self.llm_apis is None:
            self.llm_apis = {}
        if self.image_apis is None:
            self.image_apis = {}
        if self.voice_apis is None:
            self.voice_apis = {}
        if self.video_apis is None:
            self.video_apis = {}

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self._config: Optional[AppConfig] = None
        self._config_file: Optional[Path] = None
        
        # 加载配置
        self._load_config()
        
    def _load_config(self):
        """加载配置文件"""
        try:
            # 尝试加载主配置文件
            config_file = self.config_dir / "config.json"
            if config_file.exists():
                self._config_file = config_file
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self._config = self._parse_config(config_data)
            else:
                # 使用默认配置
                logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
                self._config = AppConfig()
                self._save_default_config()
                
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            self._config = AppConfig()
            
    def _parse_config(self, config_data: Dict[str, Any]) -> AppConfig:
        """解析配置数据"""
        try:
            # 解析环境
            env_str = config_data.get('environment', 'development')
            environment = Environment(env_str)
            
            # 解析UI配置
            ui_data = config_data.get('ui', {})
            ui_config = UIConfig(**ui_data)
            
            # 解析性能配置
            perf_data = config_data.get('performance', {})
            performance_config = PerformanceConfig(**perf_data)
            
            # 解析API配置
            llm_apis = {}
            for name, api_data in config_data.get('llm_apis', {}).items():
                llm_apis[name] = APIConfig(**api_data)
                
            image_apis = {}
            for name, api_data in config_data.get('image_apis', {}).items():
                image_apis[name] = APIConfig(**api_data)
                
            voice_apis = {}
            for name, api_data in config_data.get('voice_apis', {}).items():
                voice_apis[name] = APIConfig(**api_data)
                
            video_apis = {}
            for name, api_data in config_data.get('video_apis', {}).items():
                video_apis[name] = APIConfig(**api_data)
            
            return AppConfig(
                environment=environment,
                debug=config_data.get('debug', False),
                log_level=config_data.get('log_level', 'INFO'),
                data_dir=config_data.get('data_dir', 'data'),
                cache_dir=config_data.get('cache_dir', 'cache'),
                ui=ui_config,
                performance=performance_config,
                llm_apis=llm_apis,
                image_apis=image_apis,
                voice_apis=voice_apis,
                video_apis=video_apis
            )
            
        except Exception as e:
            logger.error(f"解析配置数据失败: {e}")
            raise ConfigError(f"配置格式错误: {e}")
    
    def _save_default_config(self):
        """保存默认配置"""
        try:
            config_file = self.config_dir / "config.json"
            example_config = {
                "environment": "development",
                "debug": True,
                "log_level": "INFO",
                "data_dir": "data",
                "cache_dir": "cache",
                "ui": {
                    "theme": "light",
                    "language": "zh_CN",
                    "window_width": 1400,
                    "window_height": 900,
                    "auto_save_interval": 300
                },
                "performance": {
                    "max_concurrent_tasks": 3,
                    "cache_size_mb": 512,
                    "enable_gpu": True,
                    "memory_limit_mb": 2048
                },
                "llm_apis": {
                    "zhipu": {
                        "provider": "zhipu",
                        "api_key": "your_zhipu_api_key_here",
                        "base_url": "https://open.bigmodel.cn/api/paas/v4/",
                        "timeout": 30,
                        "max_retries": 3
                    }
                },
                "image_apis": {
                    "cogview": {
                        "provider": "cogview",
                        "api_key": "your_cogview_api_key_here",
                        "timeout": 60,
                        "max_retries": 3
                    }
                },
                "voice_apis": {
                    "azure": {
                        "provider": "azure",
                        "api_key": "your_azure_api_key_here",
                        "timeout": 30,
                        "max_retries": 3
                    }
                },
                "video_apis": {
                    "cogvideo": {
                        "provider": "cogvideo",
                        "api_key": "your_cogvideo_api_key_here",
                        "timeout": 120,
                        "max_retries": 3
                    }
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(example_config, f, indent=2, ensure_ascii=False)
                
            logger.info(f"已创建默认配置文件: {config_file}")
            
        except Exception as e:
            logger.error(f"保存默认配置失败: {e}")
    
    @property
    def config(self) -> AppConfig:
        """获取配置"""
        return self._config
    
    def get_api_config(self, service_type: str, provider: str) -> Optional[APIConfig]:
        """获取API配置"""
        api_configs = getattr(self._config, f"{service_type}_apis", {})
        return api_configs.get(provider)
    
    def update_config(self, **kwargs):
        """更新配置"""
        try:
            for key, value in kwargs.items():
                if hasattr(self._config, key):
                    setattr(self._config, key, value)
            
            # 保存到文件
            self.save_config()
            
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            raise ConfigError(f"更新配置失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        if not self._config_file:
            return
            
        try:
            config_dict = asdict(self._config)
            # 转换枚举值
            config_dict['environment'] = self._config.environment.value
            
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
                
            logger.info("配置已保存")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise ConfigError(f"保存配置失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self._load_config()
        logger.info("配置已重新加载")
