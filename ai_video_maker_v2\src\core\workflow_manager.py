# -*- coding: utf-8 -*-
"""
工作流管理器 - 管理复杂的业务工作流

提供工作流编排功能，支持：
- 工作流定义和执行
- 步骤依赖管理
- 并行执行
- 错误处理和回滚
- 进度跟踪
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Set
import uuid

from .event_system import EventSystem, Event, EventType
from .service_manager import ServiceManager, ServiceType

logger = logging.getLogger(__name__)

class StepStatus(Enum):
    """步骤状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"

class WorkflowStatus(Enum):
    """工作流状态"""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"

@dataclass
class StepResult:
    """步骤执行结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0

@dataclass
class WorkflowStep:
    """工作流步骤"""
    step_id: str
    name: str
    service_type: ServiceType
    method: str
    params: Dict[str, Any] = field(default_factory=dict)
    depends_on: List[str] = field(default_factory=list)
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # 运行时状态
    status: StepStatus = StepStatus.PENDING
    result: Optional[StepResult] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.step_id:
            self.step_id = str(uuid.uuid4())

@dataclass
class Workflow:
    """工作流定义"""
    workflow_id: str
    name: str
    description: str = ""
    steps: List[WorkflowStep] = field(default_factory=list)
    
    # 运行时状态
    status: WorkflowStatus = WorkflowStatus.CREATED
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    current_step: Optional[str] = None
    
    # 结果数据
    results: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.workflow_id:
            self.workflow_id = str(uuid.uuid4())
    
    def get_step(self, step_id: str) -> Optional[WorkflowStep]:
        """获取步骤"""
        for step in self.steps:
            if step.step_id == step_id:
                return step
        return None
    
    def get_completed_steps(self) -> List[WorkflowStep]:
        """获取已完成的步骤"""
        return [step for step in self.steps if step.status == StepStatus.COMPLETED]
    
    def get_failed_steps(self) -> List[WorkflowStep]:
        """获取失败的步骤"""
        return [step for step in self.steps if step.status == StepStatus.FAILED]
    
    def get_progress(self) -> float:
        """获取进度百分比"""
        if not self.steps:
            return 0.0
        
        completed = len([s for s in self.steps if s.status == StepStatus.COMPLETED])
        return completed / len(self.steps)

ProgressCallback = Callable[[str, float, str], None]  # workflow_id, progress, message

class WorkflowManager:
    """工作流管理器"""
    
    def __init__(self, service_manager: ServiceManager, event_system: Optional[EventSystem] = None):
        self.service_manager = service_manager
        self.event_system = event_system
        
        # 活动工作流
        self._workflows: Dict[str, Workflow] = {}
        self._running_tasks: Dict[str, asyncio.Task] = {}
        
        # 工作流模板
        self._templates: Dict[str, Workflow] = {}
        
        self._shutdown = False
    
    def register_template(self, template: Workflow):
        """注册工作流模板"""
        self._templates[template.name] = template
        logger.info(f"工作流模板已注册: {template.name}")
    
    def create_workflow_from_template(self, template_name: str, **params) -> Optional[Workflow]:
        """从模板创建工作流"""
        if template_name not in self._templates:
            logger.error(f"工作流模板不存在: {template_name}")
            return None
        
        template = self._templates[template_name]
        
        # 创建新的工作流实例
        workflow = Workflow(
            workflow_id=str(uuid.uuid4()),
            name=f"{template.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            description=template.description,
            steps=[self._clone_step(step, params) for step in template.steps]
        )
        
        return workflow
    
    def _clone_step(self, step: WorkflowStep, params: Dict[str, Any]) -> WorkflowStep:
        """克隆步骤并应用参数"""
        new_params = step.params.copy()
        new_params.update(params)
        
        return WorkflowStep(
            step_id=str(uuid.uuid4()),
            name=step.name,
            service_type=step.service_type,
            method=step.method,
            params=new_params,
            depends_on=step.depends_on.copy(),
            timeout=step.timeout,
            max_retries=step.max_retries
        )
    
    async def execute_workflow(self, 
                              workflow: Workflow,
                              progress_callback: Optional[ProgressCallback] = None) -> bool:
        """执行工作流"""
        try:
            workflow.status = WorkflowStatus.RUNNING
            workflow.start_time = datetime.now()
            
            self._workflows[workflow.workflow_id] = workflow
            
            # 发布工作流开始事件
            if self.event_system:
                event = Event(
                    event_type=EventType.WORKFLOW_STARTED,
                    data={
                        'workflow_id': workflow.workflow_id,
                        'workflow_name': workflow.name,
                        'total_steps': len(workflow.steps)
                    },
                    source='workflow_manager'
                )
                await self.event_system.emit(event)
            
            # 创建执行任务
            task = asyncio.create_task(
                self._execute_workflow_steps(workflow, progress_callback)
            )
            self._running_tasks[workflow.workflow_id] = task
            
            # 等待执行完成
            success = await task
            
            # 更新状态
            workflow.status = WorkflowStatus.COMPLETED if success else WorkflowStatus.FAILED
            workflow.end_time = datetime.now()
            
            # 清理任务
            if workflow.workflow_id in self._running_tasks:
                del self._running_tasks[workflow.workflow_id]
            
            # 发布工作流完成事件
            if self.event_system:
                event_type = EventType.WORKFLOW_COMPLETED if success else EventType.WORKFLOW_FAILED
                event = Event(
                    event_type=event_type,
                    data={
                        'workflow_id': workflow.workflow_id,
                        'workflow_name': workflow.name,
                        'success': success,
                        'execution_time': (workflow.end_time - workflow.start_time).total_seconds(),
                        'completed_steps': len(workflow.get_completed_steps()),
                        'failed_steps': len(workflow.get_failed_steps())
                    },
                    source='workflow_manager'
                )
                await self.event_system.emit(event)
            
            return success
            
        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            workflow.status = WorkflowStatus.FAILED
            workflow.end_time = datetime.now()
            workflow.errors.append(str(e))
            return False
    
    async def _execute_workflow_steps(self, 
                                     workflow: Workflow,
                                     progress_callback: Optional[ProgressCallback] = None) -> bool:
        """执行工作流步骤"""
        try:
            # 构建依赖图
            dependency_graph = self._build_dependency_graph(workflow.steps)
            
            # 执行步骤
            completed_steps: Set[str] = set()
            
            while len(completed_steps) < len(workflow.steps):
                # 找到可以执行的步骤
                ready_steps = self._get_ready_steps(workflow.steps, completed_steps, dependency_graph)
                
                if not ready_steps:
                    # 检查是否有失败的步骤导致死锁
                    failed_steps = [s for s in workflow.steps if s.status == StepStatus.FAILED]
                    if failed_steps:
                        logger.error(f"工作流因步骤失败而停止: {[s.step_id for s in failed_steps]}")
                        return False
                    else:
                        logger.error("工作流出现循环依赖或其他问题")
                        return False
                
                # 并行执行准备好的步骤
                tasks = []
                for step in ready_steps:
                    task = asyncio.create_task(self._execute_step(workflow, step))
                    tasks.append((step, task))
                
                # 等待所有步骤完成
                for step, task in tasks:
                    try:
                        success = await task
                        if success:
                            completed_steps.add(step.step_id)
                        else:
                            # 步骤失败，检查是否可以继续
                            if not self._can_continue_after_failure(workflow, step):
                                return False
                    except Exception as e:
                        logger.error(f"步骤执行异常: {step.step_id}, {e}")
                        step.status = StepStatus.FAILED
                        step.result = StepResult(success=False, error=str(e))
                        
                        if not self._can_continue_after_failure(workflow, step):
                            return False
                
                # 更新进度
                if progress_callback:
                    progress = workflow.get_progress()
                    message = f"已完成 {len(completed_steps)}/{len(workflow.steps)} 个步骤"
                    progress_callback(workflow.workflow_id, progress, message)
            
            return True
            
        except Exception as e:
            logger.error(f"工作流步骤执行失败: {e}")
            return False
    
    async def _execute_step(self, workflow: Workflow, step: WorkflowStep) -> bool:
        """执行单个步骤"""
        try:
            step.status = StepStatus.RUNNING
            step.start_time = datetime.now()
            workflow.current_step = step.step_id
            
            logger.info(f"开始执行步骤: {step.name} ({step.step_id})")
            
            # 发布步骤开始事件
            if self.event_system:
                event = Event(
                    event_type=EventType.TASK_STARTED,
                    data={
                        'workflow_id': workflow.workflow_id,
                        'step_id': step.step_id,
                        'step_name': step.name,
                        'service_type': step.service_type.value,
                        'method': step.method
                    },
                    source='workflow_manager'
                )
                await self.event_system.emit(event)
            
            # 准备参数（可能包含前面步骤的结果）
            params = self._prepare_step_params(workflow, step)
            
            # 执行服务调用
            start_time = asyncio.get_event_loop().time()
            
            if step.timeout:
                service_result = await asyncio.wait_for(
                    self.service_manager.call_service(
                        step.service_type,
                        step.method,
                        **params
                    ),
                    timeout=step.timeout
                )
            else:
                service_result = await self.service_manager.call_service(
                    step.service_type,
                    step.method,
                    **params
                )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            # 创建步骤结果
            step.result = StepResult(
                success=service_result.success,
                data=service_result.data,
                error=service_result.error,
                metadata=service_result.metadata,
                execution_time=execution_time
            )
            
            if service_result.success:
                step.status = StepStatus.COMPLETED
                # 保存结果到工作流
                workflow.results[step.step_id] = service_result.data
                logger.info(f"步骤执行成功: {step.name}")
            else:
                step.status = StepStatus.FAILED
                workflow.errors.append(f"步骤 {step.name} 失败: {service_result.error}")
                logger.error(f"步骤执行失败: {step.name}, 错误: {service_result.error}")
            
            step.end_time = datetime.now()
            
            # 发布步骤完成事件
            if self.event_system:
                event_type = EventType.TASK_COMPLETED if service_result.success else EventType.TASK_FAILED
                event = Event(
                    event_type=event_type,
                    data={
                        'workflow_id': workflow.workflow_id,
                        'step_id': step.step_id,
                        'step_name': step.name,
                        'success': service_result.success,
                        'execution_time': execution_time,
                        'error': service_result.error
                    },
                    source='workflow_manager'
                )
                await self.event_system.emit(event)
            
            return service_result.success
            
        except asyncio.TimeoutError:
            step.status = StepStatus.FAILED
            step.result = StepResult(success=False, error="步骤执行超时")
            step.end_time = datetime.now()
            logger.error(f"步骤执行超时: {step.name}")
            return False
            
        except Exception as e:
            step.status = StepStatus.FAILED
            step.result = StepResult(success=False, error=str(e))
            step.end_time = datetime.now()
            logger.error(f"步骤执行异常: {step.name}, {e}")
            return False
    
    def _build_dependency_graph(self, steps: List[WorkflowStep]) -> Dict[str, Set[str]]:
        """构建依赖图"""
        graph = {}
        for step in steps:
            graph[step.step_id] = set(step.depends_on)
        return graph
    
    def _get_ready_steps(self, 
                        steps: List[WorkflowStep],
                        completed_steps: Set[str],
                        dependency_graph: Dict[str, Set[str]]) -> List[WorkflowStep]:
        """获取准备执行的步骤"""
        ready_steps = []
        
        for step in steps:
            if (step.status == StepStatus.PENDING and 
                step.step_id not in completed_steps and
                dependency_graph[step.step_id].issubset(completed_steps)):
                ready_steps.append(step)
        
        return ready_steps
    
    def _prepare_step_params(self, workflow: Workflow, step: WorkflowStep) -> Dict[str, Any]:
        """准备步骤参数"""
        params = step.params.copy()
        
        # 替换参数中的引用
        for key, value in params.items():
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                # 参数引用格式: ${step_id.field}
                ref = value[2:-1]
                if "." in ref:
                    step_id, field = ref.split(".", 1)
                    if step_id in workflow.results:
                        result_data = workflow.results[step_id]
                        if isinstance(result_data, dict) and field in result_data:
                            params[key] = result_data[field]
                        else:
                            params[key] = result_data
                else:
                    # 直接引用整个步骤结果
                    if ref in workflow.results:
                        params[key] = workflow.results[ref]
        
        return params
    
    def _can_continue_after_failure(self, workflow: Workflow, failed_step: WorkflowStep) -> bool:
        """检查失败后是否可以继续"""
        # 简单策略：如果有其他步骤依赖失败的步骤，则不能继续
        for step in workflow.steps:
            if failed_step.step_id in step.depends_on:
                return False
        return True
    
    async def cancel_workflow(self, workflow_id: str) -> bool:
        """取消工作流"""
        if workflow_id in self._running_tasks:
            task = self._running_tasks[workflow_id]
            task.cancel()
            
            if workflow_id in self._workflows:
                workflow = self._workflows[workflow_id]
                workflow.status = WorkflowStatus.CANCELLED
                workflow.end_time = datetime.now()
            
            logger.info(f"工作流已取消: {workflow_id}")
            return True
        
        return False
    
    def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self._workflows.get(workflow_id)
    
    def get_running_workflows(self) -> List[Workflow]:
        """获取运行中的工作流"""
        return [w for w in self._workflows.values() if w.status == WorkflowStatus.RUNNING]
    
    async def shutdown(self):
        """关闭工作流管理器"""
        self._shutdown = True
        
        # 取消所有运行中的任务
        for workflow_id in list(self._running_tasks.keys()):
            await self.cancel_workflow(workflow_id)
        
        logger.info("工作流管理器已关闭")
