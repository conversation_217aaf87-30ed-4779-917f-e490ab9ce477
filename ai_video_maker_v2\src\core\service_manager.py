# -*- coding: utf-8 -*-
"""
服务管理器 - 统一管理所有AI服务

提供统一的服务接口，支持：
- 服务注册/发现
- 服务生命周期管理
- 服务健康检查
- 负载均衡
- 错误处理和重试
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Union
import uuid

from .config_manager import ConfigManager, APIConfig
from .event_system import EventSystem, Event, EventType

logger = logging.getLogger(__name__)

class ServiceType(Enum):
    """服务类型"""
    LLM = "llm"
    IMAGE = "image"
    VOICE = "voice"
    VIDEO = "video"
    TRANSLATION = "translation"

class ServiceStatus(Enum):
    """服务状态"""
    UNKNOWN = "unknown"
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    OFFLINE = "offline"

@dataclass
class ServiceResult:
    """服务调用结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    execution_time: float = 0.0
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class ServiceHealth:
    """服务健康状态"""
    status: ServiceStatus
    last_check: datetime
    response_time: float
    error_count: int = 0
    success_count: int = 0
    
    @property
    def success_rate(self) -> float:
        total = self.success_count + self.error_count
        return self.success_count / total if total > 0 else 0.0

class BaseService(ABC):
    """服务基类"""
    
    def __init__(self, service_type: ServiceType, provider: str, config: APIConfig):
        self.service_type = service_type
        self.provider = provider
        self.config = config
        self.service_id = f"{service_type.value}_{provider}_{uuid.uuid4().hex[:8]}"
        
        # 健康状态
        self.health = ServiceHealth(
            status=ServiceStatus.UNKNOWN,
            last_check=datetime.now(),
            response_time=0.0
        )
        
        # 统计信息
        self.total_requests = 0
        self.failed_requests = 0
        
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def health_check(self) -> ServiceHealth:
        """健康检查"""
        pass
    
    @abstractmethod
    async def call_service(self, method: str, **kwargs) -> ServiceResult:
        """调用服务方法"""
        pass
    
    async def shutdown(self):
        """关闭服务"""
        logger.info(f"服务 {self.service_id} 正在关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            'service_id': self.service_id,
            'service_type': self.service_type.value,
            'provider': self.provider,
            'total_requests': self.total_requests,
            'failed_requests': self.failed_requests,
            'success_rate': (self.total_requests - self.failed_requests) / max(self.total_requests, 1),
            'health': {
                'status': self.health.status.value,
                'last_check': self.health.last_check.isoformat(),
                'response_time': self.health.response_time,
                'success_rate': self.health.success_rate
            }
        }

class ServiceRegistry:
    """服务注册表"""
    
    def __init__(self):
        self._services: Dict[str, BaseService] = {}
        self._services_by_type: Dict[ServiceType, List[BaseService]] = {}
        
    def register_service(self, service: BaseService):
        """注册服务"""
        self._services[service.service_id] = service
        
        if service.service_type not in self._services_by_type:
            self._services_by_type[service.service_type] = []
        
        self._services_by_type[service.service_type].append(service)
        logger.info(f"服务已注册: {service.service_id}")
    
    def unregister_service(self, service_id: str):
        """注销服务"""
        if service_id in self._services:
            service = self._services.pop(service_id)
            
            if service.service_type in self._services_by_type:
                self._services_by_type[service.service_type] = [
                    s for s in self._services_by_type[service.service_type] 
                    if s.service_id != service_id
                ]
            
            logger.info(f"服务已注销: {service_id}")
    
    def get_service(self, service_id: str) -> Optional[BaseService]:
        """获取服务"""
        return self._services.get(service_id)
    
    def get_services_by_type(self, service_type: ServiceType) -> List[BaseService]:
        """按类型获取服务"""
        return self._services_by_type.get(service_type, [])
    
    def get_healthy_services(self, service_type: ServiceType) -> List[BaseService]:
        """获取健康的服务"""
        services = self.get_services_by_type(service_type)
        return [s for s in services if s.health.status == ServiceStatus.HEALTHY]
    
    def get_all_services(self) -> List[BaseService]:
        """获取所有服务"""
        return list(self._services.values())

class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, strategy: str = "round_robin"):
        self.strategy = strategy
        self._counters: Dict[ServiceType, int] = {}
    
    def select_service(self, services: List[BaseService]) -> Optional[BaseService]:
        """选择服务"""
        if not services:
            return None
        
        if len(services) == 1:
            return services[0]
        
        if self.strategy == "round_robin":
            return self._round_robin_select(services)
        elif self.strategy == "least_loaded":
            return self._least_loaded_select(services)
        elif self.strategy == "best_health":
            return self._best_health_select(services)
        else:
            return services[0]
    
    def _round_robin_select(self, services: List[BaseService]) -> BaseService:
        """轮询选择"""
        service_type = services[0].service_type
        
        if service_type not in self._counters:
            self._counters[service_type] = 0
        
        index = self._counters[service_type] % len(services)
        self._counters[service_type] += 1
        
        return services[index]
    
    def _least_loaded_select(self, services: List[BaseService]) -> BaseService:
        """最少负载选择"""
        return min(services, key=lambda s: s.total_requests)
    
    def _best_health_select(self, services: List[BaseService]) -> BaseService:
        """最佳健康状态选择"""
        return max(services, key=lambda s: s.health.success_rate)

class ServiceManager:
    """服务管理器"""
    
    def __init__(self, config_manager: ConfigManager, event_system: Optional[EventSystem] = None):
        self.config_manager = config_manager
        self.event_system = event_system
        
        # 服务注册表和负载均衡器
        self.registry = ServiceRegistry()
        self.load_balancer = LoadBalancer()
        
        # 健康检查
        self._health_check_interval = 60  # 秒
        self._health_check_task: Optional[asyncio.Task] = None
        
        # 重试配置
        self._max_retries = 3
        self._retry_delay = 1.0
        
        self._shutdown = False
    
    async def start(self):
        """启动服务管理器"""
        # 初始化服务
        await self._initialize_services()
        
        # 启动健康检查
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info("服务管理器已启动")
    
    async def shutdown(self):
        """关闭服务管理器"""
        self._shutdown = True
        
        # 停止健康检查
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有服务
        for service in self.registry.get_all_services():
            await service.shutdown()
        
        logger.info("服务管理器已关闭")
    
    async def call_service(self, 
                          service_type: ServiceType,
                          method: str,
                          provider: Optional[str] = None,
                          **kwargs) -> ServiceResult:
        """调用服务"""
        try:
            # 选择服务
            service = await self._select_service(service_type, provider)
            if not service:
                return ServiceResult(
                    success=False,
                    error=f"没有可用的 {service_type.value} 服务"
                )
            
            # 执行调用（带重试）
            result = await self._call_with_retry(service, method, **kwargs)
            
            # 更新统计
            service.total_requests += 1
            if not result.success:
                service.failed_requests += 1
            
            return result
            
        except Exception as e:
            logger.error(f"服务调用失败: {e}")
            return ServiceResult(
                success=False,
                error=str(e)
            )
    
    async def _select_service(self, service_type: ServiceType, provider: Optional[str] = None) -> Optional[BaseService]:
        """选择服务"""
        if provider:
            # 查找指定提供商的服务
            services = [s for s in self.registry.get_services_by_type(service_type) 
                       if s.provider == provider]
        else:
            # 获取健康的服务
            services = self.registry.get_healthy_services(service_type)
        
        return self.load_balancer.select_service(services)
    
    async def _call_with_retry(self, service: BaseService, method: str, **kwargs) -> ServiceResult:
        """带重试的服务调用"""
        last_error = None
        
        for attempt in range(self._max_retries + 1):
            try:
                start_time = asyncio.get_event_loop().time()
                result = await service.call_service(method, **kwargs)
                execution_time = asyncio.get_event_loop().time() - start_time
                
                result.execution_time = execution_time
                
                if result.success:
                    return result
                
                last_error = result.error
                
            except Exception as e:
                last_error = str(e)
                logger.warning(f"服务调用失败 (尝试 {attempt + 1}/{self._max_retries + 1}): {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self._max_retries:
                await asyncio.sleep(self._retry_delay * (2 ** attempt))  # 指数退避
        
        return ServiceResult(
            success=False,
            error=f"服务调用失败，已重试 {self._max_retries} 次: {last_error}"
        )
    
    async def _initialize_services(self):
        """初始化服务"""
        config = self.config_manager.config
        
        # 初始化LLM服务
        for provider, api_config in config.llm_apis.items():
            try:
                service = await self._create_llm_service(provider, api_config)
                if service and await service.initialize():
                    self.registry.register_service(service)
            except Exception as e:
                logger.error(f"初始化LLM服务失败 ({provider}): {e}")
        
        # 初始化图像服务
        for provider, api_config in config.image_apis.items():
            try:
                service = await self._create_image_service(provider, api_config)
                if service and await service.initialize():
                    self.registry.register_service(service)
            except Exception as e:
                logger.error(f"初始化图像服务失败 ({provider}): {e}")
        
        # 初始化语音服务
        for provider, api_config in config.voice_apis.items():
            try:
                service = await self._create_voice_service(provider, api_config)
                if service and await service.initialize():
                    self.registry.register_service(service)
            except Exception as e:
                logger.error(f"初始化语音服务失败 ({provider}): {e}")
        
        # 初始化视频服务
        for provider, api_config in config.video_apis.items():
            try:
                service = await self._create_video_service(provider, api_config)
                if service and await service.initialize():
                    self.registry.register_service(service)
            except Exception as e:
                logger.error(f"初始化视频服务失败 ({provider}): {e}")
    
    async def _create_llm_service(self, provider: str, config: APIConfig) -> Optional[BaseService]:
        """创建LLM服务"""
        # 这里会导入具体的服务实现
        # 为了避免循环导入，使用动态导入
        try:
            if provider == "zhipu":
                from ..services.llm.zhipu_service import ZhipuLLMService
                return ZhipuLLMService(provider, config)
            elif provider == "openai":
                from ..services.llm.openai_service import OpenAILLMService
                return OpenAILLMService(provider, config)
            # 添加其他LLM提供商...
        except ImportError as e:
            logger.error(f"导入LLM服务失败 ({provider}): {e}")
        
        return None
    
    async def _create_image_service(self, provider: str, config: APIConfig) -> Optional[BaseService]:
        """创建图像服务"""
        try:
            if provider == "cogview":
                from ..services.image.cogview_service import CogViewImageService
                return CogViewImageService(provider, config)
            # 添加其他图像提供商...
        except ImportError as e:
            logger.error(f"导入图像服务失败 ({provider}): {e}")
        
        return None
    
    async def _create_voice_service(self, provider: str, config: APIConfig) -> Optional[BaseService]:
        """创建语音服务"""
        try:
            if provider == "azure":
                from ..services.voice.azure_service import AzureVoiceService
                return AzureVoiceService(provider, config)
            # 添加其他语音提供商...
        except ImportError as e:
            logger.error(f"导入语音服务失败 ({provider}): {e}")
        
        return None
    
    async def _create_video_service(self, provider: str, config: APIConfig) -> Optional[BaseService]:
        """创建视频服务"""
        try:
            if provider == "cogvideo":
                from ..services.video.cogvideo_service import CogVideoService
                return CogVideoService(provider, config)
            # 添加其他视频提供商...
        except ImportError as e:
            logger.error(f"导入视频服务失败 ({provider}): {e}")
        
        return None
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                await asyncio.sleep(10)  # 错误时短暂等待
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        services = self.registry.get_all_services()
        
        for service in services:
            try:
                old_status = service.health.status
                service.health = await service.health_check()
                
                # 如果状态发生变化，发布事件
                if old_status != service.health.status and self.event_system:
                    event_type = (EventType.SERVICE_CONNECTED 
                                if service.health.status == ServiceStatus.HEALTHY 
                                else EventType.SERVICE_DISCONNECTED)
                    
                    event = Event(
                        event_type=event_type,
                        data={
                            'service_id': service.service_id,
                            'service_type': service.service_type.value,
                            'provider': service.provider,
                            'old_status': old_status.value,
                            'new_status': service.health.status.value
                        },
                        source='service_manager'
                    )
                    self.event_system.emit_sync(event)
                    
            except Exception as e:
                logger.error(f"服务健康检查失败 ({service.service_id}): {e}")
                service.health.status = ServiceStatus.UNHEALTHY
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        services = self.registry.get_all_services()
        
        stats_by_type = {}
        for service_type in ServiceType:
            type_services = self.registry.get_services_by_type(service_type)
            healthy_count = len(self.registry.get_healthy_services(service_type))
            
            stats_by_type[service_type.value] = {
                'total': len(type_services),
                'healthy': healthy_count,
                'services': [s.get_stats() for s in type_services]
            }
        
        return {
            'total_services': len(services),
            'healthy_services': len([s for s in services if s.health.status == ServiceStatus.HEALTHY]),
            'services_by_type': stats_by_type
        }
