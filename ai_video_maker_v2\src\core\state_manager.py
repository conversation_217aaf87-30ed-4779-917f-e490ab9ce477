# -*- coding: utf-8 -*-
"""
状态管理器 - 应用程序状态管理

提供集中式状态管理，支持：
- 状态订阅/通知
- 状态持久化
- 状态回滚
- 状态验证
"""

import json
import logging
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, TypeVar, Generic
from copy import deepcopy
import threading

from .event_system import EventSystem, Event, EventType

logger = logging.getLogger(__name__)

T = TypeVar('T')

class StateChangeType(Enum):
    """状态变更类型"""
    SET = "set"
    UPDATE = "update"
    DELETE = "delete"
    RESET = "reset"

@dataclass
class StateChange:
    """状态变更记录"""
    path: str
    change_type: StateChangeType
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=datetime.now)
    change_id: str = field(default_factory=lambda: str(datetime.now().timestamp()))

@dataclass
class AppState:
    """应用程序状态"""
    # 当前项目信息
    current_project: Optional[Dict[str, Any]] = None
    project_path: Optional[str] = None
    
    # UI状态
    ui_state: Dict[str, Any] = field(default_factory=dict)
    
    # 工作流状态
    workflow_state: Dict[str, Any] = field(default_factory=dict)
    
    # 任务状态
    task_states: Dict[str, Any] = field(default_factory=dict)
    
    # 服务状态
    service_states: Dict[str, Any] = field(default_factory=dict)
    
    # 用户偏好
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    # 临时数据
    temp_data: Dict[str, Any] = field(default_factory=dict)

StateListener = Callable[[str, Any, Any], None]  # path, old_value, new_value

class StateManager:
    """状态管理器"""
    
    def __init__(self, event_system: Optional[EventSystem] = None, 
                 state_file: Optional[str] = None):
        self._state = AppState()
        self._event_system = event_system
        self._state_file = Path(state_file) if state_file else None
        
        # 状态监听器
        self._listeners: Dict[str, List[StateListener]] = {}
        self._global_listeners: List[StateListener] = []
        
        # 状态历史
        self._history: List[StateChange] = []
        self._max_history = 100
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 加载持久化状态
        self._load_state()
    
    def get_state(self, path: str = None) -> Any:
        """获取状态值"""
        with self._lock:
            if path is None:
                return deepcopy(asdict(self._state))
            
            return self._get_nested_value(asdict(self._state), path)
    
    def set_state(self, path: str, value: Any, notify: bool = True):
        """设置状态值"""
        with self._lock:
            old_value = self._get_nested_value(asdict(self._state), path)
            
            # 设置新值
            self._set_nested_value(self._state, path, value)
            
            # 记录变更
            change = StateChange(
                path=path,
                change_type=StateChangeType.SET,
                old_value=old_value,
                new_value=value
            )
            self._add_to_history(change)
            
            # 通知监听器
            if notify:
                self._notify_listeners(path, old_value, value)
            
            # 发布事件
            if self._event_system:
                event = Event(
                    event_type=EventType.UI_THEME_CHANGED if 'ui' in path else EventType.APP_STARTED,
                    data={
                        'path': path,
                        'old_value': old_value,
                        'new_value': value,
                        'change_type': change.change_type.value
                    },
                    source='state_manager'
                )
                self._event_system.emit_sync(event)
    
    def update_state(self, path: str, updates: Dict[str, Any], notify: bool = True):
        """更新状态（合并更新）"""
        with self._lock:
            current_value = self._get_nested_value(asdict(self._state), path)
            
            if not isinstance(current_value, dict):
                raise ValueError(f"路径 {path} 的值不是字典类型，无法进行合并更新")
            
            old_value = deepcopy(current_value)
            new_value = {**current_value, **updates}
            
            # 设置新值
            self._set_nested_value(self._state, path, new_value)
            
            # 记录变更
            change = StateChange(
                path=path,
                change_type=StateChangeType.UPDATE,
                old_value=old_value,
                new_value=new_value
            )
            self._add_to_history(change)
            
            # 通知监听器
            if notify:
                self._notify_listeners(path, old_value, new_value)
    
    def delete_state(self, path: str, notify: bool = True):
        """删除状态值"""
        with self._lock:
            old_value = self._get_nested_value(asdict(self._state), path)
            
            # 删除值
            self._delete_nested_value(self._state, path)
            
            # 记录变更
            change = StateChange(
                path=path,
                change_type=StateChangeType.DELETE,
                old_value=old_value,
                new_value=None
            )
            self._add_to_history(change)
            
            # 通知监听器
            if notify:
                self._notify_listeners(path, old_value, None)
    
    def reset_state(self, notify: bool = True):
        """重置状态"""
        with self._lock:
            old_state = deepcopy(asdict(self._state))
            self._state = AppState()
            
            # 记录变更
            change = StateChange(
                path="",
                change_type=StateChangeType.RESET,
                old_value=old_state,
                new_value=asdict(self._state)
            )
            self._add_to_history(change)
            
            # 通知监听器
            if notify:
                self._notify_listeners("", old_state, asdict(self._state))
    
    def subscribe(self, path: str, listener: StateListener) -> str:
        """订阅状态变更"""
        with self._lock:
            if path not in self._listeners:
                self._listeners[path] = []
            
            self._listeners[path].append(listener)
            listener_id = f"{path}_{len(self._listeners[path])}"
            
            logger.debug(f"订阅状态变更: {path}")
            return listener_id
    
    def subscribe_all(self, listener: StateListener) -> str:
        """订阅所有状态变更"""
        with self._lock:
            self._global_listeners.append(listener)
            listener_id = f"global_{len(self._global_listeners)}"
            
            logger.debug("订阅所有状态变更")
            return listener_id
    
    def unsubscribe(self, path: str, listener: StateListener) -> bool:
        """取消订阅"""
        with self._lock:
            if path in self._listeners and listener in self._listeners[path]:
                self._listeners[path].remove(listener)
                logger.debug(f"取消订阅状态变更: {path}")
                return True
            
            if listener in self._global_listeners:
                self._global_listeners.remove(listener)
                logger.debug("取消订阅所有状态变更")
                return True
            
            return False
    
    def get_history(self, path: Optional[str] = None, limit: Optional[int] = None) -> List[StateChange]:
        """获取状态变更历史"""
        with self._lock:
            history = self._history
            
            if path:
                history = [change for change in history if change.path.startswith(path)]
            
            if limit:
                history = history[-limit:]
            
            return deepcopy(history)
    
    def save_state(self):
        """保存状态到文件"""
        if not self._state_file:
            return
        
        try:
            with self._lock:
                state_data = asdict(self._state)
                
                # 确保目录存在
                self._state_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(self._state_file, 'w', encoding='utf-8') as f:
                    json.dump(state_data, f, indent=2, ensure_ascii=False, default=str)
                
                logger.debug(f"状态已保存到: {self._state_file}")
                
        except Exception as e:
            logger.error(f"保存状态失败: {e}")
    
    def _load_state(self):
        """从文件加载状态"""
        if not self._state_file or not self._state_file.exists():
            return
        
        try:
            with open(self._state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 重建状态对象
            self._state = AppState(**state_data)
            logger.debug(f"状态已从文件加载: {self._state_file}")
            
        except Exception as e:
            logger.error(f"加载状态失败: {e}")
            self._state = AppState()
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """获取嵌套值"""
        if not path:
            return data
        
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def _set_nested_value(self, obj: Any, path: str, value: Any):
        """设置嵌套值"""
        if not path:
            return
        
        keys = path.split('.')
        current = obj
        
        # 导航到父级
        for key in keys[:-1]:
            if not hasattr(current, key):
                setattr(current, key, {})
            current = getattr(current, key)
        
        # 设置最终值
        setattr(current, keys[-1], value)
    
    def _delete_nested_value(self, obj: Any, path: str):
        """删除嵌套值"""
        if not path:
            return
        
        keys = path.split('.')
        current = obj
        
        # 导航到父级
        for key in keys[:-1]:
            if not hasattr(current, key):
                return
            current = getattr(current, key)
        
        # 删除最终值
        if hasattr(current, keys[-1]):
            delattr(current, keys[-1])
    
    def _notify_listeners(self, path: str, old_value: Any, new_value: Any):
        """通知监听器"""
        try:
            # 通知特定路径的监听器
            if path in self._listeners:
                for listener in self._listeners[path]:
                    try:
                        listener(path, old_value, new_value)
                    except Exception as e:
                        logger.error(f"状态监听器执行失败: {e}")
            
            # 通知全局监听器
            for listener in self._global_listeners:
                try:
                    listener(path, old_value, new_value)
                except Exception as e:
                    logger.error(f"全局状态监听器执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"通知状态监听器失败: {e}")
    
    def _add_to_history(self, change: StateChange):
        """添加变更到历史记录"""
        self._history.append(change)
        
        # 限制历史记录大小
        if len(self._history) > self._max_history:
            self._history = self._history[-self._max_history:]
