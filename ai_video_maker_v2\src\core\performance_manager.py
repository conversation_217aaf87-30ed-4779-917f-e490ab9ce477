# -*- coding: utf-8 -*-
"""
性能管理器 - 系统性能优化和监控

提供全面的性能管理功能：
- 内存管理和监控
- 缓存策略和管理
- 并发控制和优化
- 性能指标收集
"""

import asyncio
import gc
import logging
import psutil
import threading
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
import json
import weakref
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    cache_hit_rate: float
    cache_size_mb: float

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    size_bytes: int = 0
    ttl_seconds: Optional[int] = None

class MemoryManager:
    """内存管理器"""
    
    def __init__(self, max_memory_mb: int = 2048):
        self.max_memory_mb = max_memory_mb
        self.weak_refs: Dict[str, weakref.ref] = {}
        self.memory_threshold = 0.8  # 80%内存使用率阈值
        
    def register_object(self, obj_id: str, obj: Any):
        """注册对象用于内存管理"""
        self.weak_refs[obj_id] = weakref.ref(obj, self._cleanup_callback(obj_id))
    
    def _cleanup_callback(self, obj_id: str):
        """清理回调"""
        def callback(ref):
            if obj_id in self.weak_refs:
                del self.weak_refs[obj_id]
                logger.debug(f"对象 {obj_id} 已从内存管理器中移除")
        return callback
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            "used_mb": memory_info.rss / 1024 / 1024,
            "percent": process.memory_percent(),
            "available_mb": psutil.virtual_memory().available / 1024 / 1024
        }
    
    def check_memory_pressure(self) -> bool:
        """检查内存压力"""
        usage = self.get_memory_usage()
        return usage["percent"] > self.memory_threshold * 100
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        collected = gc.collect()
        logger.info(f"垃圾回收完成，回收了 {collected} 个对象")
        return collected
    
    def optimize_memory(self):
        """优化内存使用"""
        if self.check_memory_pressure():
            logger.warning("检测到内存压力，开始优化...")
            
            # 强制垃圾回收
            self.force_garbage_collection()
            
            # 清理弱引用
            dead_refs = [obj_id for obj_id, ref in self.weak_refs.items() if ref() is None]
            for obj_id in dead_refs:
                del self.weak_refs[obj_id]
            
            logger.info(f"内存优化完成，清理了 {len(dead_refs)} 个死引用")

class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size_mb: int = 512, default_ttl: int = 3600):
        self.max_size_mb = max_size_mb
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order = deque()  # LRU队列
        self.hit_count = 0
        self.miss_count = 0
        self.lock = threading.RLock()
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                self.miss_count += 1
                return None
            
            entry = self.cache[key]
            
            # 检查TTL
            if entry.ttl_seconds and (datetime.now() - entry.created_at).seconds > entry.ttl_seconds:
                del self.cache[key]
                self.miss_count += 1
                return None
            
            # 更新访问信息
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            # 更新LRU队列
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
            
            self.hit_count += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self.lock:
            # 计算值的大小（简单估算）
            size_bytes = self._estimate_size(value)
            
            # 检查是否需要清理空间
            if self._get_total_size_mb() + size_bytes / 1024 / 1024 > self.max_size_mb:
                self._evict_entries()
            
            # 创建缓存条目
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                size_bytes=size_bytes,
                ttl_seconds=ttl or self.default_ttl
            )
            
            self.cache[key] = entry
            
            # 更新LRU队列
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
            
            return True
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                if key in self.access_order:
                    self.access_order.remove(key)
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
            self.hit_count = 0
            self.miss_count = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            total_requests = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
            
            return {
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": hit_rate,
                "total_entries": len(self.cache),
                "total_size_mb": self._get_total_size_mb()
            }
    
    def _estimate_size(self, obj: Any) -> int:
        """估算对象大小"""
        try:
            if isinstance(obj, str):
                return len(obj.encode('utf-8'))
            elif isinstance(obj, (int, float)):
                return 8
            elif isinstance(obj, (list, tuple)):
                return sum(self._estimate_size(item) for item in obj)
            elif isinstance(obj, dict):
                return sum(self._estimate_size(k) + self._estimate_size(v) for k, v in obj.items())
            else:
                # 简单估算
                return 1024
        except:
            return 1024
    
    def _get_total_size_mb(self) -> float:
        """获取总缓存大小（MB）"""
        total_bytes = sum(entry.size_bytes for entry in self.cache.values())
        return total_bytes / 1024 / 1024
    
    def _evict_entries(self):
        """驱逐缓存条目（LRU策略）"""
        target_size = self.max_size_mb * 0.8  # 清理到80%
        
        while self._get_total_size_mb() > target_size and self.access_order:
            # 移除最少使用的条目
            oldest_key = self.access_order.popleft()
            if oldest_key in self.cache:
                del self.cache[oldest_key]

class ConcurrencyManager:
    """并发管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_stats = defaultdict(int)
        
    async def execute_with_limit(self, task_id: str, coro):
        """在并发限制下执行任务"""
        async with self.semaphore:
            try:
                self.active_tasks[task_id] = asyncio.current_task()
                self.task_stats["started"] += 1
                
                result = await coro
                
                self.task_stats["completed"] += 1
                return result
                
            except Exception as e:
                self.task_stats["failed"] += 1
                raise
            finally:
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.cancel()
            del self.active_tasks[task_id]
            self.task_stats["cancelled"] += 1
            return True
        return False
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量"""
        return len(self.active_tasks)
    
    def get_stats(self) -> Dict[str, int]:
        """获取并发统计"""
        return dict(self.task_stats)

class PerformanceManager:
    """性能管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.memory_manager = MemoryManager(config.get("memory_limit_mb", 2048))
        self.cache_manager = CacheManager(config.get("cache_size_mb", 512))
        self.concurrency_manager = ConcurrencyManager(config.get("max_concurrent_tasks", 3))
        
        self.metrics_history: List[PerformanceMetrics] = []
        self.monitoring_enabled = False
        self.monitoring_task: Optional[asyncio.Task] = None
        
    async def start_monitoring(self, interval: int = 30):
        """开始性能监控"""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop(interval))
        logger.info("性能监控已启动")
    
    async def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_enabled = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("性能监控已停止")
    
    async def _monitoring_loop(self, interval: int):
        """监控循环"""
        while self.monitoring_enabled:
            try:
                metrics = self._collect_metrics()
                self.metrics_history.append(metrics)
                
                # 保持历史记录在合理范围内
                if len(self.metrics_history) > 1000:
                    self.metrics_history = self.metrics_history[-500:]
                
                # 检查性能问题
                await self._check_performance_issues(metrics)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"性能监控异常: {e}")
                await asyncio.sleep(interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        # CPU和内存
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        # 磁盘IO
        disk_io = psutil.disk_io_counters()
        disk_read_mb = disk_io.read_bytes / 1024 / 1024 if disk_io else 0
        disk_write_mb = disk_io.write_bytes / 1024 / 1024 if disk_io else 0
        
        # 网络IO
        net_io = psutil.net_io_counters()
        net_sent_mb = net_io.bytes_sent / 1024 / 1024 if net_io else 0
        net_recv_mb = net_io.bytes_recv / 1024 / 1024 if net_io else 0
        
        # 线程数
        active_threads = threading.active_count()
        
        # 缓存统计
        cache_stats = self.cache_manager.get_stats()
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / 1024 / 1024,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=net_sent_mb,
            network_recv_mb=net_recv_mb,
            active_threads=active_threads,
            cache_hit_rate=cache_stats["hit_rate"],
            cache_size_mb=cache_stats["total_size_mb"]
        )
    
    async def _check_performance_issues(self, metrics: PerformanceMetrics):
        """检查性能问题"""
        # 内存压力检查
        if metrics.memory_percent > 80:
            logger.warning(f"内存使用率过高: {metrics.memory_percent:.1f}%")
            self.memory_manager.optimize_memory()
        
        # CPU使用率检查
        if metrics.cpu_percent > 90:
            logger.warning(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
        
        # 缓存命中率检查
        if metrics.cache_hit_rate < 0.5:
            logger.warning(f"缓存命中率过低: {metrics.cache_hit_rate:.2%}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-10:]  # 最近10个指标
        
        return {
            "current_metrics": self.metrics_history[-1].__dict__ if self.metrics_history else {},
            "average_cpu": sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
            "average_memory": sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
            "cache_stats": self.cache_manager.get_stats(),
            "concurrency_stats": self.concurrency_manager.get_stats(),
            "monitoring_enabled": self.monitoring_enabled
        }
    
    def optimize_performance(self):
        """优化性能"""
        logger.info("开始性能优化...")
        
        # 内存优化
        self.memory_manager.optimize_memory()
        
        # 缓存优化
        cache_stats = self.cache_manager.get_stats()
        if cache_stats["hit_rate"] < 0.3:
            # 如果命中率太低，清理一些缓存
            self.cache_manager._evict_entries()
        
        logger.info("性能优化完成")
    
    async def shutdown(self):
        """关闭性能管理器"""
        await self.stop_monitoring()
        self.cache_manager.clear()
        logger.info("性能管理器已关闭")
