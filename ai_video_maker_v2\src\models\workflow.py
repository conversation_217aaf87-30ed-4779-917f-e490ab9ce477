# -*- coding: utf-8 -*-
"""
工作流模型 - 工作流数据结构

定义工作流相关的数据结构：
- 工作流数据
- 阶段数据
- 任务数据
"""

import uuid
from dataclasses import dataclass, field, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any

class WorkflowStatus(Enum):
    """工作流状态"""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class StageStatus(Enum):
    """阶段状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

@dataclass
class TaskData:
    """任务数据"""
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    task_type: str = ""  # llm, image, voice, video等
    
    # 状态信息
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0  # 0.0 - 1.0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 执行信息
    service_provider: str = ""
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # 依赖关系
    depends_on: List[str] = field(default_factory=list)  # task_ids
    
    # 执行日志
    execution_log: List[str] = field(default_factory=list)
    
    def start_task(self):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.now()
        self.add_log("任务开始执行")
    
    def complete_task(self, output_data: Dict[str, Any]):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()
        self.progress = 1.0
        self.output_data = output_data
        self.add_log("任务执行完成")
    
    def fail_task(self, error_message: str):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
        self.add_log(f"任务执行失败: {error_message}")
    
    def retry_task(self):
        """重试任务"""
        if self.retry_count < self.max_retries:
            self.retry_count += 1
            self.status = TaskStatus.RETRYING
            self.error_message = None
            self.add_log(f"任务重试 (第{self.retry_count}次)")
            return True
        return False
    
    def update_progress(self, progress: float, message: str = ""):
        """更新进度"""
        self.progress = max(0.0, min(1.0, progress))
        if message:
            self.add_log(f"进度更新: {progress:.1%} - {message}")
    
    def add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().isoformat()
        self.execution_log.append(f"[{timestamp}] {message}")
    
    def get_execution_time(self) -> Optional[float]:
        """获取执行时间（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskData':
        """从字典创建"""
        data = data.copy()
        data['status'] = TaskStatus(data.get('status', 'pending'))
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            data['started_at'] = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            data['completed_at'] = datetime.fromisoformat(data['completed_at'])
        return cls(**data)

@dataclass
class StageData:
    """阶段数据"""
    stage_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    stage_number: int = 1
    
    # 状态信息
    status: StageStatus = StageStatus.PENDING
    progress: float = 0.0  # 0.0 - 1.0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 任务列表
    tasks: List[TaskData] = field(default_factory=list)
    
    # 阶段输入输出
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error_message: Optional[str] = None
    
    # 阶段配置
    stage_config: Dict[str, Any] = field(default_factory=dict)
    
    def add_task(self, task: TaskData):
        """添加任务"""
        self.tasks.append(task)
    
    def get_task(self, task_id: str) -> Optional[TaskData]:
        """获取任务"""
        for task in self.tasks:
            if task.task_id == task_id:
                return task
        return None
    
    def start_stage(self):
        """开始阶段"""
        self.status = StageStatus.RUNNING
        self.started_at = datetime.now()
    
    def complete_stage(self, output_data: Dict[str, Any]):
        """完成阶段"""
        self.status = StageStatus.COMPLETED
        self.completed_at = datetime.now()
        self.progress = 1.0
        self.output_data = output_data
    
    def fail_stage(self, error_message: str):
        """阶段失败"""
        self.status = StageStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
    
    def update_progress(self):
        """更新阶段进度"""
        if not self.tasks:
            self.progress = 0.0
            return
        
        total_progress = sum(task.progress for task in self.tasks)
        self.progress = total_progress / len(self.tasks)
    
    def get_completed_tasks(self) -> List[TaskData]:
        """获取已完成的任务"""
        return [task for task in self.tasks if task.status == TaskStatus.COMPLETED]
    
    def get_failed_tasks(self) -> List[TaskData]:
        """获取失败的任务"""
        return [task for task in self.tasks if task.status == TaskStatus.FAILED]
    
    def get_execution_time(self) -> Optional[float]:
        """获取执行时间（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        data['tasks'] = [task.to_dict() for task in self.tasks]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StageData':
        """从字典创建"""
        data = data.copy()
        data['status'] = StageStatus(data.get('status', 'pending'))
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            data['started_at'] = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            data['completed_at'] = datetime.fromisoformat(data['completed_at'])
        
        tasks = []
        for task_data in data.get('tasks', []):
            tasks.append(TaskData.from_dict(task_data))
        data['tasks'] = tasks
        
        return cls(**data)

@dataclass
class WorkflowData:
    """工作流数据"""
    workflow_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    workflow_type: str = ""  # storyboard, image_generation, voice_generation等
    
    # 状态信息
    status: WorkflowStatus = WorkflowStatus.CREATED
    progress: float = 0.0  # 0.0 - 1.0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 阶段列表
    stages: List[StageData] = field(default_factory=list)
    current_stage: Optional[str] = None  # stage_id
    
    # 工作流输入输出
    input_data: Dict[str, Any] = field(default_factory=dict)
    output_data: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error_message: Optional[str] = None
    
    # 工作流配置
    workflow_config: Dict[str, Any] = field(default_factory=dict)
    
    # 执行统计
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    
    def add_stage(self, stage: StageData):
        """添加阶段"""
        self.stages.append(stage)
        self.total_tasks += len(stage.tasks)
    
    def get_stage(self, stage_id: str) -> Optional[StageData]:
        """获取阶段"""
        for stage in self.stages:
            if stage.stage_id == stage_id:
                return stage
        return None
    
    def get_current_stage(self) -> Optional[StageData]:
        """获取当前阶段"""
        if self.current_stage:
            return self.get_stage(self.current_stage)
        return None
    
    def start_workflow(self):
        """开始工作流"""
        self.status = WorkflowStatus.RUNNING
        self.started_at = datetime.now()
        
        # 设置第一个阶段为当前阶段
        if self.stages:
            self.current_stage = self.stages[0].stage_id
    
    def complete_workflow(self, output_data: Dict[str, Any]):
        """完成工作流"""
        self.status = WorkflowStatus.COMPLETED
        self.completed_at = datetime.now()
        self.progress = 1.0
        self.output_data = output_data
    
    def fail_workflow(self, error_message: str):
        """工作流失败"""
        self.status = WorkflowStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
    
    def pause_workflow(self):
        """暂停工作流"""
        self.status = WorkflowStatus.PAUSED
    
    def resume_workflow(self):
        """恢复工作流"""
        self.status = WorkflowStatus.RUNNING
    
    def cancel_workflow(self):
        """取消工作流"""
        self.status = WorkflowStatus.CANCELLED
        self.completed_at = datetime.now()
    
    def update_progress(self):
        """更新工作流进度"""
        if not self.stages:
            self.progress = 0.0
            return
        
        total_progress = sum(stage.progress for stage in self.stages)
        self.progress = total_progress / len(self.stages)
        
        # 更新任务统计
        self.completed_tasks = 0
        self.failed_tasks = 0
        
        for stage in self.stages:
            self.completed_tasks += len(stage.get_completed_tasks())
            self.failed_tasks += len(stage.get_failed_tasks())
    
    def get_execution_time(self) -> Optional[float]:
        """获取执行时间（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def get_summary(self) -> Dict[str, Any]:
        """获取工作流摘要"""
        return {
            'workflow_id': self.workflow_id,
            'name': self.name,
            'workflow_type': self.workflow_type,
            'status': self.status.value,
            'progress': self.progress,
            'total_stages': len(self.stages),
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'execution_time': self.get_execution_time(),
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['started_at'] = self.started_at.isoformat() if self.started_at else None
        data['completed_at'] = self.completed_at.isoformat() if self.completed_at else None
        data['stages'] = [stage.to_dict() for stage in self.stages]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowData':
        """从字典创建"""
        data = data.copy()
        data['status'] = WorkflowStatus(data.get('status', 'created'))
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            data['started_at'] = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            data['completed_at'] = datetime.fromisoformat(data['completed_at'])
        
        stages = []
        for stage_data in data.get('stages', []):
            stages.append(StageData.from_dict(stage_data))
        data['stages'] = stages
        
        return cls(**data)
