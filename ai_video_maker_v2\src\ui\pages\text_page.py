# -*- coding: utf-8 -*-
"""
文本创作页面 - AI辅助文本创作和编辑

提供智能的文本创作功能：
- 文本编辑器
- AI辅助创作
- 文本优化
- 创作模板
"""

import logging
from typing import Optional, Dict, Any
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QTextEdit, QLabel, QComboBox, QSpinBox,
                            QMessageBox, QSplitter)
from PyQt6.QtGui import QFont, QTextCursor

from ..components.card import Card, CardHeader, CardContent, CardActions
from ..components.button import PrimaryButton, SecondaryButton, LoadingButton
from ..components.input import ModernLineEdit, ModernComboBox
from ...models.project import Project

logger = logging.getLogger(__name__)

class AITextGenerationThread(QThread):
    """AI文本生成线程"""
    
    text_generated = pyqtSignal(str)
    generation_failed = pyqtSignal(str)
    
    def __init__(self, service_manager, prompt: str, mode: str, **kwargs):
        super().__init__()
        self.service_manager = service_manager
        self.prompt = prompt
        self.mode = mode
        self.kwargs = kwargs
    
    def run(self):
        """执行文本生成"""
        try:
            # 根据模式构建不同的提示词
            if self.mode == "continue":
                full_prompt = f"请继续以下故事内容，保持风格一致：\n\n{self.prompt}"
            elif self.mode == "expand":
                full_prompt = f"请扩展以下故事内容，增加更多细节和情节：\n\n{self.prompt}"
            elif self.mode == "optimize":
                full_prompt = f"请优化以下故事内容，提升文字表达和情节结构：\n\n{self.prompt}"
            elif self.mode == "rewrite":
                style = self.kwargs.get("style", "")
                full_prompt = f"请将以下内容改写为{style}风格：\n\n{self.prompt}"
            else:
                full_prompt = self.prompt
            
            # 调用LLM服务
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            result = loop.run_until_complete(
                self.service_manager.call_service(
                    "llm",
                    "generate_text",
                    prompt=full_prompt,
                    max_tokens=self.kwargs.get("max_tokens", 1000),
                    temperature=self.kwargs.get("temperature", 0.7)
                )
            )
            
            if result.success:
                generated_text = result.data.get("text", "")
                self.text_generated.emit(generated_text)
            else:
                self.generation_failed.emit(result.error)
                
        except Exception as e:
            self.generation_failed.emit(str(e))
        finally:
            loop.close()

class TextEditor(QTextEdit):
    """增强的文本编辑器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_editor()
    
    def _setup_editor(self):
        """设置编辑器"""
        # 设置字体
        font = QFont("Microsoft YaHei", 12)
        self.setFont(font)
        
        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                border: 2px solid #E0E0E0;
                border-radius: 8px;
                padding: 12px;
                background-color: white;
                line-height: 1.6;
            }
            QTextEdit:focus {
                border-color: #2196F3;
            }
        """)
        
        # 设置占位符文本
        self.setPlaceholderText("在这里输入您的故事内容...")
    
    def get_selected_text(self) -> str:
        """获取选中的文本"""
        cursor = self.textCursor()
        return cursor.selectedText()
    
    def insert_text_at_cursor(self, text: str):
        """在光标位置插入文本"""
        cursor = self.textCursor()
        cursor.insertText(text)
    
    def replace_selected_text(self, text: str):
        """替换选中的文本"""
        cursor = self.textCursor()
        if cursor.hasSelection():
            cursor.insertText(text)
        else:
            cursor.insertText(text)

class TextPage(QWidget):
    """文本创作页面"""
    
    def __init__(self, app_controller=None, parent=None):
        super().__init__(parent)
        self.app_controller = app_controller
        self.current_project: Optional[Project] = None
        self.generation_thread: Optional[AITextGenerationThread] = None
        
        self._setup_ui()
        self._setup_connections()
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)
        
        # 页面标题
        title_card = Card()
        title_header = CardHeader("文本创作", "AI辅助故事创作和编辑")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：文本编辑器
        left_panel = self._create_editor_panel()
        splitter.addWidget(left_panel)
        
        # 右侧：AI工具
        right_panel = self._create_ai_tools_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setSizes([700, 300])
        
        layout.addWidget(splitter)
    
    def _create_editor_panel(self) -> QWidget:
        """创建编辑器面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)
        
        # 编辑器工具栏
        toolbar_card = Card()
        toolbar_content = CardContent()
        
        toolbar_layout = QHBoxLayout()
        
        # 文件操作
        self.new_btn = SecondaryButton("新建")
        self.open_btn = SecondaryButton("打开")
        self.save_btn = PrimaryButton("保存")
        
        toolbar_layout.addWidget(self.new_btn)
        toolbar_layout.addWidget(self.open_btn)
        toolbar_layout.addWidget(self.save_btn)
        
        toolbar_layout.addStretch()
        
        # 字数统计
        self.word_count_label = QLabel("字数: 0")
        self.word_count_label.setStyleSheet("color: #757575; font-size: 12px;")
        toolbar_layout.addWidget(self.word_count_label)
        
        toolbar_content.add_layout(toolbar_layout)
        toolbar_card.add_widget(toolbar_content)
        layout.addWidget(toolbar_card)
        
        # 文本编辑器
        editor_card = Card()
        editor_header = CardHeader("故事编辑器", "")
        editor_card.add_widget(editor_header)
        
        editor_content = CardContent()
        
        self.text_editor = TextEditor()
        self.text_editor.textChanged.connect(self._update_word_count)
        editor_content.add_widget(self.text_editor)
        
        editor_card.add_widget(editor_content)
        layout.addWidget(editor_card)
        
        return panel
    
    def _create_ai_tools_panel(self) -> QWidget:
        """创建AI工具面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)
        
        # AI模型选择
        model_card = self._create_model_selection_card()
        layout.addWidget(model_card)
        
        # AI创作工具
        creation_card = self._create_creation_tools_card()
        layout.addWidget(creation_card)
        
        # 文本优化工具
        optimization_card = self._create_optimization_tools_card()
        layout.addWidget(optimization_card)
        
        # 创作模板
        template_card = self._create_template_card()
        layout.addWidget(template_card)
        
        layout.addStretch()
        
        return panel
    
    def _create_model_selection_card(self) -> Card:
        """创建模型选择卡片"""
        card = Card()
        header = CardHeader("AI模型", "选择文本生成模型")
        card.add_widget(header)
        
        content = CardContent()
        
        # 模型选择
        model_layout = QVBoxLayout()
        
        model_layout.addWidget(QLabel("模型:"))
        self.model_combo = ModernComboBox()
        self.model_combo.addItems([
            "GLM-4-Flash (快速)",
            "GLM-4 (标准)",
            "GLM-4-Plus (高质量)"
        ])
        model_layout.addWidget(self.model_combo)
        
        # 参数设置
        params_layout = QGridLayout()
        
        params_layout.addWidget(QLabel("创意度:"), 0, 0)
        self.temperature_spin = QSpinBox()
        self.temperature_spin.setRange(1, 10)
        self.temperature_spin.setValue(7)
        self.temperature_spin.setSuffix("/10")
        params_layout.addWidget(self.temperature_spin, 0, 1)
        
        params_layout.addWidget(QLabel("最大长度:"), 1, 0)
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 2000)
        self.max_tokens_spin.setValue(1000)
        self.max_tokens_spin.setSuffix(" 字")
        params_layout.addWidget(self.max_tokens_spin, 1, 1)
        
        model_layout.addLayout(params_layout)
        content.add_layout(model_layout)
        card.add_widget(content)
        
        return card
    
    def _create_creation_tools_card(self) -> Card:
        """创建创作工具卡片"""
        card = Card()
        header = CardHeader("AI创作", "智能文本生成")
        card.add_widget(header)
        
        content = CardContent()
        
        # 创作模式
        mode_layout = QVBoxLayout()
        
        mode_layout.addWidget(QLabel("创作模式:"))
        self.creation_mode_combo = ModernComboBox()
        self.creation_mode_combo.addItems([
            "续写故事",
            "扩展内容", 
            "重新创作",
            "自由创作"
        ])
        mode_layout.addWidget(self.creation_mode_combo)
        
        content.add_layout(mode_layout)
        card.add_widget(content)
        
        # 操作按钮
        actions = CardActions()
        
        self.generate_btn = LoadingButton("开始创作")
        actions.add_button(self.generate_btn)
        
        card.add_widget(actions)
        
        return card
    
    def _create_optimization_tools_card(self) -> Card:
        """创建优化工具卡片"""
        card = Card()
        header = CardHeader("文本优化", "改进文本质量")
        card.add_widget(header)
        
        content = CardContent()
        
        # 优化选项
        options_layout = QVBoxLayout()
        
        options_layout.addWidget(QLabel("优化类型:"))
        self.optimization_combo = ModernComboBox()
        self.optimization_combo.addItems([
            "语言润色",
            "情节优化",
            "结构调整",
            "风格统一"
        ])
        options_layout.addWidget(self.optimization_combo)
        
        content.add_layout(options_layout)
        card.add_widget(content)
        
        # 操作按钮
        actions = CardActions()
        
        self.optimize_btn = LoadingButton("优化文本")
        actions.add_button(self.optimize_btn)
        
        card.add_widget(actions)
        
        return card
    
    def _create_template_card(self) -> Card:
        """创建模板卡片"""
        card = Card()
        header = CardHeader("创作模板", "快速开始创作")
        card.add_widget(header)
        
        content = CardContent()
        
        # 模板列表
        template_layout = QVBoxLayout()
        
        template_layout.addWidget(QLabel("选择模板:"))
        self.template_combo = ModernComboBox()
        self.template_combo.addItems([
            "科幻故事",
            "爱情故事",
            "悬疑故事",
            "冒险故事",
            "日常生活",
            "历史故事"
        ])
        template_layout.addWidget(self.template_combo)
        
        content.add_layout(template_layout)
        card.add_widget(content)
        
        # 操作按钮
        actions = CardActions()
        
        self.apply_template_btn = SecondaryButton("应用模板")
        actions.add_button(self.apply_template_btn)
        
        card.add_widget(actions)
        
        return card
    
    def _setup_connections(self):
        """设置信号连接"""
        # 文件操作
        self.new_btn.clicked.connect(self._new_text)
        self.open_btn.clicked.connect(self._open_text)
        self.save_btn.clicked.connect(self._save_text)
        
        # AI创作
        self.generate_btn.clicked.connect(self._generate_text)
        self.optimize_btn.clicked.connect(self._optimize_text)
        self.apply_template_btn.clicked.connect(self._apply_template)
    
    def set_project(self, project: Project):
        """设置当前项目"""
        self.current_project = project
        
        # 加载项目文本
        if project and project.data.text_content:
            self.text_editor.setPlainText(project.data.text_content)
    
    def _update_word_count(self):
        """更新字数统计"""
        text = self.text_editor.toPlainText()
        word_count = len(text.replace(" ", "").replace("\n", ""))
        self.word_count_label.setText(f"字数: {word_count}")
    
    def _new_text(self):
        """新建文本"""
        if self.text_editor.toPlainText().strip():
            reply = QMessageBox.question(
                self, "确认",
                "当前有未保存的内容，确定要新建吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                return
        
        self.text_editor.clear()
    
    def _open_text(self):
        """打开文本文件"""
        # TODO: 实现文件打开功能
        QMessageBox.information(self, "提示", "文件打开功能正在开发中...")
    
    def _save_text(self):
        """保存文本"""
        if not self.current_project:
            QMessageBox.warning(self, "警告", "请先创建或打开项目")
            return
        
        # 保存到项目
        text_content = self.text_editor.toPlainText()
        self.current_project.data.text_content = text_content
        
        if self.current_project.save():
            QMessageBox.information(self, "成功", "文本已保存到项目")
        else:
            QMessageBox.critical(self, "错误", "保存失败")
    
    def _generate_text(self):
        """生成文本"""
        if not self.app_controller or not self.app_controller.service_manager:
            QMessageBox.warning(self, "警告", "AI服务未初始化")
            return
        
        # 获取当前文本作为上下文
        current_text = self.text_editor.toPlainText().strip()
        
        # 根据创作模式确定提示词
        mode_map = {
            "续写故事": "continue",
            "扩展内容": "expand",
            "重新创作": "rewrite",
            "自由创作": "create"
        }
        
        mode = mode_map.get(self.creation_mode_combo.currentText(), "continue")
        
        if mode != "create" and not current_text:
            QMessageBox.warning(self, "警告", "请先输入一些文本作为创作基础")
            return
        
        # 启动生成线程
        self.generation_thread = AITextGenerationThread(
            self.app_controller.service_manager,
            current_text,
            mode,
            max_tokens=self.max_tokens_spin.value(),
            temperature=self.temperature_spin.value() / 10.0
        )
        
        self.generation_thread.text_generated.connect(self._on_text_generated)
        self.generation_thread.generation_failed.connect(self._on_generation_failed)
        
        self.generate_btn.set_loading(True)
        self.generation_thread.start()
    
    def _optimize_text(self):
        """优化文本"""
        selected_text = self.text_editor.get_selected_text()
        if not selected_text:
            current_text = self.text_editor.toPlainText().strip()
            if not current_text:
                QMessageBox.warning(self, "警告", "请输入或选择要优化的文本")
                return
        else:
            current_text = selected_text
        
        if not self.app_controller or not self.app_controller.service_manager:
            QMessageBox.warning(self, "警告", "AI服务未初始化")
            return
        
        # 启动优化线程
        self.generation_thread = AITextGenerationThread(
            self.app_controller.service_manager,
            current_text,
            "optimize",
            optimization_type=self.optimization_combo.currentText()
        )
        
        self.generation_thread.text_generated.connect(self._on_text_optimized)
        self.generation_thread.generation_failed.connect(self._on_generation_failed)
        
        self.optimize_btn.set_loading(True)
        self.generation_thread.start()
    
    def _apply_template(self):
        """应用模板"""
        template_name = self.template_combo.currentText()
        
        templates = {
            "科幻故事": "在遥远的未来，人类已经掌握了星际旅行的技术...",
            "爱情故事": "在一个阳光明媚的午后，他们在咖啡厅里相遇了...",
            "悬疑故事": "深夜时分，一阵急促的敲门声打破了寂静...",
            "冒险故事": "古老的地图指向了一个传说中的宝藏...",
            "日常生活": "又是平凡的一天，但今天似乎有些不同...",
            "历史故事": "在那个动荡的年代，一个普通人的命运即将改变..."
        }
        
        template_text = templates.get(template_name, "")
        if template_text:
            if self.text_editor.toPlainText().strip():
                reply = QMessageBox.question(
                    self, "确认",
                    "当前有内容，确定要替换为模板内容吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                
                if reply == QMessageBox.StandardButton.No:
                    return
            
            self.text_editor.setPlainText(template_text)
    
    def _on_text_generated(self, text: str):
        """文本生成完成"""
        self.generate_btn.set_loading(False)
        
        # 在光标位置插入生成的文本
        self.text_editor.insert_text_at_cursor("\n\n" + text)
    
    def _on_text_optimized(self, text: str):
        """文本优化完成"""
        self.optimize_btn.set_loading(False)
        
        # 如果有选中文本，则替换；否则追加
        if self.text_editor.get_selected_text():
            self.text_editor.replace_selected_text(text)
        else:
            self.text_editor.setPlainText(text)
    
    def _on_generation_failed(self, error: str):
        """生成失败"""
        self.generate_btn.set_loading(False)
        self.optimize_btn.set_loading(False)
        
        QMessageBox.critical(self, "生成失败", f"AI文本生成失败:\n{error}")
