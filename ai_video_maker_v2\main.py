#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 V2.0 - 主程序入口

现代化的AI视频生成器，提供完整的从文本到视频的创作流程。

特性：
- 现代化卡片式界面设计
- 五阶段智能分镜生成
- 多引擎AI服务集成
- 高性能异步处理
- 完整的项目管理
"""

import sys
import asyncio
import logging
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.app_controller import AppController
from src.ui.main_window import MainWindow
from src.ui.theme import ThemeManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_video_maker.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class SplashScreen(QSplashScreen):
    """启动画面"""
    
    def __init__(self):
        # 创建简单的启动画面
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.GlobalColor.white)
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
        
        # 设置字体
        font = QFont("Segoe UI", 16, QFont.Weight.Bold)
        self.setFont(font)
        
        # 显示启动信息
        self.showMessage("AI视频生成器 V2.0", Qt.AlignmentFlag.AlignCenter, Qt.GlobalColor.black)
    
    def show_message(self, message: str):
        """显示消息"""
        self.showMessage(message, Qt.AlignmentFlag.AlignCenter, Qt.GlobalColor.black)
        QApplication.processEvents()

class Application:
    """应用程序类"""
    
    def __init__(self):
        self.app = None
        self.app_controller = None
        self.main_window = None
        self.splash = None
    
    def setup_qt_application(self):
        """设置Qt应用程序"""
        # 设置Qt应用程序属性
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling)
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps)
        
        # 创建应用程序实例
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("AI视频生成器")
        self.app.setApplicationVersion("2.0.0")
        self.app.setOrganizationName("AI Video Maker")
        
        # 设置应用程序样式
        self.app.setStyle("Fusion")
        
        logger.info("Qt应用程序初始化完成")
    
    def show_splash_screen(self):
        """显示启动画面"""
        self.splash = SplashScreen()
        self.splash.show()
        self.splash.show_message("正在初始化...")
        
        # 处理事件以显示启动画面
        QApplication.processEvents()
    
    async def initialize_app_controller(self):
        """初始化应用程序控制器"""
        try:
            if self.splash:
                self.splash.show_message("正在初始化核心组件...")
            
            # 创建应用程序控制器
            self.app_controller = AppController(
                config_dir="config",
                data_dir="data"
            )
            
            # 初始化控制器
            success = await self.app_controller.initialize()
            
            if not success:
                raise RuntimeError("应用程序控制器初始化失败")
            
            logger.info("应用程序控制器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"初始化应用程序控制器失败: {e}")
            if self.splash:
                self.splash.close()
            
            QMessageBox.critical(
                None,
                "初始化失败",
                f"应用程序初始化失败:\n{str(e)}\n\n请检查配置文件和网络连接。"
            )
            return False
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            if self.splash:
                self.splash.show_message("正在创建用户界面...")
            
            # 创建主窗口
            self.main_window = MainWindow(self.app_controller)
            
            # 设置主题
            theme_manager = ThemeManager()
            theme_manager.set_theme("light")  # 默认使用浅色主题
            
            logger.info("主窗口创建完成")
            return True
            
        except Exception as e:
            logger.error(f"创建主窗口失败: {e}")
            if self.splash:
                self.splash.close()
            
            QMessageBox.critical(
                None,
                "界面创建失败",
                f"用户界面创建失败:\n{str(e)}"
            )
            return False
    
    def show_main_window(self):
        """显示主窗口"""
        if self.main_window:
            # 关闭启动画面
            if self.splash:
                self.splash.finish(self.main_window)
            
            # 显示主窗口
            self.main_window.show()
            
            # 居中显示
            screen = QApplication.primaryScreen().geometry()
            window_geometry = self.main_window.geometry()
            x = (screen.width() - window_geometry.width()) // 2
            y = (screen.height() - window_geometry.height()) // 2
            self.main_window.move(x, y)
            
            logger.info("主窗口已显示")
    
    async def run(self):
        """运行应用程序"""
        try:
            # 1. 设置Qt应用程序
            self.setup_qt_application()
            
            # 2. 显示启动画面
            self.show_splash_screen()
            
            # 3. 初始化应用程序控制器
            if not await self.initialize_app_controller():
                return 1
            
            # 4. 创建主窗口
            if not self.create_main_window():
                return 1
            
            # 5. 显示主窗口
            self.show_main_window()
            
            # 6. 运行事件循环
            logger.info("应用程序启动完成，进入事件循环")
            return self.app.exec()
            
        except Exception as e:
            logger.error(f"应用程序运行失败: {e}")
            
            if self.splash:
                self.splash.close()
            
            QMessageBox.critical(
                None,
                "运行错误",
                f"应用程序运行时发生错误:\n{str(e)}"
            )
            return 1
        
        finally:
            # 清理资源
            if self.app_controller:
                try:
                    await self.app_controller.shutdown()
                except Exception as e:
                    logger.error(f"关闭应用程序控制器失败: {e}")

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 9):
        print("错误: 需要Python 3.9或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'PyQt6',
        'asyncio',
        'aiohttp',
        'Pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误: 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

async def main():
    """主函数"""
    print("🎬 AI视频生成器 V2.0")
    print("=" * 50)
    
    # 检查运行环境
    if not check_python_version():
        return 1
    
    if not check_dependencies():
        return 1
    
    # 创建并运行应用程序
    app = Application()
    return await app.run()

if __name__ == "__main__":
    try:
        # 在Windows上设置事件循环策略
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # 运行应用程序
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序异常退出: {e}", exc_info=True)
        print(f"程序异常退出: {e}")
        sys.exit(1)
