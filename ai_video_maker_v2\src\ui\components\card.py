# -*- coding: utf-8 -*-
"""
卡片组件 - 现代化的卡片设计

提供Material Design风格的卡片组件：
- 基础卡片
- 卡片头部
- 卡片内容
- 卡片操作区
"""

from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtGui import QPainter, QPainterPath, QColor, QLinearGradient
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QGraphicsDropShadowEffect)

class Card(QFrame):
    """基础卡片组件"""
    
    def __init__(self, parent=None, elevation=2):
        super().__init__(parent)
        self.elevation = elevation
        self._hover_elevation = elevation + 2
        self._current_elevation = elevation
        
        self.setObjectName("card")
        self.setFrameStyle(QFrame.Shape.NoFrame)
        
        # 设置基本样式
        self._setup_style()
        
        # 设置阴影效果
        self._setup_shadow()
        
        # 设置动画
        self._setup_animations()
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            Card {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #E0E0E0;
            }
        """)
    
    def _setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(self.elevation * 2)
        self.shadow_effect.setOffset(0, self.elevation)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(self.shadow_effect)
    
    def _setup_animations(self):
        """设置动画"""
        self.elevation_animation = QPropertyAnimation(self, b"current_elevation")
        self.elevation_animation.setDuration(200)
        self.elevation_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.elevation_animation.valueChanged.connect(self._update_shadow)
    
    @pyqtProperty(float)
    def current_elevation(self):
        return self._current_elevation
    
    @current_elevation.setter
    def current_elevation(self, value):
        self._current_elevation = value
    
    def _update_shadow(self):
        """更新阴影效果"""
        self.shadow_effect.setBlurRadius(self._current_elevation * 2)
        self.shadow_effect.setOffset(0, self._current_elevation)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.elevation_animation.setStartValue(self._current_elevation)
        self.elevation_animation.setEndValue(self._hover_elevation)
        self.elevation_animation.start()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.elevation_animation.setStartValue(self._current_elevation)
        self.elevation_animation.setEndValue(self.elevation)
        self.elevation_animation.start()
        super().leaveEvent(event)
    
    def set_elevation(self, elevation):
        """设置阴影高度"""
        self.elevation = elevation
        self._hover_elevation = elevation + 2
        self._current_elevation = elevation
        self._update_shadow()
    
    def add_widget(self, widget):
        """添加子组件"""
        self.main_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局"""
        self.main_layout.addLayout(layout)

class CardHeader(QWidget):
    """卡片头部组件"""
    
    def __init__(self, title="", subtitle="", parent=None):
        super().__init__(parent)
        self.setObjectName("card-header")
        
        # 布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 8)
        layout.setSpacing(4)
        
        # 标题
        if title:
            self.title_label = QLabel(title)
            self.title_label.setObjectName("card-title")
            self.title_label.setStyleSheet("""
                QLabel#card-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #212121;
                    margin: 0;
                }
            """)
            layout.addWidget(self.title_label)
        
        # 副标题
        if subtitle:
            self.subtitle_label = QLabel(subtitle)
            self.subtitle_label.setObjectName("card-subtitle")
            self.subtitle_label.setStyleSheet("""
                QLabel#card-subtitle {
                    font-size: 14px;
                    color: #757575;
                    margin: 0;
                }
            """)
            layout.addWidget(self.subtitle_label)
    
    def set_title(self, title):
        """设置标题"""
        if hasattr(self, 'title_label'):
            self.title_label.setText(title)
    
    def set_subtitle(self, subtitle):
        """设置副标题"""
        if hasattr(self, 'subtitle_label'):
            self.subtitle_label.setText(subtitle)

class CardContent(QWidget):
    """卡片内容组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("card-content")
        
        # 布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(16, 8, 16, 8)
        self.layout.setSpacing(8)
    
    def add_widget(self, widget):
        """添加组件"""
        self.layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局"""
        self.layout.addLayout(layout)
    
    def set_padding(self, left=16, top=8, right=16, bottom=8):
        """设置内边距"""
        self.layout.setContentsMargins(left, top, right, bottom)

class CardActions(QWidget):
    """卡片操作区组件"""
    
    def __init__(self, parent=None, align=Qt.AlignmentFlag.AlignRight):
        super().__init__(parent)
        self.setObjectName("card-actions")
        
        # 布局
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(16, 8, 16, 16)
        self.layout.setSpacing(8)
        
        # 设置对齐方式
        if align == Qt.AlignmentFlag.AlignLeft:
            self.layout.addStretch()
        elif align == Qt.AlignmentFlag.AlignCenter:
            self.layout.addStretch()
        # 默认右对齐，不需要添加stretch
    
    def add_button(self, button):
        """添加按钮"""
        self.layout.addWidget(button)
        
        # 如果是居中对齐，在最后添加stretch
        if self.layout.count() == 1:
            alignment = self.layout.alignment()
            if alignment & Qt.AlignmentFlag.AlignCenter:
                self.layout.addStretch()
    
    def add_widget(self, widget):
        """添加组件"""
        self.layout.addWidget(widget)

class ActionCard(Card):
    """可点击的操作卡片"""
    
    def __init__(self, parent=None, elevation=2):
        super().__init__(parent, elevation)
        self.setObjectName("action-card")
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 点击效果
        self._pressed = False
        
        # 重写样式
        self.setStyleSheet("""
            ActionCard {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #E0E0E0;
            }
            ActionCard:hover {
                border-color: #2196F3;
            }
        """)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self._pressed = True
            # 添加按下效果
            self.setStyleSheet("""
                ActionCard {
                    background-color: #F5F5F5;
                    border-radius: 8px;
                    border: 1px solid #2196F3;
                }
            """)
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton and self._pressed:
            self._pressed = False
            # 恢复样式
            self.setStyleSheet("""
                ActionCard {
                    background-color: white;
                    border-radius: 8px;
                    border: 1px solid #E0E0E0;
                }
                ActionCard:hover {
                    border-color: #2196F3;
                }
            """)
            # 发出点击信号（如果需要的话）
            self.clicked()
        super().mouseReleaseEvent(event)
    
    def clicked(self):
        """点击事件（子类可重写）"""
        pass

class InfoCard(Card):
    """信息展示卡片"""
    
    def __init__(self, title="", value="", icon=None, parent=None):
        super().__init__(parent, elevation=1)
        self.setObjectName("info-card")
        
        # 主布局
        main_layout = QHBoxLayout()
        self.main_layout.addLayout(main_layout)
        
        # 图标
        if icon:
            icon_label = QLabel()
            icon_label.setPixmap(icon)
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setFixedSize(48, 48)
            main_layout.addWidget(icon_label)
        
        # 文本区域
        text_layout = QVBoxLayout()
        text_layout.setSpacing(4)
        
        # 标题
        if title:
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #757575;
                    font-weight: 500;
                }
            """)
            text_layout.addWidget(title_label)
        
        # 值
        if value:
            self.value_label = QLabel(value)
            self.value_label.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    color: #212121;
                    font-weight: 600;
                }
            """)
            text_layout.addWidget(self.value_label)
        
        main_layout.addLayout(text_layout)
        main_layout.addStretch()
        
        # 设置固定高度
        self.setFixedHeight(80)
    
    def set_value(self, value):
        """设置值"""
        if hasattr(self, 'value_label'):
            self.value_label.setText(str(value))
