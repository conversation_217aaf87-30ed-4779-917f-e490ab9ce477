# 更新日志

所有重要的项目更改都将记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [4.0.0] - 2025-06-19

### 新增
- ✨ 图像生成后自动刷新预览区域功能
- 🖼️ 设为主图功能，支持将任意生成的图像设为镜头主图
- 🗑️ 删除图像功能，支持删除不满意的生成图像
- 📁 项目文件清理和重构，移除无用文件
- 📚 完善文档结构，所有文档移至docs目录
- 🚀 新增启动脚本start.py，提供更友好的启动体验
- 📄 添加LICENSE文件和完整的项目文档

### 修复
- 🔧 修复项目保存时ConsistencyControlPanel和StoryboardImageGenerationTab缺少get_project_data方法的问题
- 🖼️ 修复图像生成完成后预览区域不自动更新的问题
- 💾 修复项目数据保存时的警告信息

### 改进
- 📝 重写README.md文件，提供更清晰的项目介绍和使用指南
- 📦 优化requirements.txt，只包含实际需要的依赖包
- 🗂️ 整理项目结构，删除测试文件和临时文件
- 📖 新增部署指南和项目概览文档
- 🎯 改进用户体验，图像生成后立即显示在预览区域

### 删除
- 🧹 删除所有测试文件和临时脚本
- 📁 删除__pycache__目录和编译文件
- 📄 删除过时的文档文件
- 🔧 删除不必要的开发工具脚本

## [3.0.0] - 2025-06-17

### 新增
- ✨ 智能种子值管理系统
- 🔄 AI绘图设置与分镜生成的双向参数同步
- 🎨 Pollinations AI界面优化
- 🛠️ 一致性描述增强功能改进

### 改进
- 📝 完善用户文档和使用说明
- 🎯 优化用户界面体验
- 🔧 改进系统稳定性

## [2.1.0] - 2025-06-10

### 新增
- 🌐 LLM翻译功能优化
- 🔄 百度翻译作为备用翻译方案
- 🛡️ 翻译失败处理机制

### 改进
- 📈 增强系统稳定性和容错能力
- 🎯 优化翻译准确性
- 🔧 改进错误处理机制

## [2.0.0] - 2025-06-01

### 新增
- 🏗️ 全新的模块化架构设计
- 🔌 多AI服务提供商支持
- ⚡ 异步处理和现代化UI界面
- 🎨 智能优化和一致性保持系统
- 📋 完整的错误处理和日志系统

### 改进
- 🚀 大幅提升性能和稳定性
- 🎯 改进用户体验
- 📚 完善文档和使用指南

## [1.0.0] - 2025-05-15

### 新增
- 🎬 五阶段智能分镜系统
- 🖼️ 多引擎图像生成支持
- 🎨 一致性控制系统
- 📁 项目管理功能
- 🌐 多语言翻译支持

### 功能特性
- 支持从文本到视频的完整工作流程
- 智能分镜脚本生成
- 多种AI图像生成引擎
- 角色和场景一致性控制
- 项目文件管理和数据持久化

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 更新类型
- **新增**: 新功能
- **修复**: 问题修复
- **改进**: 现有功能改进
- **删除**: 移除的功能
- **安全**: 安全相关修复

### 获取更新
- 从GitHub下载最新版本
- 使用git pull更新代码
- 查看README.md了解最新安装说明

### 升级注意事项
- 升级前请备份项目数据
- 查看破坏性更改说明
- 更新配置文件格式
- 重新安装依赖包
