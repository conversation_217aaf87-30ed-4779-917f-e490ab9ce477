# 迁移指南 🔄

本指南将帮助您从旧版本的AI视频生成器迁移到V2.0版本。

## 迁移概述 📋

### 主要变化

1. **全新架构**: 采用现代化的模块化架构
2. **界面重设计**: 卡片式现代界面设计
3. **性能提升**: 异步处理和并发优化
4. **功能增强**: 五阶段分镜系统优化
5. **配置简化**: 统一的配置管理系统

### 兼容性说明

- ✅ **项目文件**: 支持导入旧版本项目
- ✅ **配置文件**: 自动转换旧配置格式
- ✅ **媒体资源**: 兼容现有图像、音频、视频文件
- ⚠️ **插件系统**: 需要重新适配插件接口
- ❌ **自定义脚本**: 需要重写基于新API

## 迁移前准备 🛠️

### 1. 数据备份

**备份项目文件**:
```bash
# 创建备份目录
mkdir backup_$(date +%Y%m%d)

# 备份项目目录
cp -r output/ backup_$(date +%Y%m%d)/projects/

# 备份配置文件
cp config.json backup_$(date +%Y%m%d)/config_old.json
```

**备份媒体资源**:
```bash
# 备份图像文件
cp -r output/*/images/ backup_$(date +%Y%m%d)/images/

# 备份音频文件
cp -r output/*/audio/ backup_$(date +%Y%m%d)/audio/

# 备份视频文件
cp -r output/*/video/ backup_$(date +%Y%m%d)/video/
```

### 2. 环境检查

**检查Python版本**:
```bash
python --version
# 需要 Python 3.9 或更高版本
```

**检查依赖包**:
```bash
pip list | grep -E "(PyQt|asyncio|aiohttp)"
```

### 3. 导出项目清单

创建项目清单以便迁移后验证：
```bash
# 列出所有项目
ls -la output/ > backup_$(date +%Y%m%d)/project_list.txt

# 统计文件数量
find output/ -type f -name "*.json" | wc -l > backup_$(date +%Y%m%d)/file_count.txt
```

## 自动迁移工具 🤖

### 使用迁移脚本

V2.0提供了自动迁移工具：

```bash
# 下载V2.0版本
git clone https://github.com/your-repo/ai_video_maker_v2.git
cd ai_video_maker_v2

# 运行迁移工具
python tools/migrate.py --source /path/to/old/version --target ./data/
```

### 迁移选项

```bash
python tools/migrate.py [选项]

选项:
  --source PATH     旧版本数据目录路径
  --target PATH     新版本数据目录路径
  --config PATH     旧版本配置文件路径
  --dry-run         预览迁移操作，不实际执行
  --verbose         显示详细迁移日志
  --backup          自动创建备份
```

### 迁移示例

```bash
# 完整迁移（推荐）
python tools/migrate.py \
  --source /old/ai_video_maker/ \
  --target ./data/ \
  --config /old/ai_video_maker/config.json \
  --backup \
  --verbose

# 仅迁移项目文件
python tools/migrate.py \
  --source /old/ai_video_maker/output/ \
  --target ./data/projects/ \
  --dry-run
```

## 手动迁移步骤 ✋

### 1. 项目文件迁移

#### 旧版本项目结构
```
output/
├── project_name/
│   ├── project.json
│   ├── texts/
│   ├── images/
│   ├── audio/
│   └── video/
```

#### 新版本项目结构
```
data/projects/
├── project_name_12345678/
│   ├── project.json
│   ├── assets/
│   │   ├── images/
│   │   ├── audio/
│   │   └── video/
│   └── cache/
```

#### 迁移脚本示例

```python
import json
import shutil
from pathlib import Path
from datetime import datetime

def migrate_project(old_project_path, new_projects_dir):
    """迁移单个项目"""
    old_path = Path(old_project_path)
    project_name = old_path.name
    
    # 读取旧项目文件
    old_project_file = old_path / "project.json"
    if not old_project_file.exists():
        print(f"跳过 {project_name}: 缺少project.json")
        return
    
    with open(old_project_file, 'r', encoding='utf-8') as f:
        old_data = json.load(f)
    
    # 创建新项目目录
    import uuid
    project_id = str(uuid.uuid4())[:8]
    new_project_dir = new_projects_dir / f"{project_name}_{project_id}"
    new_project_dir.mkdir(parents=True, exist_ok=True)
    
    # 转换项目数据格式
    new_data = convert_project_data(old_data, project_id)
    
    # 保存新项目文件
    new_project_file = new_project_dir / "project.json"
    with open(new_project_file, 'w', encoding='utf-8') as f:
        json.dump(new_data, f, indent=2, ensure_ascii=False)
    
    # 迁移媒体文件
    migrate_media_files(old_path, new_project_dir)
    
    print(f"✅ 项目迁移完成: {project_name}")

def convert_project_data(old_data, project_id):
    """转换项目数据格式"""
    return {
        "metadata": {
            "project_id": project_id,
            "name": old_data.get("project_name", "未命名项目"),
            "description": old_data.get("description", ""),
            "version": "2.0.0",
            "created_at": old_data.get("created_time", datetime.now().isoformat()),
            "updated_at": datetime.now().isoformat(),
            "author": old_data.get("author", ""),
            "tags": old_data.get("tags", [])
        },
        "settings": {
            "video_width": old_data.get("video_width", 1920),
            "video_height": old_data.get("video_height", 1080),
            "video_fps": old_data.get("fps", 30),
            "default_llm_provider": "zhipu",
            "default_image_provider": "cogview",
            "default_voice_provider": "azure",
            "default_video_provider": "cogvideo"
        },
        "data": {
            "original_text": old_data.get("original_text", ""),
            "rewritten_text": old_data.get("rewritten_text", ""),
            "storyboard_data": convert_storyboard_data(old_data.get("storyboard", {})),
            "images": convert_media_data(old_data.get("images", {})),
            "audio_files": convert_media_data(old_data.get("audio", {})),
            "video_files": convert_media_data(old_data.get("videos", {})),
            "current_stage": old_data.get("current_stage", "text")
        },
        "status": "completed" if old_data.get("completed", False) else "in_progress",
        "version": "2.0.0"
    }

def migrate_media_files(old_project_path, new_project_dir):
    """迁移媒体文件"""
    # 创建assets目录
    assets_dir = new_project_dir / "assets"
    assets_dir.mkdir(exist_ok=True)
    
    # 迁移图像文件
    old_images = old_project_path / "images"
    if old_images.exists():
        new_images = assets_dir / "images"
        shutil.copytree(old_images, new_images, dirs_exist_ok=True)
    
    # 迁移音频文件
    old_audio = old_project_path / "audio"
    if old_audio.exists():
        new_audio = assets_dir / "audio"
        shutil.copytree(old_audio, new_audio, dirs_exist_ok=True)
    
    # 迁移视频文件
    old_video = old_project_path / "video"
    if old_video.exists():
        new_video = assets_dir / "video"
        shutil.copytree(old_video, new_video, dirs_exist_ok=True)

# 使用示例
old_projects_dir = Path("/old/ai_video_maker/output")
new_projects_dir = Path("./data/projects")

for project_dir in old_projects_dir.iterdir():
    if project_dir.is_dir():
        migrate_project(project_dir, new_projects_dir)
```

### 2. 配置文件迁移

#### 旧配置格式转换

```python
def migrate_config(old_config_path, new_config_path):
    """迁移配置文件"""
    with open(old_config_path, 'r', encoding='utf-8') as f:
        old_config = json.load(f)
    
    new_config = {
        "environment": "production",
        "debug": False,
        "log_level": "INFO",
        "data_dir": "data",
        "cache_dir": "cache",
        
        "ui": {
            "theme": old_config.get("theme", "light"),
            "language": old_config.get("language", "zh_CN"),
            "window_width": old_config.get("window_width", 1400),
            "window_height": old_config.get("window_height", 900),
            "auto_save_interval": 300
        },
        
        "performance": {
            "max_concurrent_tasks": old_config.get("max_concurrent_tasks", 3),
            "cache_size_mb": 512,
            "enable_gpu": old_config.get("enable_gpu", True),
            "memory_limit_mb": 2048
        },
        
        "llm_apis": convert_api_configs(old_config.get("llm_apis", {})),
        "image_apis": convert_api_configs(old_config.get("image_apis", {})),
        "voice_apis": convert_api_configs(old_config.get("voice_apis", {})),
        "video_apis": convert_api_configs(old_config.get("video_apis", {}))
    }
    
    with open(new_config_path, 'w', encoding='utf-8') as f:
        json.dump(new_config, f, indent=2, ensure_ascii=False)
```

### 3. 媒体资源验证

迁移完成后验证媒体资源：

```python
def verify_migration(new_projects_dir):
    """验证迁移结果"""
    projects_dir = Path(new_projects_dir)
    
    for project_dir in projects_dir.iterdir():
        if not project_dir.is_dir():
            continue
            
        print(f"验证项目: {project_dir.name}")
        
        # 检查项目文件
        project_file = project_dir / "project.json"
        if not project_file.exists():
            print(f"  ❌ 缺少project.json")
            continue
        
        # 检查assets目录
        assets_dir = project_dir / "assets"
        if assets_dir.exists():
            image_count = len(list((assets_dir / "images").glob("*"))) if (assets_dir / "images").exists() else 0
            audio_count = len(list((assets_dir / "audio").glob("*"))) if (assets_dir / "audio").exists() else 0
            video_count = len(list((assets_dir / "video").glob("*"))) if (assets_dir / "video").exists() else 0
            
            print(f"  ✅ 图像: {image_count}, 音频: {audio_count}, 视频: {video_count}")
        else:
            print(f"  ⚠️ 无assets目录")
```

## 迁移后验证 ✅

### 1. 启动新版本

```bash
cd ai_video_maker_v2
python main.py
```

### 2. 检查项目列表

在仪表板中确认所有项目都已正确迁移。

### 3. 测试核心功能

- 打开迁移的项目
- 检查分镜数据完整性
- 验证媒体资源可访问
- 测试新功能正常工作

### 4. 性能对比

对比新旧版本的性能表现：
- 启动时间
- 项目加载速度
- 分镜生成效率
- 内存使用情况

## 常见问题解决 🔧

### 1. 项目文件损坏

**问题**: 迁移后项目无法打开
**解决方案**:
```bash
# 检查JSON格式
python -m json.tool data/projects/project_name/project.json

# 使用备份恢复
cp backup_20250629/projects/project_name/project.json data/projects/project_name_12345678/
```

### 2. 媒体文件丢失

**问题**: 图像或音频文件无法显示
**解决方案**:
```bash
# 检查文件路径
find data/projects/project_name_12345678/ -name "*.png" -o -name "*.jpg" -o -name "*.mp3"

# 重新复制媒体文件
cp -r backup_20250629/images/* data/projects/project_name_12345678/assets/images/
```

### 3. 配置不兼容

**问题**: API配置无法工作
**解决方案**:
```bash
# 重置配置文件
cp config/config.example.json config/config.json

# 手动添加API密钥
nano config/config.json
```

### 4. 性能问题

**问题**: 新版本运行缓慢
**解决方案**:
```json
{
  "performance": {
    "max_concurrent_tasks": 2,
    "cache_size_mb": 256,
    "memory_limit_mb": 1024
  }
}
```

## 回滚方案 ↩️

如果迁移遇到严重问题，可以回滚到旧版本：

### 1. 停止新版本
```bash
# 关闭AI视频生成器V2.0
```

### 2. 恢复旧版本数据
```bash
# 恢复项目文件
rm -rf output/
cp -r backup_20250629/projects/ output/

# 恢复配置文件
cp backup_20250629/config_old.json config.json
```

### 3. 启动旧版本
```bash
# 切换到旧版本目录
cd /old/ai_video_maker/
python main.py
```

## 迁移最佳实践 💡

### 1. 分批迁移
- 先迁移重要项目
- 验证无误后迁移其余项目
- 保留旧版本作为备份

### 2. 测试验证
- 在测试环境先进行迁移
- 验证所有功能正常
- 确认性能满足要求

### 3. 用户培训
- 熟悉新界面布局
- 了解新功能特性
- 掌握新的操作流程

### 4. 渐进式切换
- 并行运行新旧版本
- 逐步将工作流切换到新版本
- 确保业务连续性

## 技术支持 🆘

如果在迁移过程中遇到问题：

1. **查看迁移日志**: 检查详细错误信息
2. **使用诊断工具**: 运行内置诊断命令
3. **联系技术支持**: 提供日志文件和错误描述
4. **社区求助**: 在GitHub Issues中寻求帮助

**联系方式**:
- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 🐛 GitHub: https://github.com/your-repo/ai_video_maker_v2/issues

---

**祝您迁移顺利！** 🎉

迁移到V2.0将为您带来更好的用户体验和更强大的功能。
