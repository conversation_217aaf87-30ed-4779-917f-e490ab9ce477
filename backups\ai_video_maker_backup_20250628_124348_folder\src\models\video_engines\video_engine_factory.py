# -*- coding: utf-8 -*-
"""
视频生成引擎工厂
负责创建和管理不同类型的视频生成引擎
"""

from typing import Dict, List, Optional, Type
from .video_engine_base import VideoGenerationEngine, VideoEngineType, VideoEngineStatus
from src.utils.logger import logger
import importlib


class VideoEngineFactory:
    """视频生成引擎工厂类"""
    
    def __init__(self):
        self._engines: Dict[VideoEngineType, VideoGenerationEngine] = {}
        self._engine_classes: Dict[VideoEngineType, Type[VideoGenerationEngine]] = {}
        self._register_default_engines()
    
    def _register_default_engines(self):
        """注册默认引擎类"""
        # 注册引擎类映射
        engine_mappings = {
            VideoEngineType.COGVIDEOX_FLASH: ('cogvideox_engine', 'CogVideoXEngine'),
            VideoEngineType.REPLICATE_SVD: ('replicate_engine', 'ReplicateVideoEngine'),
            VideoEngineType.PIXVERSE: ('pixverse_engine', 'PixVerseEngine'),
        }
        
        for engine_type, (module_name, class_name) in engine_mappings.items():
            try:
                module = importlib.import_module(f'.engines.{module_name}', package=__package__)
                engine_class = getattr(module, class_name)
                self._engine_classes[engine_type] = engine_class
                logger.debug(f"已注册视频引擎类: {engine_type.value}")
            except (ImportError, AttributeError) as e:
                logger.warning(f"无法加载视频引擎 {engine_type.value}: {e}")
    
    async def create_engine(self, engine_type: VideoEngineType, 
                           config: Optional[Dict] = None) -> Optional[VideoGenerationEngine]:
        """创建引擎实例"""
        if engine_type in self._engines:
            return self._engines[engine_type]
        
        if engine_type not in self._engine_classes:
            logger.error(f"未找到视频引擎类: {engine_type.value}")
            return None
        
        try:
            engine_class = self._engine_classes[engine_type]
            engine = engine_class(config or {})
            
            # 初始化引擎
            if await engine.initialize():
                self._engines[engine_type] = engine
                logger.info(f"视频引擎 {engine_type.value} 创建成功")
                return engine
            else:
                logger.error(f"视频引擎 {engine_type.value} 初始化失败")
                return None
                
        except Exception as e:
            logger.error(f"创建视频引擎 {engine_type.value} 时出错: {e}")
            return None
    
    def get_engine(self, engine_type: VideoEngineType) -> Optional[VideoGenerationEngine]:
        """获取已创建的引擎实例"""
        return self._engines.get(engine_type)
    
    def get_available_engines(self) -> List[VideoEngineType]:
        """获取可用的引擎类型"""
        return list(self._engine_classes.keys())
    
    def get_active_engines(self) -> List[VideoEngineType]:
        """获取已激活的引擎"""
        return [engine_type for engine_type, engine in self._engines.items() 
                if engine.status != VideoEngineStatus.OFFLINE]
    
    def get_engine_statistics(self) -> Dict[VideoEngineType, Dict]:
        """获取所有引擎的统计信息"""
        stats = {}
        for engine_type, engine in self._engines.items():
            stats[engine_type] = engine.get_statistics()
        return stats
    
    async def test_all_engines(self) -> Dict[VideoEngineType, bool]:
        """测试所有引擎连接"""
        results = {}
        for engine_type, engine in self._engines.items():
            try:
                results[engine_type] = await engine.test_connection()
            except Exception as e:
                logger.error(f"测试视频引擎 {engine_type.value} 连接失败: {e}")
                results[engine_type] = False
        return results
    
    async def shutdown_all_engines(self):
        """关闭所有引擎"""
        for engine_type, engine in self._engines.items():
            try:
                if hasattr(engine, 'shutdown'):
                    await engine.shutdown()
                logger.info(f"视频引擎 {engine_type.value} 已关闭")
            except Exception as e:
                logger.error(f"关闭视频引擎 {engine_type.value} 失败: {e}")
        
        self._engines.clear()
    
    def register_custom_engine(self, engine_type: VideoEngineType, 
                              engine_class: Type[VideoGenerationEngine]):
        """注册自定义引擎类"""
        self._engine_classes[engine_type] = engine_class
        logger.info(f"已注册自定义视频引擎: {engine_type.value}")


# 全局工厂实例
video_engine_factory = VideoEngineFactory()


def get_video_engine_factory() -> VideoEngineFactory:
    """获取视频引擎工厂实例"""
    return video_engine_factory
