#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配音功能测试脚本
测试配音引擎和界面功能
"""

import sys
import os
import tempfile
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from utils.logger import logger
from utils.config_manager import ConfigManager
from services.tts_engine_service import TTSEngineManager
from utils.audio_file_manager import AudioFileManager


def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    try:
        config_manager = ConfigManager()
        print("✅ 配置管理器初始化成功")
        return config_manager
    except Exception as e:
        print(f"❌ 配置管理器初始化失败: {e}")
        return None


def test_tts_engine_manager(config_manager):
    """测试TTS引擎管理器"""
    print("\n🎵 测试TTS引擎管理器...")
    try:
        engine_manager = TTSEngineManager(config_manager)
        
        # 测试获取可用引擎
        engines = engine_manager.get_available_engines()
        print(f"✅ 可用引擎: {engines}")
        
        # 测试每个引擎的连接
        for engine_name in engines:
            engine = engine_manager.get_engine(engine_name)
            if engine:
                result = engine.test_connection()
                status = "✅" if result.get('success') else "❌"
                message = result.get('message', result.get('error', ''))
                print(f"{status} {engine_name}: {message}")
        
        return engine_manager
    except Exception as e:
        print(f"❌ TTS引擎管理器测试失败: {e}")
        return None


def test_audio_file_manager():
    """测试音频文件管理器"""
    print("\n📁 测试音频文件管理器...")
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            audio_manager = AudioFileManager(temp_dir)
            
            # 测试目录创建
            edge_dir = audio_manager.get_engine_audio_dir('edge_tts')
            if edge_dir.exists():
                print("✅ 音频目录创建成功")
            else:
                print("❌ 音频目录创建失败")
                return False
            
            # 测试文件名生成
            filename = audio_manager.generate_audio_filename('edge_tts', 1, 'test_shot')
            print(f"✅ 生成文件名: {filename}")
            
            # 测试存储信息
            storage_info = audio_manager.get_storage_info()
            print(f"✅ 存储信息: {storage_info['total_files']} 个文件")
            
            return True
    except Exception as e:
        print(f"❌ 音频文件管理器测试失败: {e}")
        return False


async def test_edge_tts_generation(engine_manager):
    """测试Edge-TTS语音生成"""
    print("\n🔊 测试Edge-TTS语音生成...")
    try:
        # 检查Edge-TTS是否可用
        engine = engine_manager.get_engine('edge_tts')
        if not engine:
            print("❌ Edge-TTS引擎不可用")
            return False
        
        # 测试连接
        connection_result = engine.test_connection()
        if not connection_result.get('success'):
            print(f"❌ Edge-TTS连接失败: {connection_result.get('error')}")
            return False
        
        # 生成测试音频
        test_text = "这是一个配音功能测试。"
        with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
            temp_path = temp_file.name
        
        try:
            result = await engine_manager.generate_speech(
                'edge_tts',
                test_text,
                temp_path,
                voice='zh-CN-YunxiNeural',
                speed=1.0,
                pitch=0
            )
            
            if result.get('success'):
                if os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                    print(f"✅ Edge-TTS语音生成成功: {temp_path}")
                    print(f"   文件大小: {os.path.getsize(temp_path)} 字节")
                    return True
                else:
                    print("❌ 音频文件生成失败或为空")
                    return False
            else:
                print(f"❌ Edge-TTS生成失败: {result.get('error')}")
                return False
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
        
    except Exception as e:
        print(f"❌ Edge-TTS测试失败: {e}")
        return False


def test_project_data_structure():
    """测试项目数据结构"""
    print("\n📊 测试项目数据结构...")
    try:
        # 模拟项目数据
        project_data = {
            'voice_generation': {
                'provider': 'edge_tts',
                'settings': {
                    'voice': 'zh-CN-YunxiNeural',
                    'speed': 1.0,
                    'pitch': 0,
                    'volume': 1.0,
                    'language': 'zh-CN'
                },
                'generated_audio': [],
                'voice_segments': [
                    {
                        'index': 0,
                        'scene_id': 'scene_1',
                        'shot_id': 'shot_1',
                        'text': '测试配音文本',
                        'status': '未生成',
                        'audio_path': '',
                        'selected': True
                    }
                ],
                'progress': {
                    'total_segments': 1,
                    'completed_segments': 0,
                    'status': 'pending'
                }
            }
        }
        
        # 验证数据结构
        voice_data = project_data.get('voice_generation', {})
        if 'provider' in voice_data and 'settings' in voice_data:
            print("✅ 项目数据结构正确")
            print(f"   引擎: {voice_data['provider']}")
            print(f"   设置: {voice_data['settings']}")
            print(f"   文本段落: {len(voice_data['voice_segments'])} 个")
            return True
        else:
            print("❌ 项目数据结构不完整")
            return False
            
    except Exception as e:
        print(f"❌ 项目数据结构测试失败: {e}")
        return False


def test_gui_components():
    """测试GUI组件导入"""
    print("\n🖥️ 测试GUI组件导入...")
    try:
        # 测试配音设置界面
        from gui.ai_voice_settings_widget import AIVoiceSettingsWidget
        print("✅ AI配音设置界面导入成功")
        
        # 测试配音工作界面
        from gui.voice_generation_tab import VoiceGenerationTab
        print("✅ 配音工作界面导入成功")
        
        return True
    except Exception as e:
        print(f"❌ GUI组件导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始配音功能测试\n")
    
    test_results = []
    
    # 1. 测试配置管理器
    config_manager = test_config_manager()
    test_results.append(config_manager is not None)
    
    if config_manager:
        # 2. 测试TTS引擎管理器
        engine_manager = test_tts_engine_manager(config_manager)
        test_results.append(engine_manager is not None)
        
        # 3. 测试音频文件管理器
        audio_test_result = test_audio_file_manager()
        test_results.append(audio_test_result)
        
        # 4. 测试Edge-TTS生成（如果可用）
        if engine_manager:
            try:
                edge_test_result = asyncio.run(test_edge_tts_generation(engine_manager))
                test_results.append(edge_test_result)
            except Exception as e:
                print(f"❌ Edge-TTS异步测试失败: {e}")
                test_results.append(False)
    
    # 5. 测试项目数据结构
    data_test_result = test_project_data_structure()
    test_results.append(data_test_result)
    
    # 6. 测试GUI组件
    gui_test_result = test_gui_components()
    test_results.append(gui_test_result)
    
    # 汇总结果
    print(f"\n📋 测试结果汇总:")
    print(f"   总测试数: {len(test_results)}")
    print(f"   成功: {sum(test_results)}")
    print(f"   失败: {len(test_results) - sum(test_results)}")
    
    if all(test_results):
        print("\n🎉 所有测试通过！配音功能集成成功！")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
