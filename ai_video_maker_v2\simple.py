#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 - 极简版本
只要能用就行
"""

import sys
import asyncio
import aiohttp
import json
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QMessageBox
)
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtGui import QFont

class AIWorker(QThread):
    """AI工作线程"""
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    
    def __init__(self, prompt, task_type):
        super().__init__()
        self.prompt = prompt
        self.task_type = task_type
        self.api_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
    
    def run(self):
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.task_type == "story":
                prompt = f"请创作一个有趣的故事：{self.prompt}"
            elif self.task_type == "storyboard":
                prompt = f"请将以下故事分解为详细的分镜脚本：\n\n{self.prompt}"
            elif self.task_type == "image":
                result = loop.run_until_complete(self.generate_image(self.prompt))
                self.finished.emit(result)
                return
            else:
                prompt = self.prompt
            
            result = loop.run_until_complete(self.generate_text(prompt))
            self.finished.emit(result)
            
        except Exception as e:
            self.error.emit(str(e))
    
    async def generate_text(self, prompt):
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "glm-4",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["choices"][0]["message"]["content"]
                    else:
                        return f"API调用失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"
    
    async def generate_image(self, prompt):
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "cogview-3",
                "prompt": prompt
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}images/generations",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["data"][0]["url"]
                    else:
                        return f"图像生成失败: {response.status}"
        except Exception as e:
            return f"错误: {str(e)}"

class SimpleAI(QMainWindow):
    """极简AI工具"""
    
    def __init__(self):
        super().__init__()
        self.worker = None
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("AI工具 - 极简版")
        self.setGeometry(100, 100, 800, 600)
        
        # 主控件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # 标题
        title = QLabel("AI工具")
        title.setFont(QFont("Arial", 16))
        layout.addWidget(title)
        
        # 输入
        layout.addWidget(QLabel("输入:"))
        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(100)
        layout.addWidget(self.input_text)
        
        # 按钮
        btn_layout = QHBoxLayout()
        
        self.story_btn = QPushButton("生成故事")
        self.story_btn.clicked.connect(self.generate_story)
        btn_layout.addWidget(self.story_btn)
        
        self.storyboard_btn = QPushButton("生成分镜")
        self.storyboard_btn.clicked.connect(self.generate_storyboard)
        btn_layout.addWidget(self.storyboard_btn)
        
        self.image_btn = QPushButton("生成图像")
        self.image_btn.clicked.connect(self.generate_image)
        btn_layout.addWidget(self.image_btn)
        
        layout.addLayout(btn_layout)
        
        # 输出
        layout.addWidget(QLabel("输出:"))
        self.output_text = QTextEdit()
        layout.addWidget(self.output_text)
        
        # 状态
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
    
    def generate_story(self):
        text = self.input_text.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入内容")
            return
        
        self.set_buttons_enabled(False)
        self.status_label.setText("正在生成故事...")
        
        self.worker = AIWorker(text, "story")
        self.worker.finished.connect(self.on_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()
    
    def generate_storyboard(self):
        text = self.input_text.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入内容")
            return
        
        self.set_buttons_enabled(False)
        self.status_label.setText("正在生成分镜...")
        
        self.worker = AIWorker(text, "storyboard")
        self.worker.finished.connect(self.on_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()
    
    def generate_image(self):
        text = self.input_text.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "警告", "请输入内容")
            return
        
        self.set_buttons_enabled(False)
        self.status_label.setText("正在生成图像...")
        
        self.worker = AIWorker(text, "image")
        self.worker.finished.connect(self.on_finished)
        self.worker.error.connect(self.on_error)
        self.worker.start()
    
    def on_finished(self, result):
        self.output_text.setPlainText(result)
        self.set_buttons_enabled(True)
        self.status_label.setText("完成")
        QMessageBox.information(self, "成功", "生成完成!")
    
    def on_error(self, error):
        self.set_buttons_enabled(True)
        self.status_label.setText("错误")
        QMessageBox.critical(self, "错误", f"生成失败: {error}")
    
    def set_buttons_enabled(self, enabled):
        self.story_btn.setEnabled(enabled)
        self.storyboard_btn.setEnabled(enabled)
        self.image_btn.setEnabled(enabled)

def main():
    app = QApplication(sys.argv)
    window = SimpleAI()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
