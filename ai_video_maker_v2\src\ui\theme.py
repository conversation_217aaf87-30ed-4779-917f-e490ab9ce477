# -*- coding: utf-8 -*-
"""
主题系统 - 现代化的主题管理

提供统一的主题管理，支持：
- 深色/浅色主题
- 自定义颜色方案
- 动态主题切换
- 主题持久化
"""

import json
import logging
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QColor, QPalette, QFont
from PyQt6.QtWidgets import QApplication

logger = logging.getLogger(__name__)

class ThemeType(Enum):
    """主题类型"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"

@dataclass
class ColorScheme:
    """颜色方案"""
    # 主要颜色
    primary: str = "#2196F3"
    primary_dark: str = "#1976D2"
    primary_light: str = "#BBDEFB"
    
    # 次要颜色
    secondary: str = "#FF9800"
    secondary_dark: str = "#F57C00"
    secondary_light: str = "#FFE0B2"
    
    # 背景颜色
    background: str = "#FFFFFF"
    surface: str = "#F5F5F5"
    card: str = "#FFFFFF"
    
    # 文本颜色
    text_primary: str = "#212121"
    text_secondary: str = "#757575"
    text_disabled: str = "#BDBDBD"
    
    # 边框和分割线
    border: str = "#E0E0E0"
    divider: str = "#EEEEEE"
    
    # 状态颜色
    success: str = "#4CAF50"
    warning: str = "#FF9800"
    error: str = "#F44336"
    info: str = "#2196F3"
    
    # 阴影
    shadow: str = "rgba(0, 0, 0, 0.1)"
    shadow_dark: str = "rgba(0, 0, 0, 0.2)"

@dataclass
class Typography:
    """字体配置"""
    font_family: str = "Segoe UI"
    font_size_xs: int = 10
    font_size_sm: int = 12
    font_size_base: int = 14
    font_size_lg: int = 16
    font_size_xl: int = 18
    font_size_2xl: int = 20
    font_size_3xl: int = 24
    
    # 字重
    font_weight_light: int = 300
    font_weight_normal: int = 400
    font_weight_medium: int = 500
    font_weight_bold: int = 700

@dataclass
class Spacing:
    """间距配置"""
    xs: int = 4
    sm: int = 8
    base: int = 16
    lg: int = 24
    xl: int = 32
    xxl: int = 48

@dataclass
class BorderRadius:
    """圆角配置"""
    none: int = 0
    sm: int = 4
    base: int = 8
    lg: int = 12
    xl: int = 16
    full: int = 9999

@dataclass
class Theme:
    """主题配置"""
    name: str
    type: ThemeType
    colors: ColorScheme
    typography: Typography = None
    spacing: Spacing = None
    border_radius: BorderRadius = None
    
    def __post_init__(self):
        if self.typography is None:
            self.typography = Typography()
        if self.spacing is None:
            self.spacing = Spacing()
        if self.border_radius is None:
            self.border_radius = BorderRadius()

class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = pyqtSignal(Theme)
    
    def __init__(self, themes_dir: str = "assets/themes"):
        super().__init__()
        self.themes_dir = Path(themes_dir)
        self.themes_dir.mkdir(parents=True, exist_ok=True)
        
        self._themes: Dict[str, Theme] = {}
        self._current_theme: Optional[Theme] = None
        
        # 加载内置主题
        self._load_builtin_themes()
        
        # 加载自定义主题
        self._load_custom_themes()
        
        # 设置默认主题
        self.set_theme("light")
    
    def _load_builtin_themes(self):
        """加载内置主题"""
        # 浅色主题
        light_colors = ColorScheme(
            primary="#2196F3",
            primary_dark="#1976D2",
            primary_light="#BBDEFB",
            secondary="#FF9800",
            secondary_dark="#F57C00",
            secondary_light="#FFE0B2",
            background="#FFFFFF",
            surface="#F8F9FA",
            card="#FFFFFF",
            text_primary="#212121",
            text_secondary="#757575",
            text_disabled="#BDBDBD",
            border="#E0E0E0",
            divider="#F0F0F0",
            success="#4CAF50",
            warning="#FF9800",
            error="#F44336",
            info="#2196F3",
            shadow="rgba(0, 0, 0, 0.08)",
            shadow_dark="rgba(0, 0, 0, 0.16)"
        )
        
        light_theme = Theme(
            name="light",
            type=ThemeType.LIGHT,
            colors=light_colors
        )
        
        # 深色主题
        dark_colors = ColorScheme(
            primary="#64B5F6",
            primary_dark="#42A5F5",
            primary_light="#90CAF9",
            secondary="#FFB74D",
            secondary_dark="#FFA726",
            secondary_light="#FFCC80",
            background="#121212",
            surface="#1E1E1E",
            card="#2D2D2D",
            text_primary="#FFFFFF",
            text_secondary="#B0B0B0",
            text_disabled="#666666",
            border="#404040",
            divider="#333333",
            success="#66BB6A",
            warning="#FFB74D",
            error="#EF5350",
            info="#64B5F6",
            shadow="rgba(0, 0, 0, 0.3)",
            shadow_dark="rgba(0, 0, 0, 0.5)"
        )
        
        dark_theme = Theme(
            name="dark",
            type=ThemeType.DARK,
            colors=dark_colors
        )
        
        self._themes["light"] = light_theme
        self._themes["dark"] = dark_theme
        
        logger.info("内置主题已加载")
    
    def _load_custom_themes(self):
        """加载自定义主题"""
        try:
            for theme_file in self.themes_dir.glob("*.json"):
                with open(theme_file, 'r', encoding='utf-8') as f:
                    theme_data = json.load(f)
                
                theme = self._parse_theme_data(theme_data)
                if theme:
                    self._themes[theme.name] = theme
                    logger.info(f"自定义主题已加载: {theme.name}")
                    
        except Exception as e:
            logger.error(f"加载自定义主题失败: {e}")
    
    def _parse_theme_data(self, data: Dict[str, Any]) -> Optional[Theme]:
        """解析主题数据"""
        try:
            colors_data = data.get('colors', {})
            colors = ColorScheme(**colors_data)
            
            typography_data = data.get('typography', {})
            typography = Typography(**typography_data)
            
            spacing_data = data.get('spacing', {})
            spacing = Spacing(**spacing_data)
            
            border_radius_data = data.get('border_radius', {})
            border_radius = BorderRadius(**border_radius_data)
            
            theme_type = ThemeType(data.get('type', 'light'))
            
            return Theme(
                name=data['name'],
                type=theme_type,
                colors=colors,
                typography=typography,
                spacing=spacing,
                border_radius=border_radius
            )
            
        except Exception as e:
            logger.error(f"解析主题数据失败: {e}")
            return None
    
    def get_theme(self, name: str) -> Optional[Theme]:
        """获取主题"""
        return self._themes.get(name)
    
    def get_current_theme(self) -> Optional[Theme]:
        """获取当前主题"""
        return self._current_theme
    
    def get_available_themes(self) -> Dict[str, Theme]:
        """获取可用主题"""
        return self._themes.copy()
    
    def set_theme(self, name: str) -> bool:
        """设置主题"""
        if name not in self._themes:
            logger.error(f"主题不存在: {name}")
            return False
        
        old_theme = self._current_theme
        self._current_theme = self._themes[name]
        
        # 应用主题到应用程序
        self._apply_theme_to_app()
        
        # 发出主题变更信号
        self.theme_changed.emit(self._current_theme)
        
        logger.info(f"主题已切换: {old_theme.name if old_theme else 'None'} -> {name}")
        return True
    
    def _apply_theme_to_app(self):
        """将主题应用到应用程序"""
        if not self._current_theme:
            return
        
        app = QApplication.instance()
        if not app:
            return
        
        # 设置应用程序样式表
        stylesheet = self._generate_stylesheet()
        app.setStyleSheet(stylesheet)
        
        # 设置调色板
        palette = self._generate_palette()
        app.setPalette(palette)
    
    def _generate_stylesheet(self) -> str:
        """生成样式表"""
        if not self._current_theme:
            return ""
        
        colors = self._current_theme.colors
        typography = self._current_theme.typography
        spacing = self._current_theme.spacing
        border_radius = self._current_theme.border_radius
        
        return f"""
        /* 全局样式 */
        QWidget {{
            font-family: "{typography.font_family}";
            font-size: {typography.font_size_base}px;
            color: {colors.text_primary};
            background-color: {colors.background};
        }}
        
        /* 卡片样式 */
        .card {{
            background-color: {colors.card};
            border: 1px solid {colors.border};
            border-radius: {border_radius.base}px;
            padding: {spacing.base}px;
            margin: {spacing.sm}px;
        }}
        
        .card:hover {{
            box-shadow: 0 4px 8px {colors.shadow};
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {colors.primary};
            color: white;
            border: none;
            border-radius: {border_radius.sm}px;
            padding: {spacing.sm}px {spacing.base}px;
            font-weight: {typography.font_weight_medium};
            min-height: 32px;
        }}
        
        QPushButton:hover {{
            background-color: {colors.primary_dark};
        }}
        
        QPushButton:pressed {{
            background-color: {colors.primary_dark};
        }}
        
        QPushButton:disabled {{
            background-color: {colors.text_disabled};
            color: {colors.text_secondary};
        }}
        
        /* 次要按钮 */
        QPushButton.secondary {{
            background-color: {colors.surface};
            color: {colors.text_primary};
            border: 1px solid {colors.border};
        }}
        
        QPushButton.secondary:hover {{
            background-color: {colors.border};
        }}
        
        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {colors.surface};
            border: 1px solid {colors.border};
            border-radius: {border_radius.sm}px;
            padding: {spacing.sm}px;
            selection-background-color: {colors.primary_light};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors.primary};
            outline: none;
        }}
        
        /* 标签页样式 */
        QTabWidget::pane {{
            border: 1px solid {colors.border};
            background-color: {colors.card};
            border-radius: {border_radius.sm}px;
        }}
        
        QTabBar::tab {{
            background-color: {colors.surface};
            color: {colors.text_secondary};
            padding: {spacing.sm}px {spacing.base}px;
            margin-right: 2px;
            border-top-left-radius: {border_radius.sm}px;
            border-top-right-radius: {border_radius.sm}px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors.card};
            color: {colors.text_primary};
            border-bottom: 2px solid {colors.primary};
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors.border};
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {colors.surface};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors.border};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors.text_disabled};
        }}
        
        /* 进度条样式 */
        QProgressBar {{
            background-color: {colors.surface};
            border: 1px solid {colors.border};
            border-radius: {border_radius.sm}px;
            text-align: center;
            height: 20px;
        }}
        
        QProgressBar::chunk {{
            background-color: {colors.primary};
            border-radius: {border_radius.sm}px;
        }}
        
        /* 状态颜色 */
        .success {{
            color: {colors.success};
        }}
        
        .warning {{
            color: {colors.warning};
        }}
        
        .error {{
            color: {colors.error};
        }}
        
        .info {{
            color: {colors.info};
        }}
        
        /* 工具提示 */
        QToolTip {{
            background-color: {colors.card};
            color: {colors.text_primary};
            border: 1px solid {colors.border};
            border-radius: {border_radius.sm}px;
            padding: {spacing.sm}px;
        }}
        """
    
    def _generate_palette(self) -> QPalette:
        """生成调色板"""
        palette = QPalette()
        
        if not self._current_theme:
            return palette
        
        colors = self._current_theme.colors
        
        # 设置基本颜色
        palette.setColor(QPalette.ColorRole.Window, QColor(colors.background))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(colors.text_primary))
        palette.setColor(QPalette.ColorRole.Base, QColor(colors.surface))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(colors.card))
        palette.setColor(QPalette.ColorRole.Text, QColor(colors.text_primary))
        palette.setColor(QPalette.ColorRole.Button, QColor(colors.surface))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(colors.text_primary))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(colors.primary))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor("#FFFFFF"))
        
        return palette
    
    def save_theme(self, theme: Theme) -> bool:
        """保存主题"""
        try:
            theme_file = self.themes_dir / f"{theme.name}.json"
            theme_data = asdict(theme)
            
            # 转换枚举值
            theme_data['type'] = theme.type.value
            
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, indent=2, ensure_ascii=False)
            
            # 添加到主题列表
            self._themes[theme.name] = theme
            
            logger.info(f"主题已保存: {theme.name}")
            return True
            
        except Exception as e:
            logger.error(f"保存主题失败: {e}")
            return False
    
    def create_custom_theme(self, 
                           name: str,
                           base_theme: str = "light",
                           color_overrides: Optional[Dict[str, str]] = None) -> Optional[Theme]:
        """创建自定义主题"""
        if base_theme not in self._themes:
            logger.error(f"基础主题不存在: {base_theme}")
            return None
        
        base = self._themes[base_theme]
        
        # 复制基础主题
        colors_dict = asdict(base.colors)
        
        # 应用颜色覆盖
        if color_overrides:
            colors_dict.update(color_overrides)
        
        colors = ColorScheme(**colors_dict)
        
        custom_theme = Theme(
            name=name,
            type=base.type,
            colors=colors,
            typography=base.typography,
            spacing=base.spacing,
            border_radius=base.border_radius
        )
        
        return custom_theme
