#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI视频生成器 V2.0 - 专业完整版
作者: AI Assistant
版本: 2.0.0
功能: 完整的AI视频制作工具链
"""

import sys
import os
import json
import asyncio
import aiohttp
import requests
from datetime import datetime
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QTabWidget, QMessageBox,
    QProgressBar, QFileDialog, QComboBox, QSpinBox, QListWidget,
    QListWidgetItem, QScrollArea, QFrame, QGridLayout, QLineEdit,
    QSplitter, QGroupBox, QCheckBox, QSlider, QTextBrowser
)
from PyQt6.QtCore import Qt, QThr<PERSON>, pyqt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QSize
from PyQt6.QtGui import <PERSON>Font, QPixmap, QIcon, QPalette, QColor

# 全局样式表
GLOBAL_STYLE = """
QMainWindow {
    background-color: #f5f5f5;
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
}

QTabWidget::pane {
    border: 1px solid #d0d0d0;
    background-color: white;
    border-radius: 8px;
}

QTabBar::tab {
    background-color: #e8e8e8;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: bold;
    min-width: 100px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 3px solid #2196F3;
    color: #2196F3;
}

QTabBar::tab:hover {
    background-color: #f0f0f0;
}

QPushButton {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 11pt;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #1976D2;
}

QPushButton:pressed {
    background-color: #0D47A1;
}

QPushButton:disabled {
    background-color: #BDBDBD;
    color: #757575;
}

QTextEdit, QLineEdit {
    border: 2px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px;
    font-size: 11pt;
    background-color: white;
}

QTextEdit:focus, QLineEdit:focus {
    border-color: #2196F3;
}

QLabel {
    font-size: 11pt;
    color: #333333;
}

QGroupBox {
    font-weight: bold;
    border: 2px solid #E0E0E0;
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
    color: #2196F3;
    font-size: 12pt;
}

QProgressBar {
    border: 2px solid #E0E0E0;
    border-radius: 6px;
    text-align: center;
    font-weight: bold;
    height: 25px;
}

QProgressBar::chunk {
    background-color: #4CAF50;
    border-radius: 4px;
}

QListWidget {
    border: 2px solid #E0E0E0;
    border-radius: 6px;
    background-color: white;
    alternate-background-color: #F5F5F5;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #E0E0E0;
}

QListWidget::item:selected {
    background-color: #E3F2FD;
    color: #1976D2;
}

QComboBox {
    border: 2px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px;
    background-color: white;
    min-width: 100px;
}

QComboBox:focus {
    border-color: #2196F3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #666;
    margin-right: 5px;
}

QSpinBox {
    border: 2px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px;
    background-color: white;
    min-width: 80px;
}

QSpinBox:focus {
    border-color: #2196F3;
}

QStatusBar {
    background-color: #37474F;
    color: white;
    font-weight: bold;
    padding: 5px;
}
"""

class AIService:
    """AI服务管理类"""
    
    def __init__(self):
        self.api_key = "ed7ffe9976bb4484ab16647564c0cb27.rI1BXVjDDDURMtJY"
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/"
        self.session = None
    
    async def generate_text(self, prompt, model="glm-4", temperature=0.7):
        """生成文本内容"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": temperature,
                "max_tokens": 2000
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(
                f"{self.base_url}chat/completions",
                headers=headers,
                json=data,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    error_text = await response.text()
                    return f"API调用失败 ({response.status}): {error_text}"
                    
        except asyncio.TimeoutError:
            return "请求超时，请检查网络连接"
        except Exception as e:
            return f"生成失败: {str(e)}"
    
    async def generate_image(self, prompt, model="cogview-3"):
        """生成图像"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "prompt": prompt,
                "size": "1024x1024"
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(
                f"{self.base_url}images/generations",
                headers=headers,
                json=data,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result["data"][0]["url"]
                else:
                    error_text = await response.text()
                    return f"图像生成失败 ({response.status}): {error_text}"
                    
        except asyncio.TimeoutError:
            return "图像生成超时，请重试"
        except Exception as e:
            return f"图像生成失败: {str(e)}"
    
    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()

class WorkerThread(QThread):
    """后台工作线程"""
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    progress = pyqtSignal(str)
    
    def __init__(self, ai_service, task_type, **kwargs):
        super().__init__()
        self.ai_service = ai_service
        self.task_type = task_type
        self.kwargs = kwargs
    
    def run(self):
        """执行任务"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            if self.task_type == "story":
                self.progress.emit("正在创作故事...")
                result = loop.run_until_complete(
                    self.ai_service.generate_text(
                        f"请创作一个引人入胜的故事，主题：{self.kwargs['prompt']}。"
                        f"要求：情节完整，人物生动，适合制作成视频。字数控制在500-800字。"
                    )
                )
            elif self.task_type == "storyboard":
                self.progress.emit("正在生成分镜脚本...")
                result = loop.run_until_complete(
                    self.ai_service.generate_text(
                        f"请将以下故事改写为详细的分镜脚本，包含：\n"
                        f"1. 场景编号和标题\n"
                        f"2. 镜头描述（景别、角度、运动）\n"
                        f"3. 人物动作和表情\n"
                        f"4. 对话内容\n"
                        f"5. 音效提示\n\n"
                        f"故事内容：\n{self.kwargs['prompt']}"
                    )
                )
            elif self.task_type == "image":
                self.progress.emit("正在生成图像...")
                result = loop.run_until_complete(
                    self.ai_service.generate_image(self.kwargs['prompt'])
                )
            elif self.task_type == "scene_analysis":
                self.progress.emit("正在分析场景...")
                result = loop.run_until_complete(
                    self.ai_service.generate_text(
                        f"请分析以下分镜脚本，提取每个场景的关键信息：\n"
                        f"1. 场景标题\n"
                        f"2. 视觉描述（用于图像生成）\n"
                        f"3. 音频内容（对话和音效）\n"
                        f"4. 时长估计\n\n"
                        f"分镜脚本：\n{self.kwargs['prompt']}"
                    )
                )
            else:
                result = loop.run_until_complete(
                    self.ai_service.generate_text(self.kwargs['prompt'])
                )
            
            self.finished.emit(result)
            
        except Exception as e:
            self.error.emit(str(e))

class ProjectManager:
    """项目管理器"""
    
    def __init__(self):
        self.current_project = {
            "name": "",
            "created_at": "",
            "story": "",
            "storyboard": "",
            "scenes": [],
            "images": [],
            "audio": [],
            "settings": {
                "resolution": "1920x1080",
                "fps": 30,
                "duration": 60
            }
        }
        self.project_dir = Path("projects")
        self.project_dir.mkdir(exist_ok=True)
    
    def new_project(self, name):
        """创建新项目"""
        self.current_project = {
            "name": name,
            "created_at": datetime.now().isoformat(),
            "story": "",
            "storyboard": "",
            "scenes": [],
            "images": [],
            "audio": [],
            "settings": {
                "resolution": "1920x1080",
                "fps": 30,
                "duration": 60
            }
        }
        return True
    
    def save_project(self, file_path=None):
        """保存项目"""
        try:
            if not file_path:
                file_path = self.project_dir / f"{self.current_project['name']}.json"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_project, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            return False, str(e)
    
    def load_project(self, file_path):
        """加载项目"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.current_project = json.load(f)
            return True
        except Exception as e:
            return False, str(e)
    
    def get_project_info(self):
        """获取项目信息"""
        return {
            "name": self.current_project.get("name", "未命名项目"),
            "story_length": len(self.current_project.get("story", "")),
            "storyboard_length": len(self.current_project.get("storyboard", "")),
            "scenes_count": len(self.current_project.get("scenes", [])),
            "images_count": len(self.current_project.get("images", [])),
            "audio_count": len(self.current_project.get("audio", []))
        }

class AIVideoMaker(QMainWindow):
    """AI视频生成器主窗口"""

    def __init__(self):
        super().__init__()
        self.ai_service = AIService()
        self.project_manager = ProjectManager()
        self.worker = None
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AI视频生成器 V2.0 - 专业版")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # 设置应用图标和样式
        self.setStyleSheet(GLOBAL_STYLE)

        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 顶部工具栏
        toolbar = self.create_toolbar()
        main_layout.addWidget(toolbar)

        # 主要内容区域
        content_splitter = QSplitter(Qt.Orientation.Horizontal)

        # 左侧导航面板
        nav_panel = self.create_navigation_panel()
        content_splitter.addWidget(nav_panel)

        # 右侧主要工作区域
        self.main_tabs = QTabWidget()
        self.setup_main_tabs()
        content_splitter.addWidget(self.main_tabs)

        # 设置分割器比例
        content_splitter.setSizes([250, 1150])
        main_layout.addWidget(content_splitter)

        # 底部状态栏
        self.setup_status_bar()

        # 初始化项目
        self.project_manager.new_project("新项目")
        self.update_project_display()

    def create_toolbar(self):
        """创建顶部工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.Shape.StyledPanel)
        toolbar.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        layout = QHBoxLayout(toolbar)

        # 应用标题
        title_label = QLabel("🎬 AI视频生成器 V2.0")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2196F3; padding: 5px;")
        layout.addWidget(title_label)

        layout.addStretch()

        # 项目操作按钮
        self.new_project_btn = QPushButton("📁 新建项目")
        self.new_project_btn.setStyleSheet("background-color: #4CAF50;")
        layout.addWidget(self.new_project_btn)

        self.open_project_btn = QPushButton("📂 打开项目")
        self.open_project_btn.setStyleSheet("background-color: #FF9800;")
        layout.addWidget(self.open_project_btn)

        self.save_project_btn = QPushButton("💾 保存项目")
        self.save_project_btn.setStyleSheet("background-color: #9C27B0;")
        layout.addWidget(self.save_project_btn)

        return toolbar

    def create_navigation_panel(self):
        """创建左侧导航面板"""
        nav_panel = QFrame()
        nav_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        nav_panel.setStyleSheet("""
            QFrame {
                background-color: #37474F;
                border-radius: 8px;
            }
        """)
        nav_panel.setMaximumWidth(250)
        nav_panel.setMinimumWidth(200)

        layout = QVBoxLayout(nav_panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 导航标题
        nav_title = QLabel("功能导航")
        nav_title.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        nav_title.setStyleSheet("color: white; padding: 10px; text-align: center;")
        nav_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(nav_title)

        # 导航按钮
        nav_buttons = [
            ("📝 文本创作", 0, "#2196F3"),
            ("🎬 分镜设计", 1, "#F44336"),
            ("🎨 图像生成", 2, "#9C27B0"),
            ("🎤 语音制作", 3, "#FF9800"),
            ("🎥 视频合成", 4, "#4CAF50"),
            ("📊 项目管理", 5, "#607D8B"),
            ("⚙️ 系统设置", 6, "#795548")
        ]

        self.nav_buttons = []
        for text, index, color in nav_buttons:
            btn = QPushButton(text)
            btn.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 15px;
                    border-radius: 8px;
                    text-align: left;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}DD;
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background-color: {color}BB;
                }}
            """)
            btn.clicked.connect(lambda checked, i=index: self.switch_tab(i))
            layout.addWidget(btn)
            self.nav_buttons.append(btn)

        layout.addStretch()

        # 项目信息显示
        self.project_info_label = QLabel()
        self.project_info_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: #455A64;
                padding: 10px;
                border-radius: 6px;
                font-size: 10pt;
            }
        """)
        self.project_info_label.setWordWrap(True)
        layout.addWidget(self.project_info_label)

        return nav_panel

    def setup_main_tabs(self):
        """设置主要标签页"""
        # 隐藏标签栏，通过导航面板控制
        self.main_tabs.tabBar().setVisible(False)

        # 添加各个功能页面
        self.main_tabs.addTab(self.create_text_creation_page(), "文本创作")
        self.main_tabs.addTab(self.create_storyboard_page(), "分镜设计")
        self.main_tabs.addTab(self.create_image_generation_page(), "图像生成")
        self.main_tabs.addTab(self.create_voice_creation_page(), "语音制作")
        self.main_tabs.addTab(self.create_video_composition_page(), "视频合成")
        self.main_tabs.addTab(self.create_project_management_page(), "项目管理")
        self.main_tabs.addTab(self.create_settings_page(), "系统设置")

    def switch_tab(self, index):
        """切换标签页"""
        self.main_tabs.setCurrentIndex(index)
        self.update_nav_button_states(index)

    def update_nav_button_states(self, active_index):
        """更新导航按钮状态"""
        colors = ["#2196F3", "#F44336", "#9C27B0", "#FF9800", "#4CAF50", "#607D8B", "#795548"]

        for i, btn in enumerate(self.nav_buttons):
            color = colors[i]
            if i == active_index:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: 3px solid white;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: left;
                        font-weight: bold;
                    }}
                """)
            else:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        padding: 15px;
                        border-radius: 8px;
                        text-align: left;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}DD;
                    }}
                    QPushButton:pressed {{
                        background-color: {color}BB;
                    }}
                """)

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪 - AI视频生成器已启动")

        # 添加状态指示器
        self.api_status_label = QLabel("API: 已连接")
        self.api_status_label.setStyleSheet("color: #4CAF50; font-weight: bold; padding: 0 10px;")
        self.status_bar.addPermanentWidget(self.api_status_label)

        self.project_status_label = QLabel("项目: 新项目")
        self.project_status_label.setStyleSheet("color: #2196F3; font-weight: bold; padding: 0 10px;")
        self.status_bar.addPermanentWidget(self.project_status_label)

    def setup_connections(self):
        """设置信号连接"""
        self.new_project_btn.clicked.connect(self.new_project)
        self.open_project_btn.clicked.connect(self.open_project)
        self.save_project_btn.clicked.connect(self.save_project)

    def update_project_display(self):
        """更新项目显示信息"""
        info = self.project_manager.get_project_info()
        info_text = f"""
📁 项目: {info['name']}
📝 故事: {info['story_length']} 字符
🎬 分镜: {info['storyboard_length']} 字符
🎨 图像: {info['images_count']} 张
🎤 音频: {info['audio_count']} 个
🎥 场景: {info['scenes_count']} 个
        """.strip()
        self.project_info_label.setText(info_text)
        self.project_status_label.setText(f"项目: {info['name']}")

    def create_text_creation_page(self):
        """创建文本创作页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 页面标题
        title = QLabel("📝 文本创作")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title.setStyleSheet("color: #2196F3; padding: 10px; background-color: #E3F2FD; border-radius: 8px;")
        layout.addWidget(title)

        # 输入区域
        input_group = QGroupBox("故事主题输入")
        input_layout = QVBoxLayout(input_group)

        self.story_input = QTextEdit()
        self.story_input.setPlaceholderText("请输入故事主题或关键词，例如：一个关于时间旅行的科幻故事...")
        self.story_input.setMaximumHeight(100)
        input_layout.addWidget(self.story_input)

        # 控制按钮
        btn_layout = QHBoxLayout()
        self.generate_story_btn = QPushButton("🚀 生成故事")
        self.generate_story_btn.clicked.connect(self.generate_story)
        btn_layout.addWidget(self.generate_story_btn)

        self.save_story_btn = QPushButton("💾 保存故事")
        self.save_story_btn.clicked.connect(self.save_story)
        btn_layout.addWidget(self.save_story_btn)

        self.copy_to_storyboard_btn = QPushButton("📋 复制到分镜")
        self.copy_to_storyboard_btn.clicked.connect(self.copy_story_to_storyboard)
        btn_layout.addWidget(self.copy_to_storyboard_btn)

        btn_layout.addStretch()
        input_layout.addLayout(btn_layout)

        layout.addWidget(input_group)

        # 进度条
        self.story_progress = QProgressBar()
        self.story_progress.setVisible(False)
        layout.addWidget(self.story_progress)

        # 输出区域
        output_group = QGroupBox("生成的故事")
        output_layout = QVBoxLayout(output_group)

        self.story_output = QTextEdit()
        self.story_output.setPlaceholderText("生成的故事将在这里显示...")
        output_layout.addWidget(self.story_output)

        layout.addWidget(output_group)

        return page
