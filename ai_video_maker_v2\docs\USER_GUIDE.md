# 用户指南 📖

欢迎使用AI视频生成器V2.0！本指南将帮助您快速上手，从文本创作到视频生成的完整流程。

## 快速开始 🚀

### 第一次使用

1. **启动应用程序**
   ```bash
   python main.py
   ```

2. **创建新项目**
   - 点击"新建项目"按钮
   - 输入项目名称和描述
   - 选择保存位置

3. **输入故事文本**
   - 在文本创作页面输入您的故事
   - 或使用AI辅助创作功能

4. **生成分镜**
   - 切换到分镜设计页面
   - 点击"开始分镜生成"
   - 等待五阶段分析完成

5. **生成媒体资源**
   - 依次生成图像、语音、视频
   - 预览和调整效果

6. **导出成品**
   - 合成最终视频
   - 导出到指定位置

## 界面介绍 🖥️

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│  AI视频生成器 V2.0                                        │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│  🏠 仪表板   │                                           │
│  📝 文本创作 │            主内容区域                      │
│  🎬 分镜设计 │                                           │
│  🎨 图像生成 │                                           │
│  🎤 语音制作 │                                           │
│  🎥 视频合成 │                                           │
│  ⚙️ 设置     │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

### 导航栏功能

- **🏠 仪表板**: 项目概览和快速操作
- **📝 文本创作**: 故事文本编辑和AI辅助创作
- **🎬 分镜设计**: 五阶段智能分镜生成
- **🎨 图像生成**: AI图像生成和管理
- **🎤 语音制作**: AI语音合成和配音
- **🎥 视频合成**: 自动化视频制作
- **⚙️ 设置**: 应用程序配置和偏好

## 详细功能说明 📋

### 1. 项目管理

#### 创建项目
1. 点击"新建项目"
2. 填写项目信息：
   - **项目名称**: 简洁明了的名称
   - **描述**: 项目简介（可选）
   - **作者**: 创作者信息
   - **标签**: 便于分类管理

#### 打开项目
- 点击"打开项目"选择现有项目
- 或从仪表板的最近项目中选择

#### 项目设置
在项目设置中可以配置：
- 视频分辨率和帧率
- 音频质量设置
- 默认AI服务提供商
- 生成质量偏好

### 2. 文本创作

#### 手动输入
- 直接在文本编辑器中输入故事
- 支持富文本格式
- 实时字数统计

#### AI辅助创作
1. 选择创作类型：
   - 故事续写
   - 情节扩展
   - 对话生成
   - 场景描述

2. 输入创作提示
3. 选择AI模型和参数
4. 生成并编辑内容

#### 文本优化
- 语法检查
- 风格调整
- 结构优化
- 情节完善

### 3. 五阶段分镜生成

#### 阶段1: 世界观构建
- **功能**: 分析故事背景和设定
- **输出**: 
  - 时代背景
  - 地理环境
  - 社会文化
  - 视觉风格

#### 阶段2: 角色场景分析
- **功能**: 提取角色和场景信息
- **输出**:
  - 角色列表和特征描述
  - 场景列表和环境描述
  - 关系图谱

#### 阶段3: 情节结构分析
- **功能**: 分解故事结构
- **输出**:
  - 情节段落划分
  - 叙事功能分析
  - 情感基调标注

#### 阶段4: 分镜脚本生成
- **功能**: 生成详细分镜脚本
- **输出**:
  - 镜头列表
  - 技术参数
  - 视觉描述
  - 图像提示词

#### 阶段5: 优化和完善
- **功能**: 专业分镜优化
- **输出**:
  - 技术建议
  - 叙事优化
  - 制作建议

### 4. 图像生成

#### 生成设置
- **引擎选择**: CogView, DALL-E, Stable Diffusion等
- **图像尺寸**: 1024x1024, 1920x1080等
- **生成质量**: 低、中、高
- **并发任务数**: 1-5个

#### 批量生成
1. 选择要生成的镜头
2. 配置生成参数
3. 启动批量生成
4. 监控进度和结果

#### 图像管理
- 预览和筛选
- 重新生成
- 手动上传替换
- 导出高清版本

### 5. 语音制作

#### 语音配置
- **语音引擎**: Azure TTS, ElevenLabs等
- **音色选择**: 男声、女声、儿童声等
- **语言设置**: 中文、英文等
- **语音参数**: 语速、音调、音量

#### 角色配音
1. 为每个角色分配音色
2. 调整语音参数
3. 预览语音效果
4. 批量生成音频

#### 音频处理
- 音频剪辑
- 音效添加
- 音量调节
- 格式转换

### 6. 视频合成

#### 合成设置
- **视频引擎**: CogVideoX, RunwayML等
- **分辨率**: 1920x1080, 1280x720等
- **帧率**: 24fps, 30fps, 60fps
- **时长**: 5秒、10秒等

#### 自动合成
1. 检查素材完整性
2. 配置合成参数
3. 启动自动合成
4. 预览合成结果

#### 手动调整
- 镜头时长调整
- 转场效果添加
- 音画同步调整
- 特效处理

## 高级功能 🔧

### 一致性控制

#### 角色一致性
- 角色外观描述标准化
- 参考图像管理
- 风格关键词统一
- 自动一致性检查

#### 场景一致性
- 场景环境描述规范
- 光线氛围统一
- 色彩风格保持
- 空间关系维护

### 批量处理

#### 批量图像生成
- 选择多个镜头
- 统一生成参数
- 并发处理
- 进度监控

#### 批量语音合成
- 角色语音批量配置
- 文本批量处理
- 音频格式统一
- 质量检查

### 模板系统

#### 项目模板
- 创建项目模板
- 保存常用设置
- 快速项目初始化
- 模板分享

#### 风格模板
- 视觉风格预设
- 分镜风格模板
- 音频风格配置
- 一键应用

### 插件扩展

#### 自定义插件
- 插件开发接口
- 第三方服务集成
- 自定义工作流
- 功能扩展

## 最佳实践 💡

### 文本创作建议

1. **结构清晰**: 确保故事有明确的开始、发展、高潮、结局
2. **细节丰富**: 提供足够的环境、角色、动作描述
3. **对话自然**: 角色对话要符合人物性格
4. **节奏把控**: 注意故事节奏的快慢变化

### 分镜优化技巧

1. **镜头多样性**: 合理搭配远景、中景、特写
2. **运动设计**: 适当使用摄像机运动增强表现力
3. **时长控制**: 根据内容重要性调整镜头时长
4. **情绪表达**: 通过镜头语言传达情感

### 图像生成优化

1. **提示词优化**: 使用具体、详细的描述
2. **风格统一**: 保持整体视觉风格一致
3. **质量检查**: 及时检查和替换不满意的图像
4. **参考管理**: 建立角色和场景参考库

### 语音制作建议

1. **角色区分**: 为不同角色选择合适的音色
2. **情感表达**: 根据情节调整语音参数
3. **节奏控制**: 注意语音的停顿和重音
4. **音质保证**: 确保音频清晰无杂音

## 故障排除 🔧

### 常见问题

#### 生成失败
- 检查API密钥是否有效
- 确认网络连接正常
- 查看错误日志信息
- 尝试重新生成

#### 质量不佳
- 优化提示词描述
- 调整生成参数
- 更换AI引擎
- 手动替换素材

#### 性能问题
- 降低并发任务数
- 减少缓存大小
- 关闭不必要的功能
- 升级硬件配置

### 获取帮助

1. **查看日志**: 检查详细错误信息
2. **重启应用**: 解决临时性问题
3. **重置配置**: 恢复默认设置
4. **联系支持**: 获取技术支持

## 快捷键 ⌨️

### 全局快捷键
- `Ctrl+N`: 新建项目
- `Ctrl+O`: 打开项目
- `Ctrl+S`: 保存项目
- `Ctrl+Z`: 撤销操作
- `Ctrl+Y`: 重做操作
- `F5`: 刷新界面
- `F11`: 全屏模式

### 页面快捷键
- `Ctrl+1-7`: 切换到对应页面
- `Ctrl+G`: 开始生成
- `Ctrl+P`: 预览结果
- `Ctrl+E`: 导出文件

---

**希望这份指南能帮助您快速掌握AI视频生成器V2.0！** 🎉

如有疑问，请查看 [FAQ](FAQ.md) 或联系我们的技术支持。
