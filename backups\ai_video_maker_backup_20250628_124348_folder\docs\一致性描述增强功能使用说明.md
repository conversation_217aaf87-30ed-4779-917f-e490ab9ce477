# 一致性描述增强功能使用说明

## 功能概述

本功能实现了在分镜脚本图像生成界面中，自动从`original_descriptions_with_consistency_*.json`文件加载镜头描述内容，并提供智能增强功能，可以使用LLM对描述进行优化，同时将增强结果同步更新回JSON文件。

## 主要特性

### 1. 自动加载一致性描述文件
- 系统会自动查找项目`texts`目录下的`original_descriptions_with_consistency_*.json`文件
- 优先加载最新的文件（按修改时间排序）
- 将JSON文件中的`content`字段内容填入一致性描述界面

### 2. 智能增强功能
- 点击"智能增强"按钮可调用LLM对当前选中镜头的描述进行增强
- 增强过程会显示进度对话框
- 增强结果会自动填入"增强描述"文本框
- 支持角色一致性和技术细节的智能融合

### 3. 同步更新JSON文件
- 增强完成后，系统会自动将新的描述内容更新到原始JSON文件
- 更新对应场景和镜头的`content`字段
- 保持文件结构完整，只更新指定内容

## 使用步骤

### 步骤1：打开项目
1. 确保已创建或打开包含分镜数据的项目
2. 项目中应包含`output/项目名/texts/original_descriptions_with_consistency_*.json`文件

### 步骤2：进入分镜图像生成界面
1. 切换到"🖼️ 分镜图像生成"标签页
2. 系统会自动加载一致性描述文件中的内容
3. 在表格中可以看到各个镜头的一致性描述

### 步骤3：选择要增强的镜头
1. 在分镜列表中点击选择要增强的镜头
2. 右侧详细面板会显示该镜头的一致性描述内容
3. 切换到"描述编辑"标签页

### 步骤4：执行智能增强
1. 在"一致性描述"区域查看当前内容
2. 点击"智能增强"按钮
3. 等待增强完成（会显示进度对话框）
4. 查看"增强描述"区域的结果

### 步骤5：应用增强结果
1. 如果满意增强结果，可以点击"保存到一致性"按钮
2. 系统会自动将增强内容同步到JSON文件
3. 表格中的对应行也会更新显示

## 技术细节

### 文件结构支持
系统支持以下JSON文件结构：
```json
{
  "project_name": "项目名称",
  "total_scenes": 5,
  "total_shots": 25,
  "scenes": [
    {
      "scene_name": "场景1：团结的觉醒",
      "scene_index": 1,
      "shots_count": 5,
      "shots": [
        {
          "shot_number": 1,
          "content": "镜头描述内容..."
        }
      ]
    }
  ]
}
```

### 增强算法
- 使用场景描述增强器(`SceneDescriptionEnhancer`)
- 支持技术细节分析和一致性信息注入
- 可选择使用LLM进行智能融合
- 自动检测角色和场景信息

### 错误处理
- 如果没有找到一致性描述文件，会回退到使用项目数据
- 如果LLM增强失败，会显示错误信息
- 文件更新失败不会影响界面操作

## 注意事项

1. **项目要求**：必须先打开包含分镜数据的项目
2. **文件权限**：确保JSON文件有写入权限
3. **LLM配置**：如需使用LLM增强，请确保已正确配置LLM API
4. **备份建议**：建议在批量增强前备份原始JSON文件

## 故障排除

### 问题1：无法加载一致性描述文件
**解决方案**：
- 检查项目`texts`目录下是否存在`original_descriptions_with_consistency_*.json`文件
- 确认文件格式正确，包含必要的`scenes`字段

### 问题2：智能增强失败
**解决方案**：
- 检查项目根目录是否正确设置
- 确认LLM API配置是否正常
- 查看错误日志获取详细信息

### 问题3：JSON文件更新失败
**解决方案**：
- 检查文件是否被其他程序占用
- 确认文件权限允许写入
- 验证JSON文件结构完整性

## 示例效果

**原始描述**：
```
主要角色紧握拳头，眼神坚定地望向镜头，背景是地下指挥中心的全景。
```

**增强后描述**：
```
主要角色（一个中等偏高的男性，深深灰色短发，深邃的深灰色眼睛，穿着深灰色的现代军事风格服装，带有战术背心和多功能手表，表现出勇敢、智慧和坚定的表情）紧握拳头，眼神坚定地望向镜头，背景是地下指挥中心的全景，远景镜头，中心构图，电影感，超写实，4K，胶片颗粒，景深。
```

## 更新日志

- **2025-06-17**：初始版本发布
  - 实现一致性描述文件自动加载
  - 添加智能增强功能
  - 支持JSON文件同步更新
