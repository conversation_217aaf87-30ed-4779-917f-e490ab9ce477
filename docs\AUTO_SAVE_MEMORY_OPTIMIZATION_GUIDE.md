# 自动保存和内存优化功能使用指南

## 概述

本指南介绍了AI视频助手中新增的自动保存和内存优化功能，这些功能旨在提升用户体验，防止数据丢失，并优化应用程序的内存使用。

## 功能特性

### 1. 自动保存功能

#### 核心特性
- **实时自动保存**：文本内容变化时自动触发保存
- **多数据源支持**：支持原始文本、改写文本、项目数据等多种数据类型
- **优先级管理**：支持设置保存优先级，重要数据优先保存
- **备份机制**：自动创建备份文件，防止数据损坏
- **异步保存**：后台异步执行，不影响用户操作

#### 自动保存触发条件
- 用户输入文本内容变化
- AI生成改写文本完成
- 项目配置修改
- 定时自动保存（默认每30秒）

#### 保存位置
- 文本内容：`auto_save/text_content.json`
- 项目数据：`auto_save/project_data.json`
- 备份文件：`auto_save/backups/`

### 2. 内存优化功能

#### 内存监控
- **实时监控**：每10秒检查一次系统内存使用情况
- **智能警告**：内存使用率超过70%时发出警告
- **严重警告**：内存使用率超过85%时发出严重警告
- **自动清理**：内存不足时自动清理缓存

#### 图像缓存管理
- **智能缓存**：自动缓存常用图像，提升加载速度
- **内存限制**：默认最大缓存100MB，可配置
- **LRU策略**：最近最少使用的图像优先清理
- **线程安全**：支持多线程并发访问

## 使用方法

### 自动保存

自动保存功能在应用启动时自动激活，无需手动配置。

#### 手动触发保存
```python
# 在主窗口中标记内容已修改
self.mark_content_dirty()

# 立即执行保存
self.auto_save_manager.save_immediately()
```

#### 自定义保存回调
```python
# 注册自定义保存回调
def get_custom_data():
    return {"custom": "data"}

auto_save_manager.register_save_callback(
    'custom_data',
    get_custom_data,
    'path/to/save/file.json',
    priority=1
)
```

### 内存优化

内存优化功能自动运行，但也提供手动控制接口。

#### 手动清理缓存
```python
# 清理图像缓存
self.image_memory_manager.clear_cache()

# 获取缓存统计
stats = self.image_memory_manager.get_cache_stats()
print(f"缓存统计: {stats}")
```

#### 配置内存监控
```python
# 自定义内存监控阈值
memory_monitor = MemoryMonitor(
    warning_threshold=0.6,  # 60%时警告
    critical_threshold=0.8  # 80%时严重警告
)
```

## 配置选项

### 自动保存配置

```python
# 在AutoSaveManager初始化时配置
auto_save_manager = AutoSaveManager(
    save_interval=30,  # 保存间隔（秒）
    max_backups=5,     # 最大备份数量
    backup_interval=300  # 备份间隔（秒）
)
```

### 内存优化配置

```python
# 图像缓存配置
image_manager = ImageMemoryManager(
    max_cache_size_mb=200,  # 最大缓存大小（MB）
    cleanup_threshold=0.8   # 清理阈值
)

# 内存监控配置
memory_monitor = MemoryMonitor(
    warning_threshold=0.7,   # 警告阈值
    critical_threshold=0.85  # 严重警告阈值
)
```

## 状态指示

### 自动保存状态
- **保存完成**：状态栏显示"自动保存完成"（2秒后消失）
- **保存失败**：状态栏显示错误信息
- **保存进行中**：后台静默执行

### 内存状态
- **正常**：状态栏显示"就绪"
- **警告**：状态栏显示内存使用率，文字为橙色
- **严重**：状态栏显示内存不足，文字为红色，弹出警告对话框

## 故障排除

### 自动保存问题

#### 保存失败
1. **检查磁盘空间**：确保有足够的磁盘空间
2. **检查权限**：确保应用有写入权限
3. **查看日志**：检查`logs/system.log`中的错误信息

#### 保存频率过高
1. **调整保存间隔**：增加`save_interval`参数
2. **优化数据获取**：确保数据获取函数高效

### 内存优化问题

#### 内存使用率持续过高
1. **手动清理**：执行`clear_cache()`清理缓存
2. **重启应用**：完全释放内存
3. **减少缓存大小**：降低`max_cache_size_mb`参数

#### 内存监控不准确
1. **检查psutil**：确保psutil库正确安装
2. **权限问题**：某些系统可能需要管理员权限

## 性能影响

### 自动保存
- **CPU影响**：极低，异步执行
- **磁盘影响**：轻微，仅在数据变化时写入
- **内存影响**：极低，数据序列化时短暂增加

### 内存优化
- **CPU影响**：极低，定期检查
- **内存影响**：正面，有效减少内存使用
- **响应速度**：正面，缓存提升图像加载速度

## 最佳实践

### 自动保存
1. **合理设置保存间隔**：平衡数据安全和性能
2. **定期清理备份**：避免备份文件过多占用空间
3. **监控保存状态**：关注保存失败的警告

### 内存优化
1. **适当的缓存大小**：根据系统内存设置合理的缓存大小
2. **及时响应警告**：内存警告时及时保存工作
3. **定期重启**：长时间使用后重启应用释放内存

## 技术细节

### 自动保存实现
- **线程安全**：使用线程锁保护共享数据
- **异常处理**：完善的错误处理和恢复机制
- **数据完整性**：原子写入，防止数据损坏

### 内存优化实现
- **LRU算法**：最近最少使用的缓存清理策略
- **弱引用**：避免循环引用导致的内存泄漏
- **系统集成**：与操作系统内存管理协同工作

## 更新日志

### v1.0.0 (2024-12-27)
- 初始版本发布
- 实现基础自动保存功能
- 实现内存监控和图像缓存管理
- 集成到主窗口界面

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看日志文件：`logs/system.log`
2. 检查配置文件：`config/app_settings.json`
3. 提交问题报告，包含详细的错误信息和复现步骤

---

*本文档将随功能更新持续维护，请关注最新版本。*