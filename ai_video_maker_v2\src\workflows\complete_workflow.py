# -*- coding: utf-8 -*-
"""
完整工作流 - 从文本到视频的完整制作流程

提供端到端的视频制作工作流：
1. 文本处理和优化
2. 五阶段分镜生成
3. 图像生成
4. 语音合成
5. 视频生成
6. 最终合成
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from pathlib import Path
import json

from ..core.workflow_manager import Workflow, WorkflowStep
from ..core.service_manager import ServiceType
from ..models.project import Project
from ..models.storyboard import Storyboard
from ..core.video_composer import VideoComposer, CompositionSettings
from .storyboard_workflow import StoryboardProcessor

logger = logging.getLogger(__name__)

class CompleteWorkflowProcessor:
    """完整工作流处理器"""
    
    def __init__(self, service_manager, project: Project):
        self.service_manager = service_manager
        self.project = project
        self.storyboard_processor = StoryboardProcessor(service_manager)
        
    async def execute_complete_workflow(self, 
                                      text: str,
                                      style: str = "电影风格",
                                      progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """执行完整的制作工作流"""
        try:
            logger.info("开始执行完整视频制作工作流")
            
            # 阶段1: 文本处理和分镜生成
            if progress_callback:
                progress_callback(0.1, "正在生成分镜脚本...")
            
            storyboard_result = await self._generate_storyboard(text, style)
            if not storyboard_result["success"]:
                return storyboard_result
            
            storyboard = storyboard_result["storyboard"]
            
            # 阶段2: 图像生成
            if progress_callback:
                progress_callback(0.3, "正在生成图像...")
            
            image_result = await self._generate_images(storyboard, progress_callback)
            if not image_result["success"]:
                return image_result
            
            # 阶段3: 语音合成
            if progress_callback:
                progress_callback(0.6, "正在合成语音...")
            
            voice_result = await self._generate_voices(storyboard, progress_callback)
            if not voice_result["success"]:
                return voice_result
            
            # 阶段4: 视频生成（可选）
            if progress_callback:
                progress_callback(0.8, "正在生成视频片段...")
            
            video_result = await self._generate_videos(storyboard, progress_callback)
            # 视频生成失败不影响整体流程，可以用静态图像代替
            
            # 阶段5: 最终合成
            if progress_callback:
                progress_callback(0.9, "正在合成最终视频...")
            
            composition_result = await self._compose_final_video(storyboard)
            if not composition_result["success"]:
                return composition_result
            
            # 保存项目数据
            self.project.set_storyboard_data(storyboard.to_dict())
            self.project.save()
            
            if progress_callback:
                progress_callback(1.0, "制作完成！")
            
            logger.info("完整视频制作工作流执行完成")
            
            return {
                "success": True,
                "storyboard": storyboard,
                "final_video": composition_result["output_path"],
                "statistics": {
                    "shots_count": len(storyboard.shots),
                    "images_generated": image_result.get("generated_count", 0),
                    "voices_generated": voice_result.get("generated_count", 0),
                    "videos_generated": video_result.get("generated_count", 0) if video_result["success"] else 0,
                    "total_duration": storyboard.total_duration
                }
            }
            
        except Exception as e:
            logger.error(f"完整工作流执行失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _generate_storyboard(self, text: str, style: str) -> Dict[str, Any]:
        """生成分镜脚本"""
        try:
            # 执行五阶段分镜生成
            stage_results = {}
            
            # 阶段1: 世界观构建
            stage1_result = await self.storyboard_processor.process_stage_1_world_building(text, style)
            if not stage1_result.success:
                return {"success": False, "error": f"世界观构建失败: {stage1_result.error}"}
            stage_results["stage_1"] = stage1_result.data
            
            # 阶段2: 角色场景分析
            stage2_result = await self.storyboard_processor.process_stage_2_character_scene_analysis(
                text, stage1_result.data["world_setting"]
            )
            if not stage2_result.success:
                return {"success": False, "error": f"角色场景分析失败: {stage2_result.error}"}
            stage_results["stage_2"] = stage2_result.data
            
            # 阶段3: 情节结构分析
            stage3_result = await self.storyboard_processor.process_stage_3_plot_structure(
                text, stage2_result.data["characters"], stage2_result.data["scenes"]
            )
            if not stage3_result.success:
                return {"success": False, "error": f"情节结构分析失败: {stage3_result.error}"}
            stage_results["stage_3"] = stage3_result.data
            
            # 阶段4: 分镜脚本生成
            stage4_result = await self.storyboard_processor.process_stage_4_shot_generation(
                stage3_result.data["plot_segments"],
                stage2_result.data["characters"],
                stage2_result.data["scenes"],
                style
            )
            if not stage4_result.success:
                return {"success": False, "error": f"分镜脚本生成失败: {stage4_result.error}"}
            stage_results["stage_4"] = stage4_result.data
            
            # 阶段5: 优化和完善
            stage5_result = await self.storyboard_processor.process_stage_5_optimization(
                stage4_result.data["shots"], style
            )
            if not stage5_result.success:
                return {"success": False, "error": f"分镜优化失败: {stage5_result.error}"}
            stage_results["stage_5"] = stage5_result.data
            
            # 创建分镜板对象
            storyboard = await self.storyboard_processor.create_storyboard_from_results(
                text, stage_results, style
            )
            
            return {
                "success": True,
                "storyboard": storyboard,
                "stage_results": stage_results
            }
            
        except Exception as e:
            logger.error(f"分镜生成失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_images(self, storyboard: Storyboard, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成图像"""
        try:
            generated_count = 0
            total_shots = len(storyboard.shots)
            
            # 准备图像生成任务
            tasks = []
            for i, shot in enumerate(storyboard.shots):
                if shot.image_prompt:
                    task = self._generate_single_image(shot, i)
                    tasks.append(task)
            
            # 并发执行图像生成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.warning(f"镜头 {i} 图像生成失败: {result}")
                elif result:
                    generated_count += 1
                    if progress_callback:
                        progress = 0.3 + (generated_count / total_shots) * 0.3
                        progress_callback(progress, f"已生成 {generated_count}/{total_shots} 张图像")
            
            return {
                "success": True,
                "generated_count": generated_count,
                "total_count": total_shots
            }
            
        except Exception as e:
            logger.error(f"图像生成失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_single_image(self, shot, index: int) -> bool:
        """生成单张图像"""
        try:
            # 调用图像生成服务
            result = await self.service_manager.call_service(
                ServiceType.IMAGE,
                "generate_image",
                prompt=shot.image_prompt,
                output_dir=str(self.project.get_asset_path("images", "")),
                filename=f"shot_{shot.sequence_number:04d}.png",
                size="1024x1024"
            )
            
            if result.success:
                # 更新镜头的图像路径
                shot.generated_image = f"shot_{shot.sequence_number:04d}.png"
                return True
            else:
                logger.warning(f"镜头 {index} 图像生成失败: {result.error}")
                return False
                
        except Exception as e:
            logger.error(f"镜头 {index} 图像生成异常: {e}")
            return False
    
    async def _generate_voices(self, storyboard: Storyboard, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成语音"""
        try:
            generated_count = 0
            total_shots = len([shot for shot in storyboard.shots if shot.dialogue or shot.voice_over])
            
            for i, shot in enumerate(storyboard.shots):
                text_to_speak = shot.dialogue or shot.voice_over
                if not text_to_speak:
                    continue
                
                try:
                    # 调用语音合成服务
                    result = await self.service_manager.call_service(
                        ServiceType.VOICE,
                        "synthesize_speech",
                        text=text_to_speak,
                        output_dir=str(self.project.get_asset_path("audio", "")),
                        filename=f"shot_{shot.sequence_number:04d}.wav",
                        voice_id="zh-CN-XiaoxiaoNeural"
                    )
                    
                    if result.success:
                        shot.generated_audio = f"shot_{shot.sequence_number:04d}.wav"
                        generated_count += 1
                        
                        if progress_callback:
                            progress = 0.6 + (generated_count / total_shots) * 0.2
                            progress_callback(progress, f"已生成 {generated_count}/{total_shots} 个语音")
                    else:
                        logger.warning(f"镜头 {i} 语音生成失败: {result.error}")
                        
                except Exception as e:
                    logger.error(f"镜头 {i} 语音生成异常: {e}")
            
            return {
                "success": True,
                "generated_count": generated_count,
                "total_count": total_shots
            }
            
        except Exception as e:
            logger.error(f"语音生成失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_videos(self, storyboard: Storyboard, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """生成视频片段（可选）"""
        try:
            generated_count = 0
            total_shots = len([shot for shot in storyboard.shots if shot.generated_image])
            
            # 视频生成是可选的，失败不影响整体流程
            for i, shot in enumerate(storyboard.shots):
                if not shot.generated_image:
                    continue
                
                try:
                    image_path = self.project.get_asset_path("images", shot.generated_image)
                    if not image_path.exists():
                        continue
                    
                    # 调用视频生成服务
                    result = await self.service_manager.call_service(
                        ServiceType.VIDEO,
                        "generate_video",
                        image_path=str(image_path),
                        prompt=shot.visual_description,
                        duration=shot.duration,
                        output_dir=str(self.project.get_asset_path("video", "")),
                        filename=f"shot_{shot.sequence_number:04d}.mp4"
                    )
                    
                    if result.success:
                        shot.generated_video = f"shot_{shot.sequence_number:04d}.mp4"
                        generated_count += 1
                        
                        if progress_callback:
                            progress = 0.8 + (generated_count / total_shots) * 0.1
                            progress_callback(progress, f"已生成 {generated_count}/{total_shots} 个视频片段")
                    
                except Exception as e:
                    logger.warning(f"镜头 {i} 视频生成失败: {e}")
            
            return {
                "success": True,
                "generated_count": generated_count,
                "total_count": total_shots
            }
            
        except Exception as e:
            logger.warning(f"视频生成失败（将使用静态图像）: {e}")
            return {"success": False, "error": str(e)}
    
    async def _compose_final_video(self, storyboard: Storyboard) -> Dict[str, Any]:
        """合成最终视频"""
        try:
            # 创建视频合成器
            composer = VideoComposer(self.project)
            
            # 设置输出路径
            output_path = self.project.get_asset_path("exports", f"{self.project.metadata.name}_final.mp4")
            
            # 创建合成设置
            settings = CompositionSettings(
                output_width=1920,
                output_height=1080,
                fps=30.0,
                enable_transitions=True,
                enable_audio_crossfade=True
            )
            
            # 执行合成
            result = await composer.compose_video(storyboard, str(output_path), settings)
            
            return result
            
        except Exception as e:
            logger.error(f"视频合成失败: {e}")
            return {"success": False, "error": str(e)}

def create_complete_workflow() -> Workflow:
    """创建完整制作工作流"""
    workflow = Workflow(
        workflow_id="complete_video_production",
        name="完整视频制作",
        description="从文本到视频的完整制作流程"
    )
    
    # 这里可以定义工作流步骤，但由于是复杂的异步流程，
    # 我们使用 CompleteWorkflowProcessor 来处理
    
    return workflow
