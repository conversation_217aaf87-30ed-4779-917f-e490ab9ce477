# -*- coding: utf-8 -*-
"""
视频合成器 - 将分镜素材合成为完整视频

提供完整的视频合成功能：
- 镜头视频拼接
- 音频同步
- 字幕添加
- 转场效果
- 特效处理
- 输出优化
"""

import asyncio
import logging
import subprocess
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union
import json
import tempfile
import shutil

from ..models.storyboard import Storyboard, Shot
from ..models.media import VideoAsset, AudioAsset, ImageAsset
from ..models.project import Project

logger = logging.getLogger(__name__)

@dataclass
class SubtitleSegment:
    """字幕片段"""
    start_time: float  # 开始时间（秒）
    end_time: float    # 结束时间（秒）
    text: str          # 字幕文本
    speaker: str = ""  # 说话人
    style: Dict[str, Any] = field(default_factory=dict)  # 字幕样式

@dataclass
class TransitionEffect:
    """转场效果"""
    type: str          # 转场类型：fade, dissolve, wipe, slide等
    duration: float    # 转场时长（秒）
    parameters: Dict[str, Any] = field(default_factory=dict)  # 转场参数

@dataclass
class VideoSegment:
    """视频片段"""
    shot_id: str
    video_path: str
    audio_path: Optional[str] = None
    start_time: float = 0.0
    duration: float = 0.0
    subtitles: List[SubtitleSegment] = field(default_factory=list)
    transition: Optional[TransitionEffect] = None

@dataclass
class CompositionSettings:
    """合成设置"""
    # 视频设置
    output_width: int = 1920
    output_height: int = 1080
    fps: float = 30.0
    video_codec: str = "libx264"
    video_bitrate: str = "5000k"
    
    # 音频设置
    audio_codec: str = "aac"
    audio_bitrate: str = "192k"
    audio_sample_rate: int = 44100
    
    # 字幕设置
    subtitle_font: str = "Arial"
    subtitle_size: int = 24
    subtitle_color: str = "white"
    subtitle_outline_color: str = "black"
    subtitle_outline_width: int = 2
    subtitle_position: str = "bottom"  # bottom, top, center
    
    # 输出设置
    output_format: str = "mp4"
    quality_preset: str = "medium"  # ultrafast, fast, medium, slow, veryslow
    
    # 特效设置
    enable_transitions: bool = True
    default_transition_duration: float = 0.5
    enable_audio_crossfade: bool = True
    audio_crossfade_duration: float = 0.2

class VideoComposer:
    """视频合成器"""
    
    def __init__(self, project: Project):
        self.project = project
        self.temp_dir: Optional[Path] = None
        self.ffmpeg_path = self._find_ffmpeg()
        
    def _find_ffmpeg(self) -> str:
        """查找FFmpeg可执行文件"""
        try:
            # 尝试在PATH中查找
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return 'ffmpeg'
        except FileNotFoundError:
            pass
        
        # 尝试常见路径
        common_paths = [
            'ffmpeg.exe',
            'bin/ffmpeg.exe',
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg'
        ]
        
        for path in common_paths:
            try:
                result = subprocess.run([path, '-version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    return path
            except FileNotFoundError:
                continue
        
        raise RuntimeError("未找到FFmpeg，请确保已安装FFmpeg并添加到PATH")
    
    async def compose_video(self, 
                           storyboard: Storyboard,
                           output_path: str,
                           settings: CompositionSettings = None,
                           progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """合成完整视频"""
        try:
            if not settings:
                settings = CompositionSettings()
            
            # 创建临时目录
            self.temp_dir = Path(tempfile.mkdtemp(prefix="video_compose_"))
            
            logger.info(f"开始视频合成，输出路径: {output_path}")
            
            # 1. 准备视频片段
            if progress_callback:
                progress_callback(0.1, "准备视频片段...")
            
            segments = await self._prepare_video_segments(storyboard, settings)
            
            # 2. 生成字幕文件
            if progress_callback:
                progress_callback(0.2, "生成字幕文件...")
            
            subtitle_file = await self._generate_subtitles(segments, settings)
            
            # 3. 合成视频
            if progress_callback:
                progress_callback(0.3, "开始视频合成...")
            
            final_video = await self._compose_final_video(segments, settings, progress_callback)
            
            # 4. 添加字幕
            if subtitle_file and progress_callback:
                progress_callback(0.8, "添加字幕...")
                final_video = await self._add_subtitles(final_video, subtitle_file, settings)
            
            # 5. 输出最终视频
            if progress_callback:
                progress_callback(0.9, "输出最终视频...")
            
            await self._finalize_output(final_video, output_path, settings)
            
            # 6. 清理临时文件
            if progress_callback:
                progress_callback(1.0, "清理临时文件...")
            
            await self._cleanup()
            
            # 获取输出视频信息
            video_info = await self._get_video_info(output_path)
            
            logger.info(f"视频合成完成: {output_path}")
            
            return {
                "success": True,
                "output_path": output_path,
                "video_info": video_info,
                "segments_count": len(segments),
                "total_duration": sum(seg.duration for seg in segments)
            }
            
        except Exception as e:
            logger.error(f"视频合成失败: {e}")
            await self._cleanup()
            
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _prepare_video_segments(self, storyboard: Storyboard, settings: CompositionSettings) -> List[VideoSegment]:
        """准备视频片段"""
        segments = []
        current_time = 0.0
        
        for shot in storyboard.shots:
            # 获取镜头的媒体资源
            video_path = self._get_shot_video_path(shot)
            audio_path = self._get_shot_audio_path(shot)
            
            if not video_path or not Path(video_path).exists():
                logger.warning(f"镜头 {shot.shot_id} 缺少视频文件，跳过")
                continue
            
            # 获取视频时长
            duration = await self._get_video_duration(video_path)
            if duration <= 0:
                duration = shot.duration
            
            # 生成字幕
            subtitles = self._generate_shot_subtitles(shot, current_time, duration)
            
            # 生成转场效果
            transition = None
            if settings.enable_transitions and len(segments) > 0:
                transition = TransitionEffect(
                    type="fade",
                    duration=settings.default_transition_duration
                )
            
            segment = VideoSegment(
                shot_id=shot.shot_id,
                video_path=video_path,
                audio_path=audio_path,
                start_time=current_time,
                duration=duration,
                subtitles=subtitles,
                transition=transition
            )
            
            segments.append(segment)
            current_time += duration
        
        return segments
    
    def _get_shot_video_path(self, shot: Shot) -> Optional[str]:
        """获取镜头视频路径"""
        if shot.generated_video:
            video_path = self.project.get_asset_path("video", shot.generated_video)
            if video_path.exists():
                return str(video_path)
        
        # 如果没有视频，尝试从图像生成静态视频
        if shot.generated_image:
            image_path = self.project.get_asset_path("images", shot.generated_image)
            if image_path.exists():
                return self._create_static_video_from_image(image_path, shot.duration)
        
        return None
    
    def _get_shot_audio_path(self, shot: Shot) -> Optional[str]:
        """获取镜头音频路径"""
        if shot.generated_audio:
            audio_path = self.project.get_asset_path("audio", shot.generated_audio)
            if audio_path.exists():
                return str(audio_path)
        
        return None
    
    def _create_static_video_from_image(self, image_path: Path, duration: float) -> str:
        """从图像创建静态视频"""
        if not self.temp_dir:
            raise RuntimeError("临时目录未初始化")
        
        output_path = self.temp_dir / f"static_{image_path.stem}.mp4"
        
        # 使用FFmpeg从图像创建视频
        cmd = [
            self.ffmpeg_path,
            '-loop', '1',
            '-i', str(image_path),
            '-t', str(duration),
            '-pix_fmt', 'yuv420p',
            '-r', '30',
            str(output_path),
            '-y'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return str(output_path)
        except subprocess.CalledProcessError as e:
            logger.error(f"创建静态视频失败: {e}")
            raise
    
    def _generate_shot_subtitles(self, shot: Shot, start_time: float, duration: float) -> List[SubtitleSegment]:
        """生成镜头字幕"""
        subtitles = []
        
        # 对话字幕
        if shot.dialogue:
            subtitles.append(SubtitleSegment(
                start_time=start_time,
                end_time=start_time + duration,
                text=shot.dialogue,
                speaker=shot.characters[0] if shot.characters else ""
            ))
        
        # 旁白字幕
        if shot.voice_over:
            subtitles.append(SubtitleSegment(
                start_time=start_time,
                end_time=start_time + duration,
                text=shot.voice_over,
                speaker="旁白"
            ))
        
        return subtitles
    
    async def _generate_subtitles(self, segments: List[VideoSegment], settings: CompositionSettings) -> Optional[Path]:
        """生成字幕文件"""
        if not self.temp_dir:
            return None
        
        subtitle_file = self.temp_dir / "subtitles.srt"
        
        try:
            subtitle_content = []
            subtitle_index = 1
            
            for segment in segments:
                for subtitle in segment.subtitles:
                    # SRT格式
                    start_time = self._format_srt_time(subtitle.start_time)
                    end_time = self._format_srt_time(subtitle.end_time)
                    
                    subtitle_content.append(f"{subtitle_index}")
                    subtitle_content.append(f"{start_time} --> {end_time}")
                    
                    # 添加说话人信息
                    if subtitle.speaker:
                        subtitle_content.append(f"[{subtitle.speaker}] {subtitle.text}")
                    else:
                        subtitle_content.append(subtitle.text)
                    
                    subtitle_content.append("")  # 空行分隔
                    subtitle_index += 1
            
            if subtitle_content:
                with open(subtitle_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(subtitle_content))
                
                return subtitle_file
            
        except Exception as e:
            logger.error(f"生成字幕文件失败: {e}")
        
        return None
    
    def _format_srt_time(self, seconds: float) -> str:
        """格式化SRT时间"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    async def _compose_final_video(self, segments: List[VideoSegment], 
                                 settings: CompositionSettings,
                                 progress_callback: Optional[callable] = None) -> Path:
        """合成最终视频"""
        if not self.temp_dir:
            raise RuntimeError("临时目录未初始化")
        
        # 创建视频列表文件
        video_list_file = self.temp_dir / "video_list.txt"
        audio_files = []
        
        with open(video_list_file, 'w', encoding='utf-8') as f:
            for i, segment in enumerate(segments):
                # 预处理视频片段
                processed_video = await self._preprocess_video_segment(segment, settings)
                f.write(f"file '{processed_video}'\n")
                
                # 收集音频文件
                if segment.audio_path:
                    audio_files.append(segment.audio_path)
                
                if progress_callback:
                    progress = 0.3 + (i / len(segments)) * 0.4
                    progress_callback(progress, f"处理视频片段 {i+1}/{len(segments)}")
        
        # 合并视频
        merged_video = self.temp_dir / "merged_video.mp4"
        
        cmd = [
            self.ffmpeg_path,
            '-f', 'concat',
            '-safe', '0',
            '-i', str(video_list_file),
            '-c', 'copy',
            str(merged_video),
            '-y'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            logger.error(f"视频合并失败: {e}")
            raise
        
        # 如果有音频文件，合并音频
        if audio_files:
            final_video = await self._merge_audio_tracks(merged_video, audio_files, settings)
        else:
            final_video = merged_video
        
        return final_video
    
    async def _preprocess_video_segment(self, segment: VideoSegment, settings: CompositionSettings) -> Path:
        """预处理视频片段"""
        if not self.temp_dir:
            raise RuntimeError("临时目录未初始化")
        
        input_path = Path(segment.video_path)
        output_path = self.temp_dir / f"processed_{segment.shot_id}.mp4"
        
        # 构建FFmpeg命令
        cmd = [
            self.ffmpeg_path,
            '-i', str(input_path),
            '-vf', f'scale={settings.output_width}:{settings.output_height}:force_original_aspect_ratio=decrease,pad={settings.output_width}:{settings.output_height}:(ow-iw)/2:(oh-ih)/2',
            '-r', str(settings.fps),
            '-c:v', settings.video_codec,
            '-b:v', settings.video_bitrate,
            '-preset', settings.quality_preset,
            str(output_path),
            '-y'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return output_path
        except subprocess.CalledProcessError as e:
            logger.error(f"视频预处理失败: {e}")
            raise
    
    async def _merge_audio_tracks(self, video_path: Path, audio_files: List[str], settings: CompositionSettings) -> Path:
        """合并音频轨道"""
        if not self.temp_dir:
            raise RuntimeError("临时目录未初始化")
        
        output_path = self.temp_dir / "video_with_audio.mp4"
        
        # 如果只有一个音频文件，直接合并
        if len(audio_files) == 1:
            cmd = [
                self.ffmpeg_path,
                '-i', str(video_path),
                '-i', audio_files[0],
                '-c:v', 'copy',
                '-c:a', settings.audio_codec,
                '-b:a', settings.audio_bitrate,
                '-shortest',
                str(output_path),
                '-y'
            ]
        else:
            # 多个音频文件需要先合并
            merged_audio = await self._merge_multiple_audio_files(audio_files, settings)
            
            cmd = [
                self.ffmpeg_path,
                '-i', str(video_path),
                '-i', str(merged_audio),
                '-c:v', 'copy',
                '-c:a', settings.audio_codec,
                '-b:a', settings.audio_bitrate,
                '-shortest',
                str(output_path),
                '-y'
            ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return output_path
        except subprocess.CalledProcessError as e:
            logger.error(f"音频合并失败: {e}")
            raise
    
    async def _merge_multiple_audio_files(self, audio_files: List[str], settings: CompositionSettings) -> Path:
        """合并多个音频文件"""
        if not self.temp_dir:
            raise RuntimeError("临时目录未初始化")
        
        output_path = self.temp_dir / "merged_audio.wav"
        
        # 创建音频列表文件
        audio_list_file = self.temp_dir / "audio_list.txt"
        with open(audio_list_file, 'w', encoding='utf-8') as f:
            for audio_file in audio_files:
                f.write(f"file '{audio_file}'\n")
        
        cmd = [
            self.ffmpeg_path,
            '-f', 'concat',
            '-safe', '0',
            '-i', str(audio_list_file),
            '-c:a', 'pcm_s16le',
            '-ar', str(settings.audio_sample_rate),
            str(output_path),
            '-y'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return output_path
        except subprocess.CalledProcessError as e:
            logger.error(f"多音频合并失败: {e}")
            raise
    
    async def _add_subtitles(self, video_path: Path, subtitle_path: Path, settings: CompositionSettings) -> Path:
        """添加字幕"""
        if not self.temp_dir:
            raise RuntimeError("临时目录未初始化")
        
        output_path = self.temp_dir / "video_with_subtitles.mp4"
        
        # 构建字幕样式
        subtitle_style = (
            f"FontName={settings.subtitle_font},"
            f"FontSize={settings.subtitle_size},"
            f"PrimaryColour=&H{self._color_to_hex(settings.subtitle_color)},"
            f"OutlineColour=&H{self._color_to_hex(settings.subtitle_outline_color)},"
            f"Outline={settings.subtitle_outline_width},"
            f"Alignment={self._get_subtitle_alignment(settings.subtitle_position)}"
        )
        
        cmd = [
            self.ffmpeg_path,
            '-i', str(video_path),
            '-vf', f"subtitles={subtitle_path}:force_style='{subtitle_style}'",
            '-c:a', 'copy',
            str(output_path),
            '-y'
        ]
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            return output_path
        except subprocess.CalledProcessError as e:
            logger.error(f"添加字幕失败: {e}")
            raise
    
    def _color_to_hex(self, color: str) -> str:
        """颜色转换为十六进制"""
        color_map = {
            "white": "FFFFFF",
            "black": "000000",
            "red": "FF0000",
            "green": "00FF00",
            "blue": "0000FF",
            "yellow": "FFFF00"
        }
        
        return color_map.get(color.lower(), "FFFFFF")
    
    def _get_subtitle_alignment(self, position: str) -> int:
        """获取字幕对齐方式"""
        alignment_map = {
            "bottom": 2,
            "top": 8,
            "center": 5
        }
        
        return alignment_map.get(position.lower(), 2)
    
    async def _finalize_output(self, video_path: Path, output_path: str, settings: CompositionSettings):
        """输出最终视频"""
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制到最终位置
        shutil.copy2(video_path, output_path)
    
    async def _get_video_duration(self, video_path: str) -> float:
        """获取视频时长"""
        cmd = [
            self.ffmpeg_path,
            '-i', video_path,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            # 从stderr中提取时长信息
            for line in result.stderr.split('\n'):
                if 'Duration:' in line:
                    duration_str = line.split('Duration:')[1].split(',')[0].strip()
                    # 解析时长格式 HH:MM:SS.mmm
                    time_parts = duration_str.split(':')
                    hours = float(time_parts[0])
                    minutes = float(time_parts[1])
                    seconds = float(time_parts[2])
                    return hours * 3600 + minutes * 60 + seconds
        except Exception as e:
            logger.warning(f"获取视频时长失败: {e}")
        
        return 0.0
    
    async def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """获取视频信息"""
        cmd = [
            self.ffmpeg_path,
            '-i', video_path,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            info = {
                "duration": 0.0,
                "width": 0,
                "height": 0,
                "fps": 0.0,
                "bitrate": "",
                "codec": ""
            }
            
            # 解析输出信息
            for line in result.stderr.split('\n'):
                if 'Duration:' in line:
                    duration_str = line.split('Duration:')[1].split(',')[0].strip()
                    time_parts = duration_str.split(':')
                    hours = float(time_parts[0])
                    minutes = float(time_parts[1])
                    seconds = float(time_parts[2])
                    info["duration"] = hours * 3600 + minutes * 60 + seconds
                
                if 'Video:' in line:
                    # 提取分辨率
                    if 'x' in line:
                        resolution = line.split('x')[0].split()[-1]
                        if resolution.isdigit():
                            info["width"] = int(resolution)
                        
                        height_part = line.split('x')[1].split()[0]
                        if height_part.isdigit():
                            info["height"] = int(height_part)
                    
                    # 提取帧率
                    if 'fps' in line:
                        fps_part = line.split('fps')[0].split()[-1]
                        try:
                            info["fps"] = float(fps_part)
                        except ValueError:
                            pass
            
            return info
            
        except Exception as e:
            logger.warning(f"获取视频信息失败: {e}")
            return {}
    
    async def _cleanup(self):
        """清理临时文件"""
        if self.temp_dir and self.temp_dir.exists():
            try:
                shutil.rmtree(self.temp_dir)
                logger.info("临时文件清理完成")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
            finally:
                self.temp_dir = None
