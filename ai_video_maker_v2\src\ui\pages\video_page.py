# -*- coding: utf-8 -*-
"""
视频合成页面 - 完整视频制作和合成

提供完整的视频合成功能：
- 项目素材检查
- 合成参数配置
- 视频合成执行
- 预览和导出
"""

import asyncio
import logging
from pathlib import Path
from typing import Optional
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QProgressBar, QTextEdit, QGroupBox,
                            QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox,
                            QFileDialog, QMessageBox, QScrollArea, QFrame)
from PyQt6.QtGui import QPixmap, QFont

from ..components.card import Card, CardHeader, CardContent, CardActions
from ..components.button import PrimaryButton, SecondaryButton, LoadingButton
from ..components.input import ModernLineEdit, ModernComboBox
from ...core.video_composer import VideoComposer, CompositionSettings
from ...models.project import Project
from ...models.storyboard import Storyboard

logger = logging.getLogger(__name__)

class VideoCompositionThread(QThread):
    """视频合成线程"""

    progress_updated = pyqtSignal(float, str)
    composition_finished = pyqtSignal(dict)

    def __init__(self, project: Project, storyboard: Storyboard,
                 output_path: str, settings: CompositionSettings):
        super().__init__()
        self.project = project
        self.storyboard = storyboard
        self.output_path = output_path
        self.settings = settings
        self.composer = VideoComposer(project)

    def run(self):
        """执行视频合成"""
        try:
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 执行合成
            result = loop.run_until_complete(
                self.composer.compose_video(
                    self.storyboard,
                    self.output_path,
                    self.settings,
                    self._progress_callback
                )
            )

            self.composition_finished.emit(result)

        except Exception as e:
            logger.error(f"视频合成线程异常: {e}")
            self.composition_finished.emit({
                "success": False,
                "error": str(e)
            })
        finally:
            loop.close()

    def _progress_callback(self, progress: float, message: str):
        """进度回调"""
        self.progress_updated.emit(progress, message)

class VideoPage(QWidget):
    """视频合成页面"""

    def __init__(self, app_controller=None, parent=None):
        super().__init__(parent)
        self.app_controller = app_controller
        self.current_project: Optional[Project] = None
        self.current_storyboard: Optional[Storyboard] = None
        self.composition_thread: Optional[VideoCompositionThread] = None

        self._setup_ui()
        self._setup_connections()

        # 定时器用于更新项目状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_project_status)
        self.update_timer.start(1000)  # 每秒更新一次

    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 页面标题
        title_card = Card()
        title_header = CardHeader("视频合成", "将分镜素材合成为完整视频")
        title_card.add_widget(title_header)
        layout.addWidget(title_card)

        # 主要内容区域
        content_layout = QHBoxLayout()

        # 左侧：项目状态和合成设置
        left_panel = self._create_left_panel()
        content_layout.addWidget(left_panel, 1)

        # 右侧：预览和控制
        right_panel = self._create_right_panel()
        content_layout.addWidget(right_panel, 1)

        layout.addLayout(content_layout)

    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)

        # 项目状态卡片
        self.status_card = self._create_status_card()
        layout.addWidget(self.status_card)

        # 合成设置卡片
        self.settings_card = self._create_settings_card()
        layout.addWidget(self.settings_card)

        # 输出设置卡片
        self.output_card = self._create_output_card()
        layout.addWidget(self.output_card)

        layout.addStretch()

        return panel

    def _create_status_card(self) -> Card:
        """创建项目状态卡片"""
        card = Card()
        header = CardHeader("项目状态", "检查项目素材完整性")
        card.add_widget(header)

        content = CardContent()

        # 状态网格
        status_layout = QGridLayout()

        # 分镜状态
        status_layout.addWidget(QLabel("分镜脚本:"), 0, 0)
        self.storyboard_status = QLabel("未加载")
        self.storyboard_status.setStyleSheet("color: #757575;")
        status_layout.addWidget(self.storyboard_status, 0, 1)

        # 图像状态
        status_layout.addWidget(QLabel("图像素材:"), 1, 0)
        self.images_status = QLabel("0/0")
        self.images_status.setStyleSheet("color: #757575;")
        status_layout.addWidget(self.images_status, 1, 1)

        # 音频状态
        status_layout.addWidget(QLabel("音频素材:"), 2, 0)
        self.audio_status = QLabel("0/0")
        self.audio_status.setStyleSheet("color: #757575;")
        status_layout.addWidget(self.audio_status, 2, 1)

        # 视频状态
        status_layout.addWidget(QLabel("视频素材:"), 3, 0)
        self.video_status = QLabel("0/0")
        self.video_status.setStyleSheet("color: #757575;")
        status_layout.addWidget(self.video_status, 3, 1)

        content.add_layout(status_layout)
        card.add_widget(content)

        # 操作按钮
        actions = CardActions()
        self.refresh_btn = SecondaryButton("刷新状态")
        actions.add_button(self.refresh_btn)
        card.add_widget(actions)

        return card

    def _create_settings_card(self) -> Card:
        """创建合成设置卡片"""
        card = Card()
        header = CardHeader("合成设置", "配置视频合成参数")
        card.add_widget(header)

        content = CardContent()

        # 视频设置组
        video_group = QGroupBox("视频设置")
        video_layout = QGridLayout(video_group)

        # 分辨率
        video_layout.addWidget(QLabel("分辨率:"), 0, 0)
        self.resolution_combo = ModernComboBox()
        self.resolution_combo.addItems([
            "1920x1080 (Full HD)",
            "1280x720 (HD)",
            "3840x2160 (4K)",
            "2560x1440 (2K)"
        ])
        video_layout.addWidget(self.resolution_combo, 0, 1)

        # 帧率
        video_layout.addWidget(QLabel("帧率:"), 1, 0)
        self.fps_combo = ModernComboBox()
        self.fps_combo.addItems(["24", "25", "30", "60"])
        self.fps_combo.setCurrentText("30")
        video_layout.addWidget(self.fps_combo, 1, 1)

        # 质量预设
        video_layout.addWidget(QLabel("质量预设:"), 2, 0)
        self.quality_combo = ModernComboBox()
        self.quality_combo.addItems([
            "ultrafast", "fast", "medium", "slow", "veryslow"
        ])
        self.quality_combo.setCurrentText("medium")
        video_layout.addWidget(self.quality_combo, 2, 1)

        content.add_widget(video_group)

        # 音频设置组
        audio_group = QGroupBox("音频设置")
        audio_layout = QGridLayout(audio_group)

        # 音频编码
        audio_layout.addWidget(QLabel("音频编码:"), 0, 0)
        self.audio_codec_combo = ModernComboBox()
        self.audio_codec_combo.addItems(["aac", "mp3", "wav"])
        audio_layout.addWidget(self.audio_codec_combo, 0, 1)

        # 音频比特率
        audio_layout.addWidget(QLabel("音频比特率:"), 1, 0)
        self.audio_bitrate_combo = ModernComboBox()
        self.audio_bitrate_combo.addItems(["128k", "192k", "256k", "320k"])
        self.audio_bitrate_combo.setCurrentText("192k")
        audio_layout.addWidget(self.audio_bitrate_combo, 1, 1)

        content.add_widget(audio_group)

        # 特效设置组
        effects_group = QGroupBox("特效设置")
        effects_layout = QVBoxLayout(effects_group)

        self.enable_transitions_cb = QCheckBox("启用转场效果")
        self.enable_transitions_cb.setChecked(True)
        effects_layout.addWidget(self.enable_transitions_cb)

        self.enable_subtitles_cb = QCheckBox("添加字幕")
        self.enable_subtitles_cb.setChecked(True)
        effects_layout.addWidget(self.enable_subtitles_cb)

        self.enable_audio_crossfade_cb = QCheckBox("音频交叉淡化")
        self.enable_audio_crossfade_cb.setChecked(True)
        effects_layout.addWidget(self.enable_audio_crossfade_cb)

        content.add_widget(effects_group)

        card.add_widget(content)

        return card

    def _create_output_card(self) -> Card:
        """创建输出设置卡片"""
        card = Card()
        header = CardHeader("输出设置", "配置视频输出参数")
        card.add_widget(header)

        content = CardContent()

        # 输出路径
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出路径:"))

        self.output_path_edit = ModernLineEdit()
        self.output_path_edit.setPlaceholderText("选择视频输出路径...")
        output_layout.addWidget(self.output_path_edit)

        self.browse_btn = SecondaryButton("浏览")
        output_layout.addWidget(self.browse_btn)

        content.add_layout(output_layout)

        # 输出格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("输出格式:"))

        self.format_combo = ModernComboBox()
        self.format_combo.addItems(["mp4", "avi", "mov", "mkv"])
        format_layout.addWidget(self.format_combo)

        content.add_layout(format_layout)

        card.add_widget(content)

        return card

    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setSpacing(16)

        # 预览卡片
        self.preview_card = self._create_preview_card()
        layout.addWidget(self.preview_card)

        # 合成控制卡片
        self.control_card = self._create_control_card()
        layout.addWidget(self.control_card)

        # 进度卡片
        self.progress_card = self._create_progress_card()
        layout.addWidget(self.progress_card)

        layout.addStretch()

        return panel

    def _create_preview_card(self) -> Card:
        """创建预览卡片"""
        card = Card()
        header = CardHeader("视频预览", "预览合成效果")
        card.add_widget(header)

        content = CardContent()

        # 预览区域
        self.preview_label = QLabel("暂无预览")
        self.preview_label.setMinimumHeight(200)
        self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #CCCCCC;
                border-radius: 8px;
                background-color: #F8F9FA;
                color: #757575;
                font-size: 16px;
            }
        """)
        content.add_widget(self.preview_label)

        card.add_widget(content)

        # 预览控制
        actions = CardActions()
        self.preview_btn = SecondaryButton("生成预览")
        self.preview_btn.setEnabled(False)
        actions.add_button(self.preview_btn)
        card.add_widget(actions)

        return card

    def _create_control_card(self) -> Card:
        """创建控制卡片"""
        card = Card()
        header = CardHeader("合成控制", "开始视频合成")
        card.add_widget(header)

        content = CardContent()

        # 合成信息
        info_layout = QGridLayout()

        info_layout.addWidget(QLabel("预计时长:"), 0, 0)
        self.duration_label = QLabel("--:--")
        info_layout.addWidget(self.duration_label, 0, 1)

        info_layout.addWidget(QLabel("镜头数量:"), 1, 0)
        self.shots_label = QLabel("0")
        info_layout.addWidget(self.shots_label, 1, 1)

        info_layout.addWidget(QLabel("输出大小:"), 2, 0)
        self.size_label = QLabel("未知")
        info_layout.addWidget(self.size_label, 2, 1)

        content.add_layout(info_layout)

        card.add_widget(content)

        # 控制按钮
        actions = CardActions()
        self.compose_btn = LoadingButton("开始合成")
        self.compose_btn.setEnabled(False)
        actions.add_button(self.compose_btn)

        self.stop_btn = SecondaryButton("停止")
        self.stop_btn.setEnabled(False)
        actions.add_button(self.stop_btn)

        card.add_widget(actions)

        return card

    def _create_progress_card(self) -> Card:
        """创建进度卡片"""
        card = Card()
        header = CardHeader("合成进度", "实时合成状态")
        card.add_widget(header)

        content = CardContent()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        content.add_widget(self.progress_bar)

        # 状态文本
        self.status_label = QLabel("等待开始...")
        self.status_label.setStyleSheet("color: #757575; font-size: 14px;")
        content.add_widget(self.status_label)

        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setVisible(False)
        content.add_widget(self.log_text)

        card.add_widget(content)

        return card

    def _setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.refresh_btn.clicked.connect(self._refresh_status)
        self.browse_btn.clicked.connect(self._browse_output_path)
        self.preview_btn.clicked.connect(self._generate_preview)
        self.compose_btn.clicked.connect(self._start_composition)
        self.stop_btn.clicked.connect(self._stop_composition)

        # 设置变更连接
        self.resolution_combo.currentTextChanged.connect(self._update_settings)
        self.fps_combo.currentTextChanged.connect(self._update_settings)
        self.quality_combo.currentTextChanged.connect(self._update_settings)

    def set_project(self, project: Project):
        """设置当前项目"""
        self.current_project = project
        if project and project.data.storyboard_data:
            from ...models.storyboard import Storyboard
            self.current_storyboard = Storyboard.from_dict(project.data.storyboard_data)
        else:
            self.current_storyboard = None

        self._update_project_status()

    def _update_project_status(self):
        """更新项目状态"""
        if not self.current_project or not self.current_storyboard:
            self._set_status_unavailable()
            return

        # 更新分镜状态
        shots_count = len(self.current_storyboard.shots)
        if shots_count > 0:
            self.storyboard_status.setText(f"✅ {shots_count} 个镜头")
            self.storyboard_status.setStyleSheet("color: #4CAF50;")
        else:
            self.storyboard_status.setText("❌ 无分镜数据")
            self.storyboard_status.setStyleSheet("color: #F44336;")

        # 统计素材状态
        images_ready = 0
        audio_ready = 0
        video_ready = 0

        for shot in self.current_storyboard.shots:
            if shot.generated_image:
                image_path = self.current_project.get_asset_path("images", shot.generated_image)
                if image_path.exists():
                    images_ready += 1

            if shot.generated_audio:
                audio_path = self.current_project.get_asset_path("audio", shot.generated_audio)
                if audio_path.exists():
                    audio_ready += 1

            if shot.generated_video:
                video_path = self.current_project.get_asset_path("video", shot.generated_video)
                if video_path.exists():
                    video_ready += 1

        # 更新状态显示
        self._update_status_label(self.images_status, images_ready, shots_count, "图像")
        self._update_status_label(self.audio_status, audio_ready, shots_count, "音频")
        self._update_status_label(self.video_status, video_ready, shots_count, "视频")

        # 更新合成信息
        total_duration = sum(shot.duration for shot in self.current_storyboard.shots)
        minutes = int(total_duration // 60)
        seconds = int(total_duration % 60)
        self.duration_label.setText(f"{minutes:02d}:{seconds:02d}")
        self.shots_label.setText(str(shots_count))

        # 更新按钮状态
        can_compose = shots_count > 0 and (images_ready > 0 or video_ready > 0)
        self.compose_btn.setEnabled(can_compose and not self._is_composing())
        self.preview_btn.setEnabled(can_compose)

    def _set_status_unavailable(self):
        """设置状态为不可用"""
        self.storyboard_status.setText("未加载项目")
        self.storyboard_status.setStyleSheet("color: #757575;")

        self.images_status.setText("--")
        self.images_status.setStyleSheet("color: #757575;")

        self.audio_status.setText("--")
        self.audio_status.setStyleSheet("color: #757575;")

        self.video_status.setText("--")
        self.video_status.setStyleSheet("color: #757575;")

        self.duration_label.setText("--:--")
        self.shots_label.setText("--")

        self.compose_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)

    def _update_status_label(self, label: QLabel, ready: int, total: int, asset_type: str):
        """更新状态标签"""
        label.setText(f"{ready}/{total}")

        if ready == total and total > 0:
            label.setStyleSheet("color: #4CAF50;")  # 绿色 - 完整
        elif ready > 0:
            label.setStyleSheet("color: #FF9800;")  # 橙色 - 部分
        else:
            label.setStyleSheet("color: #F44336;")  # 红色 - 缺失

    def _refresh_status(self):
        """刷新状态"""
        self._update_project_status()

    def _browse_output_path(self):
        """浏览输出路径"""
        if not self.current_project:
            QMessageBox.warning(self, "警告", "请先加载项目")
            return

        # 默认输出路径
        default_name = f"{self.current_project.metadata.name}_video.mp4"
        default_path = self.current_project.get_asset_path("exports", default_name)

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择视频输出路径",
            str(default_path),
            "视频文件 (*.mp4 *.avi *.mov *.mkv);;所有文件 (*)"
        )

        if file_path:
            self.output_path_edit.setText(file_path)

    def _generate_preview(self):
        """生成预览"""
        # TODO: 实现预览功能
        QMessageBox.information(self, "提示", "预览功能正在开发中...")

    def _start_composition(self):
        """开始视频合成"""
        if not self.current_project or not self.current_storyboard:
            QMessageBox.warning(self, "警告", "请先加载项目和分镜数据")
            return

        output_path = self.output_path_edit.text().strip()
        if not output_path:
            QMessageBox.warning(self, "警告", "请选择输出路径")
            return

        # 创建合成设置
        settings = self._create_composition_settings()

        # 开始合成
        self._start_composition_thread(output_path, settings)

    def _create_composition_settings(self) -> CompositionSettings:
        """创建合成设置"""
        # 解析分辨率
        resolution_text = self.resolution_combo.currentText()
        if "1920x1080" in resolution_text:
            width, height = 1920, 1080
        elif "1280x720" in resolution_text:
            width, height = 1280, 720
        elif "3840x2160" in resolution_text:
            width, height = 3840, 2160
        elif "2560x1440" in resolution_text:
            width, height = 2560, 1440
        else:
            width, height = 1920, 1080

        return CompositionSettings(
            output_width=width,
            output_height=height,
            fps=float(self.fps_combo.currentText()),
            quality_preset=self.quality_combo.currentText(),
            audio_codec=self.audio_codec_combo.currentText(),
            audio_bitrate=self.audio_bitrate_combo.currentText(),
            output_format=self.format_combo.currentText(),
            enable_transitions=self.enable_transitions_cb.isChecked(),
            enable_audio_crossfade=self.enable_audio_crossfade_cb.isChecked()
        )

    def _start_composition_thread(self, output_path: str, settings: CompositionSettings):
        """启动合成线程"""
        # 创建合成线程
        self.composition_thread = VideoCompositionThread(
            self.current_project,
            self.current_storyboard,
            output_path,
            settings
        )

        # 连接信号
        self.composition_thread.progress_updated.connect(self._on_progress_updated)
        self.composition_thread.composition_finished.connect(self._on_composition_finished)

        # 更新UI状态
        self.compose_btn.set_loading(True)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.setVisible(True)
        self.log_text.clear()
        self.status_label.setText("正在准备合成...")

        # 启动线程
        self.composition_thread.start()

        self._log_message("开始视频合成...")

    def _stop_composition(self):
        """停止合成"""
        if self.composition_thread and self.composition_thread.isRunning():
            self.composition_thread.terminate()
            self.composition_thread.wait()

            self._log_message("用户取消了合成操作")
            self._reset_composition_ui()

    def _on_progress_updated(self, progress: float, message: str):
        """进度更新回调"""
        self.progress_bar.setValue(int(progress * 100))
        self.status_label.setText(message)
        self._log_message(f"[{progress:.1%}] {message}")

    def _on_composition_finished(self, result: dict):
        """合成完成回调"""
        self._reset_composition_ui()

        if result.get("success", False):
            self._log_message("✅ 视频合成完成！")
            self.status_label.setText("合成完成")

            # 显示成功消息
            output_path = result.get("output_path", "")
            video_info = result.get("video_info", {})
            duration = video_info.get("duration", 0)

            message = f"视频合成成功！\n\n"
            message += f"输出路径: {output_path}\n"
            message += f"视频时长: {duration:.1f}秒\n"
            message += f"镜头数量: {result.get('segments_count', 0)}"

            reply = QMessageBox.information(
                self, "合成完成", message,
                QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Open
            )

            if reply == QMessageBox.StandardButton.Open:
                self._open_output_folder(output_path)
        else:
            error = result.get("error", "未知错误")
            self._log_message(f"❌ 合成失败: {error}")
            self.status_label.setText("合成失败")

            QMessageBox.critical(
                self, "合成失败",
                f"视频合成失败:\n\n{error}"
            )

    def _reset_composition_ui(self):
        """重置合成UI状态"""
        self.compose_btn.set_loading(False)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)

        # 重新检查按钮状态
        self._update_project_status()

    def _log_message(self, message: str):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def _open_output_folder(self, file_path: str):
        """打开输出文件夹"""
        import subprocess
        import sys

        folder_path = Path(file_path).parent

        try:
            if sys.platform == "win32":
                subprocess.run(["explorer", "/select,", file_path])
            elif sys.platform == "darwin":
                subprocess.run(["open", "-R", file_path])
            else:
                subprocess.run(["xdg-open", str(folder_path)])
        except Exception as e:
            logger.warning(f"无法打开文件夹: {e}")

    def _update_settings(self):
        """更新设置"""
        # 根据分辨率更新预计文件大小
        resolution_text = self.resolution_combo.currentText()
        if "4K" in resolution_text:
            self.size_label.setText("~500-1000MB")
        elif "2K" in resolution_text:
            self.size_label.setText("~200-500MB")
        elif "Full HD" in resolution_text:
            self.size_label.setText("~100-300MB")
        else:
            self.size_label.setText("~50-150MB")

    def _is_composing(self) -> bool:
        """检查是否正在合成"""
        return (self.composition_thread is not None and
                self.composition_thread.isRunning())

    def closeEvent(self, event):
        """关闭事件"""
        if self._is_composing():
            reply = QMessageBox.question(
                self, "确认关闭",
                "视频合成正在进行中，确定要关闭吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self._stop_composition()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()