#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CogVideoX-Flash 集成测试
测试智谱AI视频生成引擎的集成效果
"""

import asyncio
import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.video_engines.video_generation_service import VideoGenerationService, generate_video_simple
from src.models.video_engines.video_engine_base import VideoGenerationConfig
from config.video_generation_config import get_config
from src.utils.logger import logger


async def test_cogvideox_connection():
    """测试CogVideoX-Flash连接"""
    print("🔍 测试CogVideoX-Flash连接...")
    
    try:
        # 获取配置
        config = get_config('development')
        
        # 检查API密钥
        api_key = config['engines']['cogvideox_flash'].get('api_key', '')
        if not api_key:
            print("❌ 未配置CogVideoX-Flash API密钥")
            print("请在 config/video_generation_config.py 中配置 api_key")
            return False
        
        # 创建服务
        service = VideoGenerationService(config)
        
        # 测试连接
        result = await service.test_engine('cogvideox_flash')
        
        if result:
            print("✅ CogVideoX-Flash连接测试成功")
            return True
        else:
            print("❌ CogVideoX-Flash连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False
    finally:
        if 'service' in locals():
            await service.shutdown()


async def test_text_to_video():
    """测试文生视频"""
    print("\n🎬 测试文生视频...")
    
    try:
        # 测试提示词
        prompt = "一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨"
        
        print(f"提示词: {prompt}")
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            result = await generate_video_simple(
                prompt=prompt,
                duration=3.0,
                output_dir=temp_dir,
                api_key=os.getenv('ZHIPU_API_KEY', '')
            )
            
            if result.success:
                print(f"✅ 文生视频成功: {result.video_path}")
                print(f"   时长: {result.duration:.1f}秒")
                print(f"   分辨率: {result.resolution}")
                print(f"   文件大小: {result.file_size / 1024 / 1024:.2f}MB")
                return True
            else:
                print(f"❌ 文生视频失败: {result.error_message}")
                return False
                
    except Exception as e:
        print(f"❌ 文生视频异常: {e}")
        return False


async def test_image_to_video():
    """测试图生视频"""
    print("\n🖼️ 测试图生视频...")
    
    try:
        # 创建测试图像（简单的彩色图像）
        import numpy as np
        from PIL import Image
        
        # 创建一个简单的测试图像
        img_array = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        img = Image.fromarray(img_array)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 保存测试图像
            image_path = os.path.join(temp_dir, "test_image.png")
            img.save(image_path)
            
            print(f"测试图像: {image_path}")
            
            # 生成视频
            prompt = "图像中的内容开始动起来，充满生机"
            
            result = await generate_video_simple(
                prompt=prompt,
                image_path=image_path,
                duration=3.0,
                output_dir=temp_dir,
                api_key=os.getenv('ZHIPU_API_KEY', '')
            )
            
            if result.success:
                print(f"✅ 图生视频成功: {result.video_path}")
                print(f"   时长: {result.duration:.1f}秒")
                print(f"   分辨率: {result.resolution}")
                print(f"   文件大小: {result.file_size / 1024 / 1024:.2f}MB")
                return True
            else:
                print(f"❌ 图生视频失败: {result.error_message}")
                return False
                
    except ImportError:
        print("⚠️ 需要安装 Pillow 和 numpy 来创建测试图像")
        print("pip install Pillow numpy")
        return False
    except Exception as e:
        print(f"❌ 图生视频异常: {e}")
        return False


async def test_engine_info():
    """测试引擎信息获取"""
    print("\n📊 测试引擎信息...")
    
    try:
        config = get_config('development')
        service = VideoGenerationService(config)
        
        # 获取可用引擎
        engines = service.get_available_engines()
        print(f"可用引擎: {engines}")
        
        # 获取CogVideoX引擎信息
        if 'cogvideox_flash' in engines:
            info = service.get_engine_info('cogvideox_flash')
            if info:
                print(f"CogVideoX-Flash 引擎信息:")
                print(f"  名称: {info['name']}")
                print(f"  版本: {info['version']}")
                print(f"  描述: {info['description']}")
                print(f"  免费: {info['is_free']}")
                print(f"  支持图生视频: {info['supports_image_to_video']}")
                print(f"  支持文生视频: {info['supports_text_to_video']}")
                print(f"  最大时长: {info['max_duration']}秒")
                print(f"  支持分辨率: {info['supported_resolutions'][:3]}...")
                print(f"  支持帧率: {info['supported_fps']}")
        
        # 获取统计信息
        stats = service.get_service_statistics()
        print(f"\n服务统计:")
        print(f"  活跃任务: {stats.get('active_tasks', 0)}")
        print(f"  队列大小: {stats.get('queue_size', 0)}")
        print(f"  路由策略: {stats.get('routing_strategy', 'unknown')}")
        
        await service.shutdown()
        return True
        
    except Exception as e:
        print(f"❌ 获取引擎信息异常: {e}")
        return False


async def test_batch_generation():
    """测试批量生成"""
    print("\n📦 测试批量生成...")
    
    try:
        config = get_config('development')
        service = VideoGenerationService(config)
        
        # 创建多个配置
        configs = []
        prompts = [
            "一朵花在微风中摇摆",
            "小鸟在树枝上歌唱",
            "云朵在天空中飘动"
        ]
        
        for prompt in prompts:
            config_obj = VideoGenerationConfig(
                input_prompt=prompt,
                duration=2.0,
                fps=24,
                width=512,
                height=512
            )
            configs.append(config_obj)
        
        print(f"批量生成 {len(configs)} 个视频...")
        
        # 批量生成
        results = await service.batch_generate_videos(
            configs=configs,
            progress_callback=lambda msg: print(f"  {msg}")
        )
        
        success_count = sum(1 for r in results if r.success)
        print(f"✅ 批量生成完成: {success_count}/{len(results)} 成功")
        
        await service.shutdown()
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 批量生成异常: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始CogVideoX-Flash集成测试\n")
    
    # 检查环境变量
    api_key = os.getenv('ZHIPU_API_KEY')
    if not api_key:
        print("⚠️ 未设置环境变量 ZHIPU_API_KEY")
        print("请设置您的智谱AI API密钥:")
        print("export ZHIPU_API_KEY='your-api-key-here'")
        print("\n或者在配置文件中直接配置")
    
    tests = [
        ("连接测试", test_cogvideox_connection),
        ("引擎信息", test_engine_info),
        ("文生视频", test_text_to_video),
        ("图生视频", test_image_to_video),
        ("批量生成", test_batch_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📋 测试结果汇总:")
    print("="*50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n总计: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！CogVideoX-Flash集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")


if __name__ == "__main__":
    asyncio.run(main())
