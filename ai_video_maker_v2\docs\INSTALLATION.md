# 安装指南 📦

本文档将指导您完成AI视频生成器V2.0的安装和配置。

## 系统要求

### 最低要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.9 或更高版本
- **内存**: 8GB RAM
- **存储**: 5GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Windows 11, macOS 12+, Ubuntu 20.04+
- **Python**: 3.11 或更高版本
- **内存**: 16GB RAM
- **存储**: 20GB 可用空间（包含项目文件）
- **GPU**: 支持CUDA的显卡（可选，用于加速处理）
- **网络**: 高速互联网连接

## 安装步骤

### 1. 克隆项目

```bash
git clone https://github.com/your-repo/ai_video_maker_v2.git
cd ai_video_maker_v2
```

### 2. 创建虚拟环境

#### 使用 venv
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

#### 使用 conda
```bash
conda create -n ai_video_maker python=3.11
conda activate ai_video_maker
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置API密钥

1. 复制配置文件模板：
```bash
cp config/config.example.json config/config.json
```

2. 编辑 `config/config.json`，添加您的API密钥：

```json
{
  "llm_apis": {
    "zhipu": {
      "api_key": "your_zhipu_api_key_here"
    }
  },
  "image_apis": {
    "cogview": {
      "api_key": "your_cogview_api_key_here"
    }
  },
  "voice_apis": {
    "azure": {
      "api_key": "your_azure_speech_key_here"
    }
  }
}
```

### 5. 验证安装

运行以下命令验证安装：

```bash
python main.py --check
```

如果看到 "✅ 所有组件检查通过"，说明安装成功。

## API密钥获取

### 智谱AI (推荐)
1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册账号并完成实名认证
3. 创建API密钥
4. 将密钥添加到配置文件的 `llm_apis.zhipu.api_key` 和 `image_apis.cogview.api_key`

### Azure 语音服务
1. 访问 [Azure Portal](https://portal.azure.com/)
2. 创建"语音服务"资源
3. 获取API密钥和区域信息
4. 将密钥添加到配置文件的 `voice_apis.azure.api_key`

### OpenAI (可选)
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 创建API密钥
3. 将密钥添加到配置文件的相应位置

## 启动应用程序

### 图形界面模式
```bash
python main.py
```

### 命令行模式
```bash
python main.py --cli
```

### 开发模式
```bash
python main.py --debug
```

## 故障排除

### 常见问题

#### 1. Python版本错误
```
错误: 需要Python 3.9或更高版本
```
**解决方案**: 升级Python到3.9或更高版本。

#### 2. 依赖包安装失败
```
ERROR: Could not install packages
```
**解决方案**: 
- 确保网络连接正常
- 尝试使用国内镜像源：
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 3. Qt相关错误
```
ImportError: No module named 'PyQt6'
```
**解决方案**: 
- 确保正确安装了PyQt6：
```bash
pip install PyQt6 PyQt6-WebEngine
```

#### 4. API密钥错误
```
认证失败: Invalid API key
```
**解决方案**: 
- 检查API密钥是否正确
- 确认API密钥有足够的配额
- 检查网络连接

#### 5. 内存不足
```
MemoryError: Unable to allocate memory
```
**解决方案**: 
- 关闭其他占用内存的程序
- 在配置文件中降低 `performance.memory_limit_mb`
- 减少 `performance.max_concurrent_tasks`

### 日志文件

应用程序会在以下位置生成日志文件：
- `ai_video_maker.log` - 主日志文件
- `data/logs/` - 详细日志目录

查看日志可以帮助诊断问题：
```bash
tail -f ai_video_maker.log
```

### 重置配置

如果配置文件损坏，可以重置为默认配置：
```bash
rm config/config.json
cp config/config.example.json config/config.json
```

### 清理缓存

如果遇到缓存相关问题，可以清理缓存：
```bash
rm -rf cache/
rm -rf data/cache/
```

## 更新应用程序

### 从Git更新
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

### 备份数据
更新前建议备份重要数据：
```bash
cp -r data/ backup_data_$(date +%Y%m%d)/
cp config/config.json backup_config_$(date +%Y%m%d).json
```

## 卸载

### 完全卸载
```bash
# 停用虚拟环境
deactivate

# 删除虚拟环境
rm -rf venv/

# 删除项目目录
cd ..
rm -rf ai_video_maker_v2/
```

### 保留数据卸载
```bash
# 备份数据
cp -r data/ ~/ai_video_maker_backup/
cp config/config.json ~/ai_video_maker_backup/

# 然后执行完全卸载
```

## 性能优化

### GPU加速
如果您有NVIDIA GPU，可以安装CUDA支持：
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 内存优化
在配置文件中调整内存设置：
```json
{
  "performance": {
    "cache_size_mb": 256,
    "memory_limit_mb": 1024,
    "max_concurrent_tasks": 2
  }
}
```

### 网络优化
如果网络较慢，可以调整超时设置：
```json
{
  "llm_apis": {
    "zhipu": {
      "timeout": 60,
      "max_retries": 5
    }
  }
}
```

## 获取帮助

如果您在安装过程中遇到问题：

1. **查看文档**: 阅读 [用户指南](USER_GUIDE.md) 和 [FAQ](FAQ.md)
2. **检查日志**: 查看日志文件了解详细错误信息
3. **社区支持**: 访问我们的 [GitHub Issues](https://github.com/your-repo/ai_video_maker_v2/issues)
4. **联系我们**: 发送邮件到 <EMAIL>

---

**祝您使用愉快！** 🎉
