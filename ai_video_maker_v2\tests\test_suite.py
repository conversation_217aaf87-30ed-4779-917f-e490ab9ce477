#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试套件 - AI视频生成器V2.0

提供全面的测试功能：
- 单元测试
- 集成测试
- 性能测试
- 压力测试
"""

import asyncio
import logging
import sys
import time
import unittest
from pathlib import Path
from typing import Dict, Any, List
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入测试模块
from src.core.config_manager import ConfigManager
from src.core.event_system import EventSystem
from src.core.state_manager import StateManager
from src.core.service_manager import ServiceManager
from src.core.performance_manager import PerformanceManager
from src.models.project import Project
from src.models.storyboard import Storyboard, Character, Scene, Shot
from src.core.consistency_manager import ConsistencyManager

logger = logging.getLogger(__name__)

class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        self.config_manager = ConfigManager()
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_load_config(self):
        """测试配置加载"""
        # 创建测试配置文件
        config_file = self.temp_dir / "test_config.json"
        config_data = {
            "environment": "test",
            "debug": True,
            "test_value": 123
        }
        
        import json
        with open(config_file, 'w') as f:
            json.dump(config_data, f)
        
        # 加载配置
        config = self.config_manager.load_config(str(config_file))
        
        self.assertIsNotNone(config)
        self.assertEqual(config.environment, "test")
        self.assertTrue(config.debug)
    
    def test_validate_config(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = {
            "environment": "development",
            "debug": True,
            "log_level": "INFO"
        }
        
        result = self.config_manager.validate_config(valid_config)
        self.assertTrue(result)
        
        # 测试无效配置
        invalid_config = {
            "environment": "invalid_env"
        }
        
        result = self.config_manager.validate_config(invalid_config)
        self.assertFalse(result)

class TestEventSystem(unittest.TestCase):
    """事件系统测试"""
    
    def setUp(self):
        self.event_system = EventSystem()
        self.received_events = []
    
    def test_event_subscription(self):
        """测试事件订阅"""
        def handler(event_data):
            self.received_events.append(event_data)
        
        # 订阅事件
        self.event_system.subscribe("test_event", handler)
        
        # 发布事件
        self.event_system.publish("test_event", {"message": "test"})
        
        # 验证事件接收
        self.assertEqual(len(self.received_events), 1)
        self.assertEqual(self.received_events[0]["message"], "test")
    
    def test_event_unsubscription(self):
        """测试事件取消订阅"""
        def handler(event_data):
            self.received_events.append(event_data)
        
        # 订阅并取消订阅
        self.event_system.subscribe("test_event", handler)
        self.event_system.unsubscribe("test_event", handler)
        
        # 发布事件
        self.event_system.publish("test_event", {"message": "test"})
        
        # 验证事件未接收
        self.assertEqual(len(self.received_events), 0)

class TestStateManager(unittest.TestCase):
    """状态管理器测试"""
    
    def setUp(self):
        self.state_manager = StateManager()
    
    def test_state_operations(self):
        """测试状态操作"""
        # 设置状态
        self.state_manager.set_state("test_key", "test_value")
        
        # 获取状态
        value = self.state_manager.get_state("test_key")
        self.assertEqual(value, "test_value")
        
        # 删除状态
        self.state_manager.delete_state("test_key")
        value = self.state_manager.get_state("test_key")
        self.assertIsNone(value)
    
    def test_state_persistence(self):
        """测试状态持久化"""
        # 设置状态
        self.state_manager.set_state("persistent_key", {"data": "value"})
        
        # 保存状态
        state_file = self.temp_dir / "state.json"
        self.state_manager.save_state(str(state_file))
        
        # 创建新的状态管理器并加载状态
        new_state_manager = StateManager()
        new_state_manager.load_state(str(state_file))
        
        # 验证状态
        value = new_state_manager.get_state("persistent_key")
        self.assertEqual(value["data"], "value")

class TestProjectModel(unittest.TestCase):
    """项目模型测试"""
    
    def setUp(self):
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def tearDown(self):
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_project_creation(self):
        """测试项目创建"""
        project = Project.create_new(
            name="测试项目",
            description="这是一个测试项目",
            base_dir=str(self.temp_dir),
            author="测试用户"
        )
        
        self.assertIsNotNone(project)
        self.assertEqual(project.metadata.name, "测试项目")
        self.assertEqual(project.metadata.author, "测试用户")
    
    def test_project_save_load(self):
        """测试项目保存和加载"""
        # 创建项目
        project = Project.create_new(
            name="测试项目",
            description="测试描述",
            base_dir=str(self.temp_dir)
        )
        
        # 保存项目
        success = project.save()
        self.assertTrue(success)
        
        # 加载项目
        project_file = project._project_file
        loaded_project = Project.load_from_file(str(project_file))
        
        self.assertIsNotNone(loaded_project)
        self.assertEqual(loaded_project.metadata.name, "测试项目")
        self.assertEqual(loaded_project.metadata.description, "测试描述")

class TestStoryboardModel(unittest.TestCase):
    """分镜模型测试"""
    
    def test_storyboard_creation(self):
        """测试分镜板创建"""
        storyboard = Storyboard()
        storyboard.metadata.title = "测试分镜"
        storyboard.metadata.style = "电影风格"
        
        self.assertEqual(storyboard.metadata.title, "测试分镜")
        self.assertEqual(storyboard.metadata.style, "电影风格")
    
    def test_character_management(self):
        """测试角色管理"""
        storyboard = Storyboard()
        
        # 添加角色
        character = Character(
            name="主角",
            description="故事主人公",
            appearance="年轻男性"
        )
        storyboard.add_character(character)
        
        self.assertEqual(storyboard.character_count, 1)
        self.assertIn(character.character_id, storyboard.characters)
    
    def test_scene_management(self):
        """测试场景管理"""
        storyboard = Storyboard()
        
        # 添加场景
        scene = Scene(
            title="客厅",
            location="家中客厅",
            time_of_day="下午"
        )
        storyboard.add_scene(scene)
        
        self.assertEqual(storyboard.scene_count, 1)
        self.assertIn(scene.scene_id, storyboard.scenes)
    
    def test_shot_management(self):
        """测试镜头管理"""
        storyboard = Storyboard()
        
        # 添加镜头
        shot = Shot(
            sequence_number=1,
            description="开场镜头",
            duration=3.0
        )
        storyboard.add_shot(shot)
        
        self.assertEqual(storyboard.shot_count, 1)
        self.assertEqual(storyboard.total_duration, 3.0)
    
    def test_storyboard_serialization(self):
        """测试分镜板序列化"""
        storyboard = Storyboard()
        storyboard.metadata.title = "测试分镜"
        
        # 添加角色
        character = Character(name="角色1", description="测试角色")
        storyboard.add_character(character)
        
        # 添加场景
        scene = Scene(title="场景1", location="测试场景")
        storyboard.add_scene(scene)
        
        # 添加镜头
        shot = Shot(sequence_number=1, description="测试镜头")
        storyboard.add_shot(shot)
        
        # 序列化
        storyboard_dict = storyboard.to_dict()
        
        # 反序列化
        restored_storyboard = Storyboard.from_dict(storyboard_dict)
        
        self.assertEqual(restored_storyboard.metadata.title, "测试分镜")
        self.assertEqual(restored_storyboard.character_count, 1)
        self.assertEqual(restored_storyboard.scene_count, 1)
        self.assertEqual(restored_storyboard.shot_count, 1)

class TestConsistencyManager(unittest.TestCase):
    """一致性管理器测试"""
    
    def setUp(self):
        self.consistency_manager = ConsistencyManager()
    
    def test_consistency_analysis(self):
        """测试一致性分析"""
        # 创建测试分镜板
        storyboard = Storyboard()
        
        # 添加角色
        character = Character(
            name="主角",
            appearance="年轻男性，黑发",
            personality="开朗"
        )
        storyboard.add_character(character)
        
        # 添加场景
        scene = Scene(
            title="客厅",
            location="家中客厅",
            lighting="自然光"
        )
        storyboard.add_scene(scene)
        
        # 添加镜头
        shot1 = Shot(
            sequence_number=1,
            scene_id=scene.scene_id,
            characters=[character.character_id],
            visual_description="主角坐在客厅沙发上，年轻男性，黑发",
            duration=3.0
        )
        storyboard.add_shot(shot1)
        
        shot2 = Shot(
            sequence_number=2,
            scene_id=scene.scene_id,
            characters=[character.character_id],
            visual_description="主角站在客厅中央，年轻男性，黑发",
            duration=2.0
        )
        storyboard.add_shot(shot2)
        
        # 执行一致性分析
        report = self.consistency_manager.analyze_consistency(storyboard)
        
        self.assertIsNotNone(report)
        self.assertGreaterEqual(report.overall_score, 0.0)
        self.assertLessEqual(report.overall_score, 1.0)

class TestPerformanceManager(unittest.TestCase):
    """性能管理器测试"""
    
    def setUp(self):
        self.config = {
            "memory_limit_mb": 1024,
            "cache_size_mb": 256,
            "max_concurrent_tasks": 2
        }
        self.performance_manager = PerformanceManager(self.config)
    
    def test_cache_operations(self):
        """测试缓存操作"""
        cache = self.performance_manager.cache_manager
        
        # 设置缓存
        cache.set("test_key", "test_value")
        
        # 获取缓存
        value = cache.get("test_key")
        self.assertEqual(value, "test_value")
        
        # 删除缓存
        cache.delete("test_key")
        value = cache.get("test_key")
        self.assertIsNone(value)
    
    def test_memory_management(self):
        """测试内存管理"""
        memory_manager = self.performance_manager.memory_manager
        
        # 获取内存使用情况
        usage = memory_manager.get_memory_usage()
        
        self.assertIn("used_mb", usage)
        self.assertIn("percent", usage)
        self.assertIn("available_mb", usage)
        
        # 测试垃圾回收
        collected = memory_manager.force_garbage_collection()
        self.assertIsInstance(collected, int)

class AsyncTestCase(unittest.TestCase):
    """异步测试基类"""
    
    def setUp(self):
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        self.loop.close()
    
    def run_async(self, coro):
        """运行异步函数"""
        return self.loop.run_until_complete(coro)

class TestServiceManager(AsyncTestCase):
    """服务管理器测试"""
    
    def setUp(self):
        super().setUp()
        self.config_manager = ConfigManager()
        # 使用示例配置
        config_file = project_root / "config" / "config.example.json"
        self.config = self.config_manager.load_config(str(config_file))
        
        if self.config:
            self.service_manager = ServiceManager(self.config)
        else:
            self.skipTest("无法加载配置文件")
    
    def test_service_initialization(self):
        """测试服务初始化"""
        async def test():
            success = await self.service_manager.initialize()
            # 即使没有API密钥，初始化也应该部分成功
            self.assertIsInstance(success, bool)
            
            # 获取可用服务
            services = self.service_manager.get_available_services()
            self.assertIsInstance(services, dict)
            
            await self.service_manager.shutdown()
        
        self.run_async(test())

def run_performance_tests():
    """运行性能测试"""
    print("🚀 开始性能测试...")
    
    # 测试项目创建性能
    start_time = time.time()
    temp_dir = Path(tempfile.mkdtemp())
    
    try:
        for i in range(10):
            project = Project.create_new(
                name=f"性能测试项目{i}",
                description="性能测试",
                base_dir=str(temp_dir / f"project_{i}")
            )
            project.save()
        
        creation_time = time.time() - start_time
        print(f"✅ 项目创建性能: 10个项目用时 {creation_time:.2f}秒")
        
        # 测试分镜板序列化性能
        start_time = time.time()
        
        storyboard = Storyboard()
        for i in range(100):
            character = Character(name=f"角色{i}", description=f"测试角色{i}")
            storyboard.add_character(character)
            
            scene = Scene(title=f"场景{i}", location=f"测试场景{i}")
            storyboard.add_scene(scene)
            
            shot = Shot(sequence_number=i+1, description=f"测试镜头{i}")
            storyboard.add_shot(shot)
        
        # 序列化
        storyboard_dict = storyboard.to_dict()
        
        # 反序列化
        restored_storyboard = Storyboard.from_dict(storyboard_dict)
        
        serialization_time = time.time() - start_time
        print(f"✅ 分镜板序列化性能: 100个元素用时 {serialization_time:.2f}秒")
        
    finally:
        if temp_dir.exists():
            shutil.rmtree(temp_dir)

def run_all_tests():
    """运行所有测试"""
    print("🧪 AI视频生成器V2.0 - 完整测试套件")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 运行单元测试
    print("📋 运行单元测试...")
    
    test_classes = [
        TestConfigManager,
        TestEventSystem,
        TestStateManager,
        TestProjectModel,
        TestStoryboardModel,
        TestConsistencyManager,
        TestPerformanceManager,
        TestServiceManager
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=0, stream=open('/dev/null', 'w'))
        result = runner.run(suite)
        
        total_tests += result.testsRun
        passed_tests += result.testsRun - len(result.failures) - len(result.errors)
        
        status = "✅" if result.wasSuccessful() else "❌"
        print(f"{status} {test_class.__name__}: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun}")
    
    print(f"\n📊 单元测试结果: {passed_tests}/{total_tests} 通过")
    
    # 运行性能测试
    print("\n⚡ 运行性能测试...")
    run_performance_tests()
    
    # 总结
    print("\n" + "=" * 50)
    success_rate = passed_tests / total_tests if total_tests > 0 else 0
    
    if success_rate >= 0.9:
        print("🎉 测试结果: 优秀！系统质量很高。")
    elif success_rate >= 0.7:
        print("✅ 测试结果: 良好，系统基本可用。")
    else:
        print("⚠️  测试结果: 需要改进，发现多个问题。")
    
    return success_rate >= 0.7

if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 测试异常: {e}")
        sys.exit(1)
